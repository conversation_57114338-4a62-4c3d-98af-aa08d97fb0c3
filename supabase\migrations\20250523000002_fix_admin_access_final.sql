-- تعديل سياسات RLS لضمان وصول الأدمن لكافة البيانات

-- إعادة تعريف سياسة المستخدمين للأدمن
DROP POLICY IF EXISTS "Admin users can access all users" ON "public"."users";
CREATE POLICY "Admin users can access all users"
  ON "public"."users"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR auth.uid() = id OR tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

-- إعادة تعريف سياسة المدارس للأدمن
DROP POLICY IF EXISTS "Admin can access all schools" ON "public"."tenants";
CREATE POLICY "Admin can access all schools"
  ON "public"."tenants"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

-- إعادة تعريف سياسة الحافلات للأدمن
DROP POLICY IF EXISTS "Admin can access all buses" ON "public"."buses";
CREATE POLICY "Admin can access all buses"
  ON "public"."buses"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR driver_id = auth.uid());

-- إعادة تعريف سياسة المسارات للأدمن
DROP POLICY IF EXISTS "Admin can access all routes" ON "public"."routes";
CREATE POLICY "Admin can access all routes"
  ON "public"."routes"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

-- إعادة تعريف سياسة الطلاب للأدمن
DROP POLICY IF EXISTS "Admin can access all students" ON "public"."students";
CREATE POLICY "Admin can access all students"
  ON "public"."students"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR parent_id = auth.uid());

-- إعادة تعريف سياسة الحضور للأدمن
DROP POLICY IF EXISTS "Admin can access all attendance" ON "public"."attendance";
CREATE POLICY "Admin can access all attendance"
  ON "public"."attendance"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR EXISTS (SELECT 1 FROM students s JOIN users u ON s.tenant_id = u.tenant_id WHERE s.id = student_id AND u.id = auth.uid()));

-- إعادة تعريف سياسة الإشعارات للأدمن
DROP POLICY IF EXISTS "Admin can access all notifications" ON "public"."notifications";
CREATE POLICY "Admin can access all notifications"
  ON "public"."notifications"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR user_id = auth.uid() OR tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

-- إعادة تعريف سياسة الصيانة للأدمن
DROP POLICY IF EXISTS "Admin can access all maintenance" ON "public"."bus_maintenance";
CREATE POLICY "Admin can access all maintenance"
  ON "public"."bus_maintenance"
  FOR ALL
  TO authenticated
  USING (auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR EXISTS (SELECT 1 FROM buses b JOIN users u ON b.tenant_id = u.tenant_id WHERE b.id = bus_id AND u.id = auth.uid()));

-- تحديث وظيفة الحصول على بيانات المستخدم الحالي
DROP FUNCTION IF EXISTS get_current_user_profile();
CREATE FUNCTION get_current_user_profile()
RETURNS SETOF users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM public.users
  WHERE id = auth.uid();
END;
$$;

-- تحديث وظيفة الحصول على جميع المستخدمين للأدمن
DROP FUNCTION IF EXISTS get_all_users();
CREATE FUNCTION get_all_users()
RETURNS SETOF users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- إذا كان المستخدم أدمن، أرجع جميع المستخدمين
  IF v_role = 'admin' THEN
    RETURN QUERY
    SELECT * FROM public.users;
  ELSE
    -- وإلا أرجع فقط المستخدمين في نفس المدرسة
    RETURN QUERY
    SELECT u.*
    FROM public.users u
    JOIN public.users u_current ON u_current.id = auth.uid()
    WHERE u.tenant_id = u_current.tenant_id OR u.id = auth.uid();
  END IF;
END;
$$;

-- تحديث وظيفة الحصول على جميع الحافلات للأدمن
DROP FUNCTION IF EXISTS get_all_buses();
CREATE FUNCTION get_all_buses()
RETURNS SETOF buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- إذا كان المستخدم أدمن، أرجع جميع الحافلات
  IF v_role = 'admin' THEN
    RETURN QUERY
    SELECT * FROM public.buses;
  ELSE
    -- وإلا أرجع فقط الحافلات في نفس المدرسة
    RETURN QUERY
    SELECT b.*
    FROM public.buses b
    JOIN public.users u ON u.id = auth.uid()
    WHERE b.tenant_id = u.tenant_id OR b.driver_id = auth.uid();
  END IF;
END;
$$;

-- تحديث وظيفة الحصول على جميع المسارات للأدمن
DROP FUNCTION IF EXISTS get_all_routes();
CREATE FUNCTION get_all_routes()
RETURNS SETOF routes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- إذا كان المستخدم أدمن، أرجع جميع المسارات
  IF v_role = 'admin' THEN
    RETURN QUERY
    SELECT * FROM public.routes;
  ELSE
    -- وإلا أرجع فقط المسارات في نفس المدرسة
    RETURN QUERY
    SELECT r.*
    FROM public.routes r
    JOIN public.users u ON u.id = auth.uid()
    WHERE r.tenant_id = u.tenant_id OR EXISTS (
      SELECT 1 FROM public.buses b WHERE b.id = r.bus_id AND b.driver_id = auth.uid()
    );
  END IF;
END;
$$;

-- تحديث وظيفة الحصول على جميع الطلاب للأدمن
DROP FUNCTION IF EXISTS get_all_students();
CREATE FUNCTION get_all_students()
RETURNS SETOF students
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- إذا كان المستخدم أدمن، أرجع جميع الطلاب
  IF v_role = 'admin' THEN
    RETURN QUERY
    SELECT * FROM public.students;
  ELSE
    -- وإلا أرجع فقط الطلاب في نفس المدرسة أو أطفال المستخدم
    RETURN QUERY
    SELECT s.*
    FROM public.students s
    JOIN public.users u ON u.id = auth.uid()
    WHERE s.tenant_id = u.tenant_id OR s.parent_id = auth.uid();
  END IF;
END;
$$;

-- تحديث وظيفة الحصول على جميع المدارس للأدمن
DROP FUNCTION IF EXISTS get_all_tenants();
CREATE FUNCTION get_all_tenants()
RETURNS SETOF tenants
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- إذا كان المستخدم أدمن، أرجع جميع المدارس
  IF v_role = 'admin' THEN
    RETURN QUERY
    SELECT * FROM public.tenants;
  ELSE
    -- وإلا أرجع فقط المدرسة الخاصة بالمستخدم
    RETURN QUERY
    SELECT t.*
    FROM public.tenants t
    JOIN public.users u ON u.id = auth.uid()
    WHERE t.id = u.tenant_id;
  END IF;
END;
$$;

-- تحديث وظيفة تعيين مدير المدرسة
DROP FUNCTION IF EXISTS assign_school_manager(uuid, uuid);
CREATE FUNCTION assign_school_manager(user_id uuid, tenant_id uuid)
RETURNS SETOF users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
  v_user_exists boolean;
  v_tenant_exists boolean;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role INTO v_role FROM public.users WHERE id = auth.uid();
  
  -- تحقق من وجود المستخدم والمدرسة
  SELECT EXISTS(SELECT 1 FROM public.users WHERE id = user_id) INTO v_user_exists;
  SELECT EXISTS(SELECT 1 FROM public.tenants WHERE id = tenant_id) INTO v_tenant_exists;
  
  -- إذا كان المستخدم أدمن وموجود المستخدم والمدرسة
  IF v_role = 'admin' AND v_user_exists AND v_tenant_exists THEN
    -- تحديث المستخدم ليكون مدير مدرسة
    UPDATE public.users
    SET role = 'school_manager', tenant_id = assign_school_manager.tenant_id, updated_at = now()
    WHERE id = assign_school_manager.user_id;
    
    -- إرجاع المستخدم المحدث
    RETURN QUERY
    SELECT * FROM public.users WHERE id = assign_school_manager.user_id;
  ELSE
    -- إرجاع خطأ
    RAISE EXCEPTION 'غير مصرح بتعيين مدير المدرسة';
  END IF;
END;
$$;

-- تحديث وظيفة تعيين سائق للحافلة
DROP FUNCTION IF EXISTS assign_bus_driver(uuid, uuid);
CREATE FUNCTION assign_bus_driver(bus_id uuid, driver_id uuid)
RETURNS SETOF buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role text;
  v_tenant_id uuid;
  v_bus_exists boolean;
  v_driver_exists boolean;
BEGIN
  -- تحقق من دور المستخدم الحالي
  SELECT role, tenant_id INTO v_role, v_tenant_id FROM public.users WHERE id = auth.uid();
  
  -- تحقق من وجود الحافلة والسائق
  SELECT EXISTS(SELECT 1 FROM public.buses WHERE id = bus_id) INTO v_bus_exists;
  SELECT EXISTS(SELECT 1 FROM public.users WHERE id = driver_id AND role = 'driver') INTO v_driver_exists;
  
  -- إذا كان المستخدم أدمن أو مدير مدرسة وموجود الحافلة والسائق
  IF (v_role = 'admin' OR v_role = 'school_manager') AND v_bus_exists AND (v_driver_exists OR driver_id IS NULL) THEN
    -- تحديث الحافلة بالسائق الجديد
    UPDATE public.buses
    SET driver_id = assign_bus_driver.driver_id, updated_at = now()
    WHERE id = assign_bus_driver.bus_id
    AND (v_role = 'admin' OR tenant_id = v_tenant_id);
    
    -- إرجاع الحافلة المحدثة
    RETURN QUERY
    SELECT * FROM public.buses WHERE id = assign_bus_driver.bus_id;
  ELSE
    -- إرجاع خطأ
    RAISE EXCEPTION 'غير مصرح بتعيين سائق للحافلة';
  END IF;
END;
$$;