import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { tempo } from "tempo-devtools/dist/vite";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tempo()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    include: ["lucide-react", "@supabase/supabase-js"],
    exclude: ["tempo-routes"],
  },
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      css: {
        charset: false,
      },
    },
  },
  server: {
    // @ts-ignore
    allowedHosts: process.env.TEMPO === "true" ? true : undefined,
    hmr: {
      port: process.env.TEMPO === "true" ? 443 : 24678,
      clientPort: process.env.TEMPO === "true" ? 443 : undefined,
      overlay: true,
    },
    watch: {
      usePolling: process.env.TEMPO === "true",
      interval: 1000,
      ignored: ["**/node_modules/**", "**/dist/**"],
    },
    cors: true,
    headers: {
      "Cache-Control": "no-cache",
    },
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          "lucide-icons": ["lucide-react"],
          supabase: ["@supabase/supabase-js"],
          "react-vendor": ["react", "react-dom"],
        },
      },
    },
  },
});
