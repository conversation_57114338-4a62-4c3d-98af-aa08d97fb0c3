-- Fix admin permissions to allow access to all data across all tenants
-- This migration updates RLS policies to allow admin users to access all data

-- Drop existing restrictive policies and create new ones that allow admin access

-- Users table policies
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admin can view all users" ON users;
DROP POLICY IF EXISTS "Admin can update all users" ON users;

CREATE POLICY "Admin can view all users"
ON users FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  id = auth.uid()
);

CREATE POLICY "Admin can update all users"
ON users FOR UPDATE
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  id = auth.uid()
);

CREATE POLICY "Admin can insert users"
ON users FOR INSERT
WITH CHECK (
  auth.jwt() ->> 'role' = 'admin' OR
  auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor')
);

-- Tenants table policies
DROP POLICY IF EXISTS "Tenants are viewable by tenant" ON tenants;
DROP POLICY IF EXISTS "Tenants are manageable by school managers" ON tenants;

CREATE POLICY "Admin can view all tenants"
ON tenants FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  id = (auth.jwt() ->> 'tenant_id')::uuid
);

CREATE POLICY "Admin can manage all tenants"
ON tenants FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' = 'school_manager' AND id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Buses table policies
DROP POLICY IF EXISTS "Buses are viewable by tenant" ON buses;
DROP POLICY IF EXISTS "Buses are manageable by school managers" ON buses;

CREATE POLICY "Admin can view all buses"
ON buses FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

CREATE POLICY "Admin can manage all buses"
ON buses FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Routes table policies
DROP POLICY IF EXISTS "Routes are viewable by tenant" ON routes;
DROP POLICY IF EXISTS "Routes are manageable by school managers" ON routes;

CREATE POLICY "Admin can view all routes"
ON routes FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

CREATE POLICY "Admin can manage all routes"
ON routes FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Students table policies
DROP POLICY IF EXISTS "Students are viewable by tenant" ON students;
DROP POLICY IF EXISTS "Students are manageable by school managers" ON students;

CREATE POLICY "Admin can view all students"
ON students FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid OR
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'parent' 
    AND students.parent_id = users.id
  )
);

CREATE POLICY "Admin can manage all students"
ON students FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Drop the existing function first to avoid return type conflict
DROP FUNCTION IF EXISTS assign_school_manager(uuid, uuid);

-- Update the assign_school_manager function to work with admin permissions
CREATE OR REPLACE FUNCTION assign_school_manager(user_id uuid, tenant_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_user users%ROWTYPE;
  current_user_role text;
BEGIN
  -- Get current user role
  SELECT auth.jwt() ->> 'role' INTO current_user_role;
  
  -- Only admin can assign school managers
  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only admin users can assign school managers';
  END IF;
  
  -- Update the user
  UPDATE users 
  SET 
    tenant_id = assign_school_manager.tenant_id,
    role = 'school_manager',
    updated_at = NOW()
  WHERE id = assign_school_manager.user_id
  RETURNING * INTO updated_user;
  
  IF updated_user.id IS NULL THEN
    RAISE EXCEPTION 'User not found or update failed';
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'user', row_to_json(updated_user)
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$;

-- Attendance table policies
DROP POLICY IF EXISTS "Attendance viewable by tenant" ON attendance;
DROP POLICY IF EXISTS "Attendance manageable by drivers and supervisors" ON attendance;

CREATE POLICY "Admin can view all attendance"
ON attendance FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid OR
  EXISTS (
    SELECT 1 FROM students s
    JOIN users u ON s.parent_id = u.id
    WHERE s.id = attendance.student_id
    AND u.id = auth.uid()
    AND u.role = 'parent'
  )
);

CREATE POLICY "Admin can manage all attendance"
ON attendance FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor', 'driver') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Notifications table policies
DROP POLICY IF EXISTS "Notifications viewable by tenant" ON notifications;
DROP POLICY IF EXISTS "Notifications manageable by school staff" ON notifications;

CREATE POLICY "Admin can view all notifications"
ON notifications FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid OR
  user_id = auth.uid()
);

CREATE POLICY "Admin can manage all notifications"
ON notifications FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Bus maintenance table policies
DROP POLICY IF EXISTS "Bus maintenance viewable by tenant" ON bus_maintenance;
DROP POLICY IF EXISTS "Bus maintenance manageable by school staff" ON bus_maintenance;

CREATE POLICY "Admin can view all bus maintenance"
ON bus_maintenance FOR SELECT
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

CREATE POLICY "Admin can manage all bus maintenance"
ON bus_maintenance FOR ALL
USING (
  auth.jwt() ->> 'role' = 'admin' OR
  (auth.jwt() ->> 'role' IN ('school_manager', 'transport_supervisor') AND tenant_id = (auth.jwt() ->> 'tenant_id')::uuid)
);

-- Reports table policies removed - table does not exist

-- Evaluations table policies removed - table does not exist

-- Complaints table policies removed - table does not exist