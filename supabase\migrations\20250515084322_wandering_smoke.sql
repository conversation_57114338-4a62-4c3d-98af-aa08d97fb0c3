/*
  # Initial Schema Setup for School Bus Management System

  1. New Tables
    - `tenants` (schools)
      - `id` (uuid, primary key)
      - `name` (text)
      - `domain` (text, unique)
      - `logo_url` (text)
      - `address` (text)
      - `contact_number` (text)
      - `is_active` (boolean)
      - `settings` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `users`
      - `id` (uuid, primary key, links to auth.users)
      - `tenant_id` (uuid, foreign key)
      - `role` (enum)
      - `name` (text)
      - `email` (text)
      - `avatar_url` (text)
      - `phone` (text)
      - `is_active` (boolean)
      - `metadata` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `buses`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `plate_number` (text)
      - `capacity` (integer)
      - `driver_id` (uuid, foreign key)
      - `is_active` (boolean)
      - `last_location` (point)
      - `last_updated` (timestamp)
      - `metadata` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `routes`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `name` (text)
      - `bus_id` (uuid, foreign key)
      - `is_active` (boolean)
      - `schedule` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `route_stops`
      - `id` (uuid, primary key)
      - `route_id` (uuid, foreign key)
      - `name` (text)
      - `location` (point)
      - `order` (integer)
      - `arrival_time` (time)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `students`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `name` (text)
      - `grade` (text)
      - `parent_id` (uuid, foreign key)
      - `route_stop_id` (uuid, foreign key)
      - `is_active` (boolean)
      - `photo_url` (text)
      - `metadata` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `attendance`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `student_id` (uuid, foreign key)
      - `bus_id` (uuid, foreign key)
      - `type` (enum: pickup, dropoff)
      - `location` (point)
      - `recorded_at` (timestamp)
      - `recorded_by` (uuid, foreign key)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for tenant isolation
    - Add role-based access policies

  3. Extensions
    - Enable PostGIS for location data
*/

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;

-- Create user role enum
CREATE TYPE user_role AS ENUM (
  'admin',
  'school_manager',
  'supervisor',
  'driver',
  'parent',
  'student'
);

-- Create attendance type enum
CREATE TYPE attendance_type AS ENUM ('pickup', 'dropoff');

-- Create tenants table
CREATE TABLE IF NOT EXISTS tenants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  domain text UNIQUE,
  logo_url text,
  address text,
  contact_number text,
  is_active boolean DEFAULT true,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  role user_role NOT NULL,
  name text NOT NULL,
  email text UNIQUE NOT NULL,
  avatar_url text,
  phone text,
  is_active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create buses table
CREATE TABLE IF NOT EXISTS buses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  plate_number text NOT NULL,
  capacity integer NOT NULL,
  driver_id uuid REFERENCES users(id),
  is_active boolean DEFAULT true,
  last_location geometry(Point, 4326),
  last_updated timestamptz,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(tenant_id, plate_number)
);

-- Create routes table
CREATE TABLE IF NOT EXISTS routes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  name text NOT NULL,
  bus_id uuid REFERENCES buses(id),
  is_active boolean DEFAULT true,
  schedule jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create route_stops table
CREATE TABLE IF NOT EXISTS route_stops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  route_id uuid NOT NULL REFERENCES routes(id),
  name text NOT NULL,
  location geometry(Point, 4326) NOT NULL,
  "order" integer NOT NULL,
  arrival_time time,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create students table
CREATE TABLE IF NOT EXISTS students (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  name text NOT NULL,
  grade text NOT NULL,
  parent_id uuid REFERENCES users(id),
  route_stop_id uuid REFERENCES route_stops(id),
  is_active boolean DEFAULT true,
  photo_url text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create attendance table
CREATE TABLE IF NOT EXISTS attendance (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  student_id uuid NOT NULL REFERENCES students(id),
  bus_id uuid NOT NULL REFERENCES buses(id),
  type attendance_type NOT NULL,
  location geometry(Point, 4326) NOT NULL,
  recorded_at timestamptz NOT NULL DEFAULT now(),
  recorded_by uuid NOT NULL REFERENCES users(id),
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Tenants policies
CREATE POLICY "Tenants are viewable by authenticated users of the same tenant" ON tenants
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM users WHERE tenant_id = tenants.id
    )
  );

CREATE POLICY "Tenants are manageable by admins only" ON tenants
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM users WHERE role = 'admin'
    )
  );

-- Users policies
CREATE POLICY "Users are viewable by users of the same tenant" ON users
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IN (
      SELECT tenant_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own record" ON users
  FOR UPDATE
  TO authenticated
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- Buses policies
CREATE POLICY "Buses are viewable by users of the same tenant" ON buses
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IN (
      SELECT tenant_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Buses are manageable by school managers and supervisors" ON buses
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM users 
      WHERE tenant_id = buses.tenant_id 
      AND role IN ('school_manager', 'supervisor')
    )
  );

-- Routes policies
CREATE POLICY "Routes are viewable by users of the same tenant" ON routes
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IN (
      SELECT tenant_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Routes are manageable by school managers and supervisors" ON routes
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM users 
      WHERE tenant_id = routes.tenant_id 
      AND role IN ('school_manager', 'supervisor')
    )
  );

-- Route stops policies
CREATE POLICY "Route stops are viewable by users of the same tenant" ON route_stops
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM routes 
      WHERE routes.id = route_stops.route_id
      AND routes.tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
      )
    )
  );

-- Students policies
CREATE POLICY "Students are viewable by users of the same tenant" ON students
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IN (
      SELECT tenant_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Students are manageable by school staff" ON students
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM users 
      WHERE tenant_id = students.tenant_id 
      AND role IN ('school_manager', 'supervisor')
    )
  );

-- Attendance policies
CREATE POLICY "Attendance records are viewable by users of the same tenant" ON attendance
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IN (
      SELECT tenant_id FROM users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Attendance can be recorded by drivers and supervisors" ON attendance
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM users 
      WHERE tenant_id = attendance.tenant_id 
      AND role IN ('driver', 'supervisor')
    )
  );

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_id ON attendance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_recorded_at ON attendance(recorded_at);
CREATE INDEX IF NOT EXISTS idx_buses_last_location ON buses USING GIST(last_location);
CREATE INDEX IF NOT EXISTS idx_route_stops_location ON route_stops USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_attendance_location ON attendance USING GIST(location);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at
    BEFORE UPDATE ON tenants
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_buses_updated_at
    BEFORE UPDATE ON buses
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_routes_updated_at
    BEFORE UPDATE ON routes
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_route_stops_updated_at
    BEFORE UPDATE ON route_stops
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();