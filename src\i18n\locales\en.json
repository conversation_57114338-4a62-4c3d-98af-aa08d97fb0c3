{"app": {"name": "SchoolBus", "tagline": "Smart School Bus Management System"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "resetPassword": "Reset Password", "resetPasswordEmailSent": "If an account with that email exists, you will receive password reset instructions.", "signUp": "Create Account", "errors": {"allFieldsRequired": "All fields are required", "invalidCredentials": "Invalid email or password"}}, "nav": {"dashboard": "Dashboard", "schools": "Schools", "buses": "Buses", "routes": "Routes", "students": "Students", "users": "Users", "allUsers": "All Users", "allBuses": "All Buses", "allRoutes": "All Routes", "allStudents": "All Students", "systemReports": "System Reports", "systemNotifications": "System Notifications", "reports": "Reports", "settings": "Settings", "profile": "Profile", "notifications": "Notifications"}, "schools": {"addSchool": "Add School", "editSchool": "Edit School", "name": "School Name", "domain": "Domain", "address": "Address", "contactNumber": "Contact Number", "isActive": "Active", "deleteConfirmation": "Are you sure you want to delete this school? This action cannot be undone.", "saving": "Saving...", "deleting": "Deleting..."}, "dashboard": {"overview": "Overview", "welcome": "Welcome", "activeSchools": "Active Schools", "activeBuses": "Active Buses", "totalStudents": "Total Students", "totalRoutes": "Total Routes", "recentActivity": "Recent Activity", "busesOnRoute": "Buses on Route", "todayAttendance": "Today's Attendance"}, "buses": {"isActive": "Bus is Active", "editBus": "Edit Bus", "busDetails": "Bus Details", "maintenanceRecords": "Maintenance Records", "maintenanceScheduleDescription": "Maintenance schedule for this bus", "scheduleMaintenance": "Schedule Maintenance", "upcomingMaintenance": "Upcoming Maintenance", "overdueMaintenance": "Overdue Maintenance", "completedMaintenance": "Completed Maintenance", "scheduleFirstMaintenance": "Schedule First Maintenance", "basicInformation": "Basic Information", "driverInformation": "Driver Information", "locationInformation": "Location Information", "lastLocation": "Last Location", "lastUpdated": "Last Updated", "manageBuses": "Manage Buses", "addBus": "Add Bus", "plateNumber": "Plate Number", "capacity": "Capacity", "driver": "Driver", "status": "Status", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "tracking": "Tracking", "details": "Details", "edit": "Edit", "delete": "Delete"}, "routes": {"noRouteAssigned": "No route assigned", "contactAdminForRoute": "Please contact admin to assign a route", "allStatus": "All Statuses", "allBuses": "All Buses", "busAssigned": "Bus Assigned", "busUnassigned": "Bus Unassigned", "assignDriver": "Assign Driver", "totalRoutes": "Total Routes", "activeRoutes": "Active Routes", "routesWithBuses": "Routes with Buses", "totalStops": "Total Stops", "routeInformation": "Route Information", "name": "Route Name", "noDriverAssigned": "No driver assigned", "noBusAssigned": "No bus assigned", "assignBus": "Assign Bus", "schedule": "Schedule", "operatingDays": "Operating Days", "noStops": "No stops assigned", "manageRoutes": "Manage Routes", "addRoute": "Add Route", "routeName": "Route Name", "stops": "Stops", "students": "Students", "editRoute": "Edit Route", "viewRoute": "View Route", "addStop": "Add Stop", "stopName": "Stop Name", "arrivalTime": "Arrival Time", "nameRequired": "Route name is required", "selectAtLeastOneDay": "Select at least one operating day", "orderAlreadyExists": "A stop with this order already exists", "locationRequired": "Location is required", "routeCreated": "Route created successfully", "routeUpdated": "Route updated successfully", "saveError": "Failed to save route. Please try again.", "manageRoutesDescription": "View, add, edit, and assign buses or drivers to routes. Manage stops and schedules for each route.", "startTime": "Start Time", "endTime": "End Time"}, "attendance": {"driverInterface": "Driver Interface", "recordStudentAttendance": "Record Student Attendance", "mode": "Attendance Mode", "pickup": "Pickup", "dropoff": "Dropoff", "locationRequired": "Location is required to record attendance", "enableLocationServices": "Please enable location services on your device"}, "tracking": {"trackBusesRealTime": "Track buses in real time", "detailedView": "Detailed View", "liveTracking": "Live Tracking"}, "students": {"status": "Status", "total": "Total Students", "editStudent": "Edit Student", "dailyView": "Daily View", "dashboardView": "Dashboard View", "pickup": "Pickup", "dropoff": "Dropoff", "attendanceDashboard": "Attendance Dashboard", "totalStudents": "Total Students", "averageAttendance": "Average Attendance", "attendanceDashboardDescription": "Overview of student attendance statistics", "searchStudents": "Search Students", "attendanceTrends": "Attendance Trends", "studentAttendance": "Student Attendance", "frequentAbsentees": "Frequent Absentees", "perfectAttendance": "Perfect Attendance", "attendanceRecords": "Attendance Records", "bulkEdit": "Bulk Edit", "attendanceRate": "Attendance Rate", "partial": "Partial Attendance", "manageStudents": "Manage Students", "addStudent": "Add Student", "name": "Name", "grade": "Grade", "parent": "Parent", "school": "School", "route": "Route", "stop": "Stop", "attendance": "Attendance", "present": "Present", "absent": "Absent"}, "users": {"bulkRole": "Bulk Role", "bulkStatus": "Bulk Status", "bulkDelete": "Bulk Delete", "newPassword": "New Password", "phone": "Phone", "manageUsers": "Manage Users", "addUser": "Add User", "name": "Name", "email": "Email", "role": "Role", "school": "School", "status": "Status", "active": "Active", "inactive": "Inactive", "admin": "Admin", "schoolManager": "School Manager", "supervisor": "Transport Supervisor", "driver": "Driver", "parent": "Parent", "student": "Student"}, "profile": {"myBus": "My Bus", "myRoute": "My Route", "studentsOnRoute": "Students on Route", "title": "Profile", "saveChanges": "Save Changes", "personalInfo": "Personal Information", "email": "Email", "phone": "Phone", "security": "Security", "changePassword": "Change Password", "name": "Name"}, "notifications": {"new": "New Notifications", "markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "markAsUnread": "<PERSON> as Unread", "clearAll": "Clear All", "empty": "No notifications", "noUnreadNotifications": "No unread notifications", "notifications": "Notifications", "unread": "Unread", "read": "Read", "selected": "Selected", "selectAll": "Select All", "noNotificationsFound": "No notifications found", "searchNotifications": "Search notifications...", "deleteConfirmation": "Are you sure you want to delete the selected notifications?", "history": "Notification History", "preferences": "Notification Preferences", "preferencesDescription": "Customize how and when you receive notifications", "preferencesSaved": "Notification preferences saved successfully", "preferencesError": "Failed to save notification preferences", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "enablePush": "Enable Push Notifications", "pushPermissionRequired": "Push notification permission is required to receive notifications", "soundSettings": "Sound Settings", "volume": "Volume", "test": "Test", "grouping": "Notification Grouping", "groupingDescription": "Group similar notifications to reduce clutter", "timeWindow": "Time Window", "maxGroupSize": "Max Group Size", "geofence": "Location Alerts", "attendance": "Attendance", "maintenance": "Maintenance", "announcements": "Announcements", "templates": "Notification Templates", "templatesDescription": "Create and manage notification templates for different event types", "createTemplate": "Create Template", "editTemplate": "Edit Template", "templateName": "Template Name", "type": "Type", "title": "Title", "message": "Message", "variables": "Variables", "clickToInsert": "Click to insert into message", "activeTemplate": "Active Template", "deleteTemplateConfirmation": "Are you sure you want to delete this template?", "duplicate": "Duplicate", "noTemplatesFound": "No templates found", "noTemplates": "No templates created yet", "createFirstTemplate": "Create your first notification template to get started", "searchTemplates": "Search templates...", "adminOnly": "Admin Access Required", "adminOnlyDescription": "Only administrators can manage notification templates"}, "settings": {"title": "Settings", "general": "General Settings", "appearance": "Appearance", "language": "Language", "selectLanguage": "Select Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "routeUpdates": "Route Updates", "attendanceAlerts": "Attendance Alerts", "systemAnnouncements": "System Announcements", "notificationTypes": "Notification Types", "saveSettings": "Save Settings", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Failed to save settings", "security": "Security", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication"}, "evaluation": {"title": "Evaluation System", "description": "Manage evaluations and feedback for drivers, routes, and services", "evaluations": "Evaluations", "evaluationsDescription": "View and manage user evaluations and ratings", "reports": "Reports", "reportsDescription": "View satisfaction reports and analytics", "addEvaluation": "Add Evaluation", "evaluateDriver": "Evaluate Driver", "evaluateService": "Evaluate Service", "evaluateRoute": "Evaluate Route", "evaluate": "Evaluate", "evaluating": "Evaluating", "rating": "Rating", "comment": "Comment", "commentPlaceholder": "Share your experience and feedback...", "submit": "Submit Evaluation", "driver": "Driver", "service": "Service", "route": "Route", "generalService": "General Transport Service", "targetType": "Type", "noEvaluations": "No Evaluations Yet", "noEvaluationsDescription": "No evaluations have been submitted yet. Be the first to share your feedback!", "reviews": "reviews", "totalEvaluations": "Total Evaluations", "averageRating": "Average Rating", "basedOnEvaluations": "Based on {{count}} evaluations", "ratingDistribution": "Rating Distribution", "satisfactionReport": "User Satisfaction Report", "satisfactionReportDescription": "Comprehensive analysis of user satisfaction and feedback trends", "satisfactionTrend": "Satisfaction Trend", "ratings": {"poor": "Poor", "fair": "Fair", "good": "Good", "veryGood": "Very Good", "excellent": "Excellent"}, "satisfaction": {"excellent": "Excellent", "good": "Good", "average": "Average", "poor": "Poor", "critical": "Critical"}, "criteria": {"title": "Evaluation Criteria", "punctuality": "Punctuality", "safety": "Safety", "professionalism": "Professionalism", "communication": "Communication", "comfort": "Comfort", "cleanliness": "Cleanliness", "overall": "Overall Experience", "efficiency": "Route Efficiency", "timing": "Timing", "stops": "Stop Locations"}}, "complaints": {"complaints": "<PERSON><PERSON><PERSON><PERSON>", "complaintsDescription": "Submit and track complaints and suggestions", "submitComplaint": "Submit <PERSON><PERSON><PERSON><PERSON>", "submitDriverComplaint": "Submit <PERSON>", "submitServiceComplaint": "Submit Service Complaint", "submitRouteComplaint": "Submit Route Complaint", "submitGeneralComplaint": "Submit General Comp<PERSON>", "regarding": "Regarding", "title": "Title", "titlePlaceholder": "Brief description of the issue", "description": "Description", "descriptionPlaceholder": "Please provide detailed information about your complaint or suggestion...", "submit": "Submit <PERSON><PERSON><PERSON><PERSON>", "guidelines": "Guidelines", "guideline1": "Be specific and provide clear details", "guideline2": "Include date, time, and location if relevant", "guideline3": "<PERSON><PERSON><PERSON> respectful and constructive", "guideline4": "We will respond within 48 hours", "noComplaints": "No Complaints", "noComplaintsDescription": "No complaints have been submitted yet.", "response": "Response", "respond": "Respond", "responsePlaceholder": "Enter your response to this complaint...", "resolve": "Resolve", "inProgress": "Mark In Progress", "totalComplaints": "Total Complaints", "resolutionRate": "Resolution Rate", "status": {"title": "Status", "pending": "Pending", "inProgress": "In Progress", "resolved": "Resolved", "rejected": "Rejected"}, "type": {"title": "Type", "driver": "Driver", "service": "Service", "route": "Route", "general": "General"}}, "reports": {"title": "Reports", "hideFilters": "Hide Filters", "exportCSV": "Export CSV", "attendanceReport": "Attendance Report", "attendanceData": "Attendance Data", "totalStudents": "Total Students", "reportType": "Report Type", "attendance": "Attendance", "utilization": "Utilization", "performance": "Performance", "delays": "Delays", "summary": "Summary", "timeFrame": "Time Frame", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "dateRange": "Date Range", "school": "School", "generateAttendanceReports": "Generate Attendance Reports"}, "common": {"records": "Records", "select": "Select", "selectedCount": "Selected", "bulkActions": "Bulk Actions", "clearSelection": "Clear Selection", "noChange": "No Change", "setActive": "Set Active", "exportPDF": "Export PDF", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "active": "Active", "notes": "Notes (Optional)", "search": "Search", "filter": "Filter", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "back": "Back", "next": "Next", "finish": "Finish", "loading": "Loading", "noData": "No data available", "actions": "Actions", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "all": "All", "seats": "seats", "showing": "Showing", "of": "of", "previous": "Previous", "update": "Update", "create": "Create", "duplicate": "Duplicate", "inactive": "Inactive", "more": "More", "less": "Less", "optional": "Optional", "submitting": "Submitting...", "anonymous": "Anonymous", "startDate": "Start Date", "endDate": "End Date", "exportCSV": "Export CSV"}, "stats": {"today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "total": "Total"}}