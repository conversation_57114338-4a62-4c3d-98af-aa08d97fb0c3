-- Create notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('geofence', 'attendance', 'maintenance', 'announcements')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  variables TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification actions table
CREATE TABLE IF NOT EXISTS notification_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  action_data JSONB DEFAULT '{}',
  result TEXT,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification acknowledgments table
CREATE TABLE IF NOT EXISTS notification_acknowledgments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  acknowledged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE
);

-- Create notification groups table for grouping similar notifications
CREATE TABLE IF NOT EXISTS notification_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_key TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_count INTEGER DEFAULT 1,
  last_notification_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification group members table
CREATE TABLE IF NOT EXISTS notification_group_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID NOT NULL REFERENCES notification_groups(id) ON DELETE CASCADE,
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_templates_tenant_type ON notification_templates(tenant_id, type);
CREATE INDEX IF NOT EXISTS idx_notification_actions_notification ON notification_actions(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_notification ON notification_acknowledgments(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_groups_tenant_key ON notification_groups(tenant_id, group_key);
CREATE INDEX IF NOT EXISTS idx_notification_group_members_group ON notification_group_members(group_id);

-- Add updated_at trigger for notification_templates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_groups_updated_at
    BEFORE UPDATE ON notification_groups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable realtime for new tables
ALTER PUBLICATION supabase_realtime ADD TABLE notification_templates;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_actions;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_acknowledgments;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_groups;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_group_members;

-- Add priority and type columns to notifications table if they don't exist
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high'));
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS type TEXT DEFAULT 'general';
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add trigger for notifications updated_at
CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default notification templates
INSERT INTO notification_templates (name, type, title, message, variables, tenant_id) 
SELECT 
  'Bus Arrival Alert',
  'geofence',
  'Bus {{busNumber}} has arrived',
  'The bus {{busNumber}} has arrived at {{stopName}} for {{studentName}}. Estimated departure time: {{departureTime}}',
  ARRAY['busNumber', 'stopName', 'studentName', 'departureTime'],
  id
FROM tenants
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates 
  WHERE name = 'Bus Arrival Alert' AND tenant_id = tenants.id
);

INSERT INTO notification_templates (name, type, title, message, variables, tenant_id)
SELECT 
  'Attendance Alert',
  'attendance',
  'Attendance Update for {{studentName}}',
  '{{studentName}} has been marked {{status}} for {{attendanceType}} on {{date}}',
  ARRAY['studentName', 'status', 'attendanceType', 'date'],
  id
FROM tenants
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates 
  WHERE name = 'Attendance Alert' AND tenant_id = tenants.id
);

INSERT INTO notification_templates (name, type, title, message, variables, tenant_id)
SELECT 
  'Maintenance Alert',
  'maintenance',
  'Maintenance Required for Bus {{busNumber}}',
  'Bus {{busNumber}} requires {{maintenanceType}} maintenance. Scheduled for {{scheduledDate}}. Priority: {{priority}}',
  ARRAY['busNumber', 'maintenanceType', 'scheduledDate', 'priority'],
  id
FROM tenants
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates 
  WHERE name = 'Maintenance Alert' AND tenant_id = tenants.id
);

INSERT INTO notification_templates (name, type, title, message, variables, tenant_id)
SELECT 
  'General Announcement',
  'announcements',
  '{{title}}',
  '{{message}}',
  ARRAY['title', 'message'],
  id
FROM tenants
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates 
  WHERE name = 'General Announcement' AND tenant_id = tenants.id
);
