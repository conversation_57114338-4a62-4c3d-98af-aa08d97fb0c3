-- Ensure admin users can see all buses across all tenants
-- Update RLS policies for buses table

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view buses in their tenant" ON buses;
DROP POLICY IF EXISTS "Users can insert buses in their tenant" ON buses;
DROP POLICY IF EXISTS "Users can update buses in their tenant" ON buses;
DROP POLICY IF EXISTS "Users can delete buses in their tenant" ON buses;

-- Create new policies that allow admin access
CREATE POLICY "Users can view buses" ON buses FOR SELECT USING (
  -- Admin users can see all buses
  (auth.jwt() ->> 'role' = 'admin') OR
  ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin') OR
  -- Other users can only see buses in their tenant
  (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  (tenant_id = ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'tenant_id')::uuid)
);

CREATE POLICY "Users can insert buses" ON buses FOR INSERT WITH CHECK (
  -- Admin users can insert buses for any tenant
  (auth.jwt() ->> 'role' = 'admin') OR
  ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin') OR
  -- Other users can only insert buses in their tenant
  (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  (tenant_id = ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'tenant_id')::uuid)
);

CREATE POLICY "Users can update buses" ON buses FOR UPDATE USING (
  -- Admin users can update all buses
  (auth.jwt() ->> 'role' = 'admin') OR
  ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin') OR
  -- Other users can only update buses in their tenant
  (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  (tenant_id = ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'tenant_id')::uuid)
);

CREATE POLICY "Users can delete buses" ON buses FOR DELETE USING (
  -- Admin users can delete all buses
  (auth.jwt() ->> 'role' = 'admin') OR
  ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'admin') OR
  -- Other users can only delete buses in their tenant
  (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid) OR
  (tenant_id = ((auth.jwt() ->> 'user_metadata')::jsonb ->> 'tenant_id')::uuid)
);

-- Ensure the buses table has proper indexes for performance
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_driver_id ON buses(driver_id);
CREATE INDEX IF NOT EXISTS idx_buses_is_active ON buses(is_active);

-- Update realtime publication (only if not already added)
DO $
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication_tables 
    WHERE pubname = 'supabase_realtime' AND tablename = 'buses'
  ) THEN
    ALTER PUBLICATION supabase_realtime ADD TABLE buses;
  END IF;
END $;