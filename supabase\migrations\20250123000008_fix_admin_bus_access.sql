-- Fix admin access to buses and other resources
-- This migration ensures admin users can access all data regardless of RLS policies

-- Create RPC function to get all buses with details for admin users
CREATE OR REPLACE FUNCTION get_all_buses_with_details()
RETURNS TABLE (
  id uuid,
  plate_number text,
  capacity integer,
  is_active boolean,
  tenant_id uuid,
  driver_id uuid,
  notes text,
  created_at timestamptz,
  updated_at timestamptz,
  driver jsonb,
  tenant jsonb
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  RETURN QUERY
  SELECT 
    b.id,
    b.plate_number,
    b.capacity,
    b.is_active,
    b.tenant_id,
    b.driver_id,
    b.notes,
    b.created_at,
    b.updated_at,
    CASE 
      WHEN d.id IS NOT NULL THEN
        jsonb_build_object(
          'id', d.id,
          'name', d.name,
          'email', d.email,
          'role', d.role,
          'tenant_id', d.tenant_id,
          'is_active', d.is_active
        )
      ELSE NULL
    END as driver,
    CASE 
      WHEN t.id IS NOT NULL THEN
        jsonb_build_object(
          'id', t.id,
          'name', t.name
        )
      ELSE NULL
    END as tenant
  FROM buses b
  LEFT JOIN users d ON b.driver_id = d.id
  LEFT JOIN tenants t ON b.tenant_id = t.id
  ORDER BY b.created_at DESC;
END;
$$;

-- Create RPC function to get all routes with details for admin users
CREATE OR REPLACE FUNCTION get_all_routes_with_details()
RETURNS TABLE (
  id uuid,
  name text,
  description text,
  is_active boolean,
  tenant_id uuid,
  bus_id uuid,
  created_at timestamptz,
  updated_at timestamptz,
  stops jsonb
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  RETURN QUERY
  SELECT 
    r.id,
    r.name,
    r.description,
    r.is_active,
    r.tenant_id,
    r.bus_id,
    r.created_at,
    r.updated_at,
    COALESCE(
      (
        SELECT jsonb_agg(
          jsonb_build_object(
            'id', rs.id,
            'name', rs.name,
            'latitude', rs.latitude,
            'longitude', rs.longitude,
            'order_index', rs.order_index,
            'estimated_arrival_time', rs.estimated_arrival_time
          ) ORDER BY rs.order_index
        )
        FROM route_stops rs
        WHERE rs.route_id = r.id
      ),
      '[]'::jsonb
    ) as stops
  FROM routes r
  ORDER BY r.created_at DESC;
END;
$$;

-- Update the existing get_all_buses function to be more robust
CREATE OR REPLACE FUNCTION get_all_buses()
RETURNS SETOF buses
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  RETURN QUERY SELECT * FROM buses ORDER BY created_at DESC;
END;
$$;

-- Update the existing get_all_routes function to be more robust
CREATE OR REPLACE FUNCTION get_all_routes()
RETURNS SETOF routes
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  RETURN QUERY SELECT * FROM routes ORDER BY created_at DESC;
END;
$$;

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION get_all_buses_with_details() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_routes_with_details() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_buses() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_routes() TO authenticated;

-- Ensure admin users can access buses table directly as fallback
-- Update RLS policy for buses to allow admin access
DROP POLICY IF EXISTS "Admin can access all buses" ON buses;
CREATE POLICY "Admin can access all buses"
ON buses FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

-- Update RLS policy for routes to allow admin access
DROP POLICY IF EXISTS "Admin can access all routes" ON routes;
CREATE POLICY "Admin can access all routes"
ON routes FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

-- Update RLS policy for tenants to allow admin access
DROP POLICY IF EXISTS "Admin can access all tenants" ON tenants;
CREATE POLICY "Admin can access all tenants"
ON tenants FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

-- Update RLS policy for students to allow admin access
DROP POLICY IF EXISTS "Admin can access all students" ON students;
CREATE POLICY "Admin can access all students"
ON students FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);
