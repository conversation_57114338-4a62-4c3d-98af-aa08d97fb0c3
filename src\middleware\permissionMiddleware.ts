import { User, UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "../lib/rbac";
import {
  ACCESS_CONTROL_MATRIX,
  getAccessControlMatrix,
} from "../lib/permissionMatrix";

/**
 * Middleware محدث للتحقق من الصلاحيات
 */
export class PermissionMiddleware {
  /**
   * التحقق من صلاحية الوصول للصفحة
   */
  static canAccessRoute(user: User | null, route: string): boolean {
    if (!user) return false;

    const userRole = user.role as UserRole;
    const matrix = getAccessControlMatrix(userRole);

    // تعريف الصفحات والصلاحيات المطلوبة
    const routePermissions: Record<string, Permission[]> = {
      "/dashboard": [], // متاح للجميع
      "/dashboard/schools": [Permission.VIEW_ALL_SCHOOLS],
      "/dashboard/users": [
        Permission.VIEW_ALL_USERS,
        Permission.VIEW_TENANT_USERS,
      ],
      "/dashboard/buses": [
        Permission.VIEW_ALL_BUSES,
        Permission.VIEW_TENANT_BUSES,
        Permission.VIEW_ASSIGNED_BUSES,
      ],
      "/dashboard/routes": [
        Permission.VIEW_ALL_ROUTES,
        Permission.VIEW_TENANT_ROUTES,
      ],
      "/dashboard/my-route": [Permission.VIEW_ASSIGNED_ROUTE],
      "/dashboard/students": [
        Permission.VIEW_ALL_STUDENTS,
        Permission.VIEW_TENANT_STUDENTS,
      ],
      "/dashboard/children": [Permission.VIEW_OWN_CHILDREN],
      "/dashboard/attendance": [Permission.MANAGE_ATTENDANCE],
      "/dashboard/driver-attendance": [Permission.MANAGE_ATTENDANCE],
      "/dashboard/tracking": [Permission.TRACK_BUS],
      "/dashboard/reports": [
        Permission.VIEW_SYSTEM_REPORTS,
        Permission.VIEW_TENANT_REPORTS,
      ],
      "/dashboard/notifications": [
        Permission.VIEW_ALL_NOTIFICATIONS,
        Permission.SEND_NOTIFICATIONS,
      ],
      "/dashboard/evaluation": [Permission.VIEW_EVALUATIONS],
      "/dashboard/settings": [], // متاح للجميع
      "/dashboard/profile": [], // متاح للجميع
    };

    const requiredPermissions = routePermissions[route];

    // إذا لم تكن هناك صلاحيات مطلوبة، فالصفحة متاحة
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    // فحص ما إذا كان المستخدم لديه أي من الصلاحيات المطلوبة
    return requiredPermissions.some((permission) =>
      matrix.permissions.includes(permission),
    );
  }

  /**
   * فلترة البيانات حسب صلاحيات المستخدم مع التحقق الأمني الصارم
   */
  static filterDataByPermissions<T extends { tenant_id?: string; id?: string }>(
    user: User | null,
    data: T[],
    resourceType: ResourceType,
    ownerIdField?: keyof T,
    options?: { strictTenantIsolation?: boolean; logAccess?: boolean }
  ): T[] {
    if (!user) {
      console.warn('Data filtering failed: No user provided');
      return [];
    }

    if (!user.is_active) {
      console.warn(`Data filtering failed: User ${user.id} is inactive`);
      return [];
    }

    if (!Array.isArray(data)) {
      console.warn('Data filtering failed: Invalid data format');
      return [];
    }

    const userRole = user.role as UserRole;
    const matrix = getAccessControlMatrix(userRole);
    const strictMode = options?.strictTenantIsolation ?? true;
    const logAccess = options?.logAccess ?? false;

    // الأدمن يرى كل البيانات (مع التسجيل)
    if (userRole === UserRole.ADMIN) {
      if (logAccess) {
        console.log(`Admin ${user.id} accessed ${data.length} ${resourceType} records`);
      }
      return data;
    }

    const startTime = performance.now();
    const filtered = data.filter((item) => {
      // التحقق من صحة العنصر
      if (!item || typeof item !== 'object') {
        return false;
      }

      // البيانات الشخصية
      if (
        matrix.dataScopes.includes(DataScope.PERSONAL) &&
        ownerIdField &&
        item[ownerIdField] === user.id
      ) {
        return true;
      }

      // البيانات على مستوى المدرسة مع التحقق الصارم
      if (matrix.dataScopes.includes(DataScope.TENANT)) {
        if (strictMode) {
          // التحقق الصارم: يجب أن يكون للمستخدم والعنصر نفس tenant_id
          return item.tenant_id && user.tenant_id && item.tenant_id === user.tenant_id;
        } else {
          // التحقق العادي
          return item.tenant_id === user.tenant_id;
        }
      }

      // البيانات المخصصة
      if (matrix.dataScopes.includes(DataScope.ASSIGNED)) {
        return this.isAssignedData(user, item, resourceType);
      }

      // بيانات الأطفال مع التحقق الإضافي
      if (
        matrix.dataScopes.includes(DataScope.CHILDREN) &&
        resourceType === ResourceType.STUDENT
      ) {
        const isChild = (item as any).parent_id === user.id;
        const sameTenant = !strictMode || (item.tenant_id === user.tenant_id);
        return isChild && sameTenant;
      }

      return false;
    });

    const filterTime = performance.now() - startTime;

    // تسجيل عملية الفلترة
    if (logAccess || filterTime > 10) { // تسجيل العمليات البطيئة
      console.log(`Data filter: ${userRole} ${user.id} - ${data.length} -> ${filtered.length} ${resourceType} (${filterTime.toFixed(2)}ms)`);
    }

    // كشف محاولات الوصول المشبوهة
    if (data.length > 100 && filtered.length === 0 && userRole !== UserRole.STUDENT) {
      console.warn(`🚨 Suspicious: ${userRole} ${user.id} got 0 results from ${data.length} ${resourceType} records`);
      this.logSecurityViolation(
        user.id,
        'suspicious_data_access',
        `User got 0 results from large dataset`,
        { resourceType, dataCount: data.length, userRole }
      );
    }

    return filtered;
  }

  /**
   * فحص البيانات المخصصة
   */
  private static isAssignedData<T extends { tenant_id?: string; id?: string }>(
    user: User,
    item: T,
    resourceType: ResourceType,
  ): boolean {
    switch (resourceType) {
      case ResourceType.BUS:
        return (item as any).driver_id === user.id;
      case ResourceType.ROUTE:
        return (item as any).bus?.driver_id === user.id;
      case ResourceType.STUDENT:
        return (item as any).route_stop?.route?.bus?.driver_id === user.id;
      default:
        return false;
    }
  }

  /**
   * التحقق من صلاحية تنفيذ عملية مع التحقق الأمني المحسن
   */
  static canPerformAction(
    user: User | null,
    resource: ResourceType,
    action: Action,
    context?: {
      resourceOwnerId?: string;
      resourceTenantId?: string;
      resourceData?: any;
    },
  ): boolean {
    if (!user) {
      console.warn('Permission check failed: No user provided');
      return false;
    }

    // التحقق من حالة المستخدم
    if (!user.is_active) {
      console.warn(`Permission check failed: User ${user.id} is inactive`);
      return false;
    }

    const userRole = user.role as UserRole;
    const matrix = getAccessControlMatrix(userRole);

    // فحص الصلاحية الأساسية
    const allowedActions = matrix.allowedActions[resource];
    if (!allowedActions || !allowedActions.includes(action)) {
      console.warn(`Permission denied: ${userRole} cannot perform ${action} on ${resource}`);
      return false;
    }

    // فحص القيود مع التسجيل المحسن
    const restrictionCheck = this.checkRestrictions(user, matrix, context);
    
    if (!restrictionCheck) {
      console.warn(`Restriction check failed for ${userRole} on ${resource}:${action}`, {
        userId: user.id,
        tenantId: user.tenant_id,
        resourceTenantId: context?.resourceTenantId,
        resourceOwnerId: context?.resourceOwnerId
      });
    }

    return restrictionCheck;
  }

  /**
   * فحص القيود
   */
  private static checkRestrictions(
    user: User,
    matrix: any,
    context?: {
      resourceOwnerId?: string;
      resourceTenantId?: string;
      resourceData?: any;
    },
  ): boolean {
    if (!context) return true;

    // فحص نطاق المدرسة
    if (matrix.restrictions.tenantScope && context.resourceTenantId) {
      return user.tenant_id === context.resourceTenantId;
    }

    // فحص النطاق الشخصي
    if (matrix.restrictions.personalScope && context.resourceOwnerId) {
      return user.id === context.resourceOwnerId;
    }

    // فحص النطاق المخصص
    if (matrix.restrictions.assignedScope && context.resourceData) {
      return context.resourceData.driver_id === user.id;
    }

    // فحص نطاق الأطفال
    if (matrix.restrictions.childrenScope && context.resourceData) {
      return context.resourceData.parent_id === user.id;
    }

    return true;
  }

  /**
   * إنشاء سياق الصلاحيات
   */
  static createPermissionContext(
    user: User,
    resourceId?: string,
    resourceOwnerId?: string,
    resourceTenantId?: string,
    resourceData?: any,
  ) {
    return {
      userId: user.id,
      userRole: user.role,
      userTenantId: user.tenant_id,
      resourceId,
      resourceOwnerId,
      resourceTenantId,
      resourceData,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * تسجيل محاولة الوصول مع التحليل الأمني
   */
  static logAccessAttempt(
    user: User,
    resource: ResourceType,
    action: Action,
    success: boolean,
    context?: any,
  ) {
    const logEntry = {
      userId: user.id,
      userRole: user.role,
      userTenantId: user.tenant_id,
      resource,
      action,
      success,
      context,
      timestamp: new Date().toISOString(),
      userAgent:
        typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
      ipAddress: typeof window !== "undefined" ? window.location.hostname : "unknown",
      sessionId: this.generateSessionId(),
      severity: success ? 'INFO' : 'WARNING'
    };

    if (success) {
      console.log("✅ Access Granted:", logEntry);
    } else {
      console.warn("❌ Access Denied:", logEntry);
      
      // تحليل أنماط الفشل
      this.analyzeFailurePattern(user, resource, action);
    }

    // في التطبيق الحقيقي، يجب حفظ هذا في قاعدة البيانات
    // await this.saveAccessLog(logEntry);
    
    // إرسال تنبيهات للعمليات الحساسة
    if (this.isSensitiveOperation(resource, action)) {
      this.sendSecurityAlert(logEntry);
    }
  }

  /**
   * تحليل أنماط الفشل للكشف عن الهجمات
   */
  private static failureTracker = new Map<string, { count: number; lastAttempt: number }>();
  
  private static analyzeFailurePattern(user: User, resource: ResourceType, action: Action): void {
    const key = `${user.id}:${resource}:${action}`;
    const now = Date.now();
    const current = this.failureTracker.get(key);
    
    if (!current || now - current.lastAttempt > 300000) { // 5 minutes reset
      this.failureTracker.set(key, { count: 1, lastAttempt: now });
      return;
    }
    
    current.count++;
    current.lastAttempt = now;
    
    // تنبيه عند تكرار المحاولات الفاشلة
    if (current.count >= 5) {
      console.error(`🚨 SECURITY ALERT: User ${user.id} has ${current.count} failed attempts on ${resource}:${action}`);
      this.logSecurityViolation(
        user.id,
        'repeated_access_failures',
        `${current.count} failed attempts on ${resource}:${action}`,
        { resource, action, failureCount: current.count }
      );
    }
  }

  /**
   * فحص ما إذا كانت العملية حساسة
   */
  private static isSensitiveOperation(resource: ResourceType, action: Action): boolean {
    const sensitiveOperations = [
      `${ResourceType.SYSTEM}:${Action.MANAGE}`,
      `${ResourceType.USER}:${Action.DELETE}`,
      `${ResourceType.USER}:${Action.CREATE}`,
      `${ResourceType.SCHOOL}:${Action.DELETE}`,
      `${ResourceType.BUS}:${Action.DELETE}`,
      `${ResourceType.ROUTE}:${Action.DELETE}`,
      `${ResourceType.REPORT}:${Action.EXPORT}`,
    ];
    
    return sensitiveOperations.includes(`${resource}:${action}`);
  }

  /**
   * إرسال تنبيه أمني
   */
  private static sendSecurityAlert(logEntry: any): void {
    console.warn('🔔 Security Alert Triggered:', {
      operation: `${logEntry.resource}:${logEntry.action}`,
      user: logEntry.userId,
      role: logEntry.userRole,
      success: logEntry.success,
      timestamp: logEntry.timestamp
    });
    
    // في التطبيق الحقيقي، يجب إرسال تنبيه فعلي
    // await this.sendNotificationToSecurityTeam(logEntry);
  }

  /**
   * توليد معرف الجلسة
   */
  private static generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * التحقق من صحة المستخدم
   */
  static validateUser(user: any): user is User {
    return (
      user &&
      typeof user.id === "string" &&
      typeof user.role === "string" &&
      Object.values(UserRole).includes(user.role as UserRole)
    );
  }

  /**
   * الحصول على رسالة خطأ مناسبة
   */
  static getAccessDeniedMessage(
    user: User | null,
    resource: ResourceType,
    action: Action,
  ): string {
    if (!user) {
      return "يجب تسجيل الدخول للوصول لهذا المورد";
    }

    const roleNames: Record<UserRole, string> = {
      [UserRole.ADMIN]: "مدير النظام",
      [UserRole.SCHOOL_MANAGER]: "مدير المدرسة",
      [UserRole.SUPERVISOR]: "المشرف",
      [UserRole.DRIVER]: "السائق",
      [UserRole.PARENT]: "ولي الأمر",
      [UserRole.STUDENT]: "الطالب",
    };

    const resourceNames: Record<ResourceType, string> = {
      [ResourceType.SYSTEM]: "النظام",
      [ResourceType.SCHOOL]: "المدرسة",
      [ResourceType.USER]: "المستخدم",
      [ResourceType.BUS]: "الحافلة",
      [ResourceType.ROUTE]: "المسار",
      [ResourceType.STUDENT]: "الطالب",
      [ResourceType.ATTENDANCE]: "الحضور",
      [ResourceType.NOTIFICATION]: "الإشعار",
      [ResourceType.REPORT]: "التقرير",
      [ResourceType.MAINTENANCE]: "الصيانة",
      [ResourceType.EVALUATION]: "التقييم",
    };

    const actionNames: Record<Action, string> = {
      [Action.CREATE]: "إنشاء",
      [Action.READ]: "عرض",
      [Action.UPDATE]: "تعديل",
      [Action.DELETE]: "حذف",
      [Action.MANAGE]: "إدارة",
      [Action.ASSIGN]: "تخصيص",
      [Action.TRACK]: "تتبع",
      [Action.EXPORT]: "تصدير",
      [Action.SCHEDULE]: "جدولة",
      [Action.ANALYZE]: "تحليل",
    };

    const roleName = roleNames[user.role as UserRole] || "المستخدم";
    const resourceName = resourceNames[resource] || resource;
    const actionName = actionNames[action] || action;

    return `عذراً، ${roleName} لا يملك صلاحية ${actionName} ${resourceName}. يرجى التواصل مع مدير النظام.`;
  }
}

  /**
   * فحص حدود المعدل للعمليات
   */
  static checkRateLimit(
    userId: string,
    action: string,
    limit: number = 10,
    windowMinutes: number = 1
  ): { allowed: boolean; error?: string; remaining?: number } {
    const key = `${userId}:${action}`;
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;
    
    if (!this.rateLimitStore) {
      this.rateLimitStore = new Map();
    }
    
    const current = this.rateLimitStore.get(key);
    
    if (!current || now - current.resetTime > windowMs) {
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now,
        windowMs
      });
      return { allowed: true, remaining: limit - 1 };
    }
    
    if (current.count >= limit) {
      return {
        allowed: false,
        error: `Rate limit exceeded. Maximum ${limit} requests per ${windowMinutes} minute(s)`,
        remaining: 0
      };
    }
    
    current.count++;
    return { allowed: true, remaining: limit - current.count };
  }

  private static rateLimitStore = new Map<string, { count: number; resetTime: number; windowMs: number }>();

  /**
   * فحص صلاحية تعديل المستخدم
   */
  static checkUserEditPermission(
    editor: User,
    target: User
  ): { allowed: boolean; error?: string } {
    // المستخدم يمكنه تعديل بياناته الشخصية
    if (editor.id === target.id) {
      return { allowed: true };
    }

    // التحقق من حالة المحرر
    if (!editor.is_active) {
      return {
        allowed: false,
        error: 'Your account has been deactivated'
      };
    }

    // التحقق من التسلسل الهرمي
    const editorRole = editor.role as UserRole;
    const targetRole = target.role as UserRole;
    
    const canManage = RBACManager.canManageRole(editorRole, targetRole);
    if (!canManage) {
      return {
        allowed: false,
        error: `${editorRole} cannot manage ${targetRole} users`
      };
    }

    // التحقق من نطاق المدرسة
    if (editorRole !== UserRole.ADMIN) {
      if (editor.tenant_id !== target.tenant_id) {
        return {
          allowed: false,
          error: 'Cannot edit users from different tenant'
        };
      }
    }

    return { allowed: true };
  }

  /**
   * فحص الوصول للمورد مع التحقق الشامل
   */
  static checkResourceAccess(
    user: User | null,
    resource: ResourceType,
    action: Action,
    context?: any
  ): { allowed: boolean; error?: string; details?: any } {
    if (!user) {
      return {
        allowed: false,
        error: 'Authentication required'
      };
    }

    if (!user.is_active) {
      return {
        allowed: false,
        error: 'Account has been deactivated'
      };
    }

    // فحص حدود المعدل
    const rateLimit = this.checkRateLimit(user.id, `${resource}:${action}`);
    if (!rateLimit.allowed) {
      return {
        allowed: false,
        error: rateLimit.error
      };
    }

    // فحص الصلاحيات
    const hasPermission = this.canPerformAction(user, resource, action, context);
    if (!hasPermission) {
      this.logAccessAttempt(user, resource, action, false, context);
      return {
        allowed: false,
        error: this.getAccessDeniedMessage(user, resource, action),
        details: {
          userRole: user.role,
          resource,
          action,
          context
        }
      };
    }

    this.logAccessAttempt(user, resource, action, true, context);
    return { allowed: true };
  }

  /**
   * تسجيل انتهاك أمني
   */
  static logSecurityViolation(
    userId: string,
    violationType: string,
    description: string,
    metadata?: any
  ): void {
    const violation = {
      userId,
      violationType,
      description,
      metadata,
      timestamp: new Date().toISOString(),
      severity: 'HIGH',
      ipAddress: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    };

    console.error('🚨 Security Violation:', violation);
    
    // في التطبيق الحقيقي، يجب حفظ هذا في قاعدة البيانات وإرسال تنبيهات
    // await this.saveSecurityViolation(violation);
    // await this.notifySecurityTeam(violation);
  }
}

export default PermissionMiddleware;
