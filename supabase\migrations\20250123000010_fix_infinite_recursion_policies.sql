-- Fix infinite recursion in RLS policies
-- This migration addresses the circular dependency issues

-- First, drop all existing problematic policies
DROP POLICY IF EXISTS "admin_all_users" ON users;
DROP POLICY IF EXISTS "school_manager_tenant_users" ON users;
DROP POLICY IF EXISTS "users_own_profile" ON users;
DROP POLICY IF EXISTS "tenant_users" ON users;
DROP POLICY IF EXISTS "own_profile" ON users;
DROP POLICY IF EXISTS "admin_users" ON users;

DROP POLICY IF EXISTS "admin_all_tenants" ON tenants;
DROP POLICY IF EXISTS "users_own_tenant" ON tenants;
DROP POLICY IF EXISTS "tenant_access" ON tenants;
DROP POLICY IF EXISTS "admin_tenants" ON tenants;

DROP POLICY IF EXISTS "admin_all_buses" ON buses;
DROP POLICY IF EXISTS "school_manager_tenant_buses" ON buses;
DROP POLICY IF EXISTS "driver_assigned_buses" ON buses;
DROP POLICY IF EXISTS "tenant_buses" ON buses;
DROP POLICY IF EXISTS "admin_buses" ON buses;

DROP POLICY IF EXISTS "admin_all_routes" ON routes;
DROP POLICY IF EXISTS "tenant_routes" ON routes;
DROP POLICY IF EXISTS "admin_routes" ON routes;

DROP POLICY IF EXISTS "admin_all_students" ON students;
DROP POLICY IF EXISTS "school_staff_tenant_students" ON students;
DROP POLICY IF EXISTS "parent_children" ON students;
DROP POLICY IF EXISTS "student_own_record" ON students;
DROP POLICY IF EXISTS "tenant_students" ON students;
DROP POLICY IF EXISTS "admin_students" ON students;

DROP POLICY IF EXISTS "admin_all_attendance" ON attendance;
DROP POLICY IF EXISTS "school_staff_tenant_attendance" ON attendance;
DROP POLICY IF EXISTS "driver_bus_attendance" ON attendance;
DROP POLICY IF EXISTS "parent_children_attendance" ON attendance;
DROP POLICY IF EXISTS "tenant_attendance" ON attendance;
DROP POLICY IF EXISTS "admin_attendance" ON attendance;

DROP POLICY IF EXISTS "admin_all_notifications" ON notifications;
DROP POLICY IF EXISTS "tenant_notifications" ON notifications;
DROP POLICY IF EXISTS "admin_notifications" ON notifications;

DROP POLICY IF EXISTS "admin_route_stops" ON route_stops;
DROP POLICY IF EXISTS "tenant_route_stops" ON route_stops;

-- Create simplified, non-recursive policies

-- Users table policies (avoid recursion by not joining with users table)
CREATE POLICY "users_admin_access" ON users
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "users_own_profile_access" ON users
  FOR ALL USING (
    id = auth.uid()
  );

CREATE POLICY "users_school_manager_access" ON users
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'school_manager' AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

CREATE POLICY "users_supervisor_read" ON users
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'supervisor' AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

-- Tenants table policies
CREATE POLICY "tenants_admin_access" ON tenants
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "tenants_own_access" ON tenants
  FOR SELECT USING (
    id = (auth.jwt() ->> 'tenant_id')::uuid
  );

-- Buses table policies
CREATE POLICY "buses_admin_access" ON buses
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "buses_tenant_access" ON buses
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

CREATE POLICY "buses_driver_access" ON buses
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'driver' AND
    driver_id = auth.uid()
  );

CREATE POLICY "buses_parent_student_read" ON buses
  FOR SELECT USING (
    (auth.jwt() ->> 'role') IN ('parent', 'student')
  );

-- Routes table policies
CREATE POLICY "routes_admin_access" ON routes
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "routes_tenant_access" ON routes
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

CREATE POLICY "routes_driver_read" ON routes
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'driver' AND
    bus_id IN (
      SELECT id FROM buses WHERE driver_id = auth.uid()
    )
  );

CREATE POLICY "routes_parent_student_read" ON routes
  FOR SELECT USING (
    (auth.jwt() ->> 'role') IN ('parent', 'student')
  );

-- Route stops table policies
CREATE POLICY "route_stops_admin_access" ON route_stops
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "route_stops_tenant_access" ON route_stops
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    route_id IN (
      SELECT id FROM routes WHERE tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
    )
  );

CREATE POLICY "route_stops_public_read" ON route_stops
  FOR SELECT USING (
    (auth.jwt() ->> 'role') IN ('driver', 'parent', 'student')
  );

-- Students table policies
CREATE POLICY "students_admin_access" ON students
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "students_tenant_access" ON students
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

CREATE POLICY "students_parent_access" ON students
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'parent' AND
    parent_id = auth.uid()
  );

CREATE POLICY "students_own_access" ON students
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'student' AND
    id = auth.uid()
  );

CREATE POLICY "students_driver_read" ON students
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'driver'
  );

-- Attendance table policies
CREATE POLICY "attendance_admin_access" ON attendance
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "attendance_tenant_access" ON attendance
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

CREATE POLICY "attendance_driver_access" ON attendance
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'driver' AND
    bus_id IN (
      SELECT id FROM buses WHERE driver_id = auth.uid()
    )
  );

CREATE POLICY "attendance_parent_read" ON attendance
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'parent' AND
    student_id IN (
      SELECT id FROM students WHERE parent_id = auth.uid()
    )
  );

CREATE POLICY "attendance_student_read" ON attendance
  FOR SELECT USING (
    (auth.jwt() ->> 'role') = 'student' AND
    student_id = auth.uid()
  );

-- Notifications table policies
CREATE POLICY "notifications_admin_access" ON notifications
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "notifications_tenant_access" ON notifications
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid OR tenant_id IS NULL)
  );

CREATE POLICY "notifications_user_access" ON notifications
  FOR SELECT USING (
    user_id = auth.uid()
  );

-- Bus maintenance policies
CREATE POLICY "bus_maintenance_admin_access" ON bus_maintenance
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "bus_maintenance_tenant_access" ON bus_maintenance
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    bus_id IN (
      SELECT id FROM buses WHERE tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
    )
  );

CREATE POLICY "bus_maintenance_driver_access" ON bus_maintenance
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'driver' AND
    bus_id IN (
      SELECT id FROM buses WHERE driver_id = auth.uid()
    )
  );

-- Evaluations policies (only if table exists)
DO $
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'evaluations') THEN
    EXECUTE 'CREATE POLICY "evaluations_admin_access" ON evaluations
      FOR ALL USING (
        (auth.jwt() ->> ''role'') = ''admin''
      )';
    
    EXECUTE 'CREATE POLICY "evaluations_tenant_access" ON evaluations
      FOR ALL USING (
        (auth.jwt() ->> ''role'') IN (''school_manager'', ''supervisor'') AND
        tenant_id = (auth.jwt() ->> ''tenant_id'')::uuid
      )';
    
    EXECUTE 'CREATE POLICY "evaluations_user_access" ON evaluations
      FOR ALL USING (
        user_id = auth.uid()
      )';
  END IF;
END
$;

-- Complaints policies (only if table exists)
DO $
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'complaints') THEN
    EXECUTE 'CREATE POLICY "complaints_admin_access" ON complaints
      FOR ALL USING (
        (auth.jwt() ->> ''role'') = ''admin''
      )';
    
    EXECUTE 'CREATE POLICY "complaints_tenant_access" ON complaints
      FOR ALL USING (
        (auth.jwt() ->> ''role'') IN (''school_manager'', ''supervisor'') AND
        tenant_id = (auth.jwt() ->> ''tenant_id'')::uuid
      )';
    
    EXECUTE 'CREATE POLICY "complaints_user_access" ON complaints
      FOR ALL USING (
        user_id = auth.uid()
      )';
  END IF;
END
$;

-- Push subscriptions policies
CREATE POLICY "push_subscriptions_admin_access" ON push_subscriptions
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "push_subscriptions_own_access" ON push_subscriptions
  FOR ALL USING (
    user_id = auth.uid()
  );

-- Notification templates policies
CREATE POLICY "notification_templates_admin_access" ON notification_templates
  FOR ALL USING (
    (auth.jwt() ->> 'role') = 'admin'
  );

CREATE POLICY "notification_templates_tenant_access" ON notification_templates
  FOR ALL USING (
    (auth.jwt() ->> 'role') IN ('school_manager', 'supervisor') AND
    (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid OR tenant_id IS NULL)
  );

CREATE POLICY "notification_templates_read_access" ON notification_templates
  FOR SELECT USING (
    (auth.jwt() ->> 'role') IN ('driver', 'parent', 'student')
  );

COMMIT;
