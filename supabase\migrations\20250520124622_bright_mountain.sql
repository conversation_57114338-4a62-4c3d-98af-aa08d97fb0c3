/*
  # Fix users policies with proper UUID casting

  1. Changes
    - Add proper type casting for UUID comparisons
    - Drop existing policies and recreate them with fixed type casting
    - Maintain same security rules but with correct data types

  2. Security
    - Maintain RLS enabled on users table
    - Keep all existing access patterns:
      - Users can view/update their own records
      - Users can view others in same tenant
      - <PERSON><PERSON> can view all users
      - Public signup allowed
*/

-- Drop existing policies to recreate them
DROP POLICY IF EXISTS "Users can view users in same tenant" ON users;
DROP POLICY IF EXISTS "Users can view own record" ON users;
DROP POLICY IF EXISTS "Users can update own record" ON users;
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Allow signup" ON users;

-- Create new policies with proper type casting
CREATE POLICY "Users can view users in same tenant"
ON users
FOR SELECT
TO authenticated
USING (
  tenant_id IS NOT NULL AND 
  tenant_id = (auth.jwt()->>'tenant_id')::uuid
);

CREATE POLICY "Users can view own record"
ON users
FOR SELECT
TO authenticated
USING (
  id = auth.uid()
);

CREATE POLICY "Users can update own record"
ON users
FOR UPDATE
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

CREATE POLICY "Admins can view all users"
ON users
FOR SELECT
TO authenticated
USING (
  auth.jwt()->>'role' = 'admin'
);

CREATE POLICY "Allow signup"
ON users
FOR INSERT
TO public
WITH CHECK (true);