-- Enhance bus_maintenance table with additional fields
ALTER TABLE bus_maintenance
ADD COLUMN IF NOT EXISTS technician_name TEXT,
ADD COLUMN IF NOT EXISTS next_maintenance_date DATE,
ADD COLUMN IF NOT EXISTS completed_date DATE;

-- Create function to update spare part quantity
CREATE OR REPLACE FUNCTION update_spare_part_quantity(p_part_id UUID, p_quantity_change INT)
RETURNS VOID AS $$
BEGIN
  UPDATE spare_parts
  SET quantity = quantity + p_quantity_change,
      updated_at = NOW()
  WHERE id = p_part_id;
  
  -- Create notification if stock is low
  IF (SELECT quantity <= min_quantity FROM spare_parts WHERE id = p_part_id) THEN
    INSERT INTO notifications (user_id, tenant_id, title, message, type, priority, read)
    SELECT 
      u.id, 
      sp.tenant_id, 
      'Low Stock Alert', 
      'Part ' || sp.name || ' is running low on stock. Current quantity: ' || sp.quantity, 
      'inventory', 
      'high', 
      false
    FROM spare_parts sp
    CROSS JOIN users u
    WHERE sp.id = p_part_id
    AND u.tenant_id = sp.tenant_id
    AND u.role IN ('supervisor', 'school_manager');
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate maintenance report
CREATE OR REPLACE FUNCTION generate_maintenance_report(
  p_tenant_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS TABLE (
  bus_id UUID,
  plate_number TEXT,
  total_maintenance_count INT,
  completed_count INT,
  pending_count INT,
  overdue_count INT,
  total_cost DECIMAL,
  avg_completion_days DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id AS bus_id,
    b.plate_number,
    COUNT(bm.id) AS total_maintenance_count,
    COUNT(bm.id) FILTER (WHERE bm.status = 'completed') AS completed_count,
    COUNT(bm.id) FILTER (WHERE bm.status IN ('scheduled', 'in_progress')) AS pending_count,
    COUNT(bm.id) FILTER (WHERE bm.status = 'overdue') AS overdue_count,
    COALESCE(SUM(bm.cost), 0) AS total_cost,
    COALESCE(AVG(EXTRACT(DAY FROM (bm.completed_date - bm.scheduled_date))) FILTER (WHERE bm.status = 'completed'), 0) AS avg_completion_days
  FROM buses b
  LEFT JOIN bus_maintenance bm ON b.id = bm.bus_id
    AND bm.scheduled_date BETWEEN p_start_date AND p_end_date
  WHERE b.tenant_id = p_tenant_id
  GROUP BY b.id, b.plate_number
  ORDER BY b.plate_number;
END;
$$ LANGUAGE plpgsql;

-- Create function to get fleet status report
CREATE OR REPLACE FUNCTION get_fleet_status_report(p_tenant_id UUID)
RETURNS TABLE (
  total_buses INT,
  active_buses INT,
  inactive_buses INT,
  buses_needing_maintenance INT,
  buses_under_maintenance INT,
  buses_overdue_maintenance INT,
  total_maintenance_cost_ytd DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(b.id) AS total_buses,
    COUNT(b.id) FILTER (WHERE b.is_active = true) AS active_buses,
    COUNT(b.id) FILTER (WHERE b.is_active = false) AS inactive_buses,
    COUNT(DISTINCT bm_scheduled.bus_id) AS buses_needing_maintenance,
    COUNT(DISTINCT bm_progress.bus_id) AS buses_under_maintenance,
    COUNT(DISTINCT bm_overdue.bus_id) AS buses_overdue_maintenance,
    COALESCE(SUM(bm_ytd.cost) FILTER (WHERE bm_ytd.status = 'completed'), 0) AS total_maintenance_cost_ytd
  FROM buses b
  LEFT JOIN bus_maintenance bm_scheduled ON b.id = bm_scheduled.bus_id 
    AND bm_scheduled.status = 'scheduled'
  LEFT JOIN bus_maintenance bm_progress ON b.id = bm_progress.bus_id 
    AND bm_progress.status = 'in_progress'
  LEFT JOIN bus_maintenance bm_overdue ON b.id = bm_overdue.bus_id 
    AND bm_overdue.status = 'overdue'
  LEFT JOIN bus_maintenance bm_ytd ON b.id = bm_ytd.bus_id 
    AND bm_ytd.scheduled_date >= DATE_TRUNC('year', CURRENT_DATE)
  WHERE b.tenant_id = p_tenant_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to schedule recurring maintenance
CREATE OR REPLACE FUNCTION schedule_recurring_maintenance(p_bus_id UUID, p_tenant_id UUID)
RETURNS VOID AS $$
DECLARE
  v_maintenance RECORD;
BEGIN
  -- Find maintenance records with next_maintenance_date in the past that haven't been scheduled yet
  FOR v_maintenance IN (
    SELECT 
      bm.bus_id,
      bm.type,
      bm.description,
      bm.next_maintenance_date,
      bm.cost,
      bm.notes,
      bm.tenant_id,
      bm.technician_name
    FROM bus_maintenance bm
    WHERE bm.bus_id = p_bus_id
    AND bm.tenant_id = p_tenant_id
    AND bm.next_maintenance_date IS NOT NULL
    AND bm.next_maintenance_date <= CURRENT_DATE
    AND NOT EXISTS (
      SELECT 1 FROM bus_maintenance bm2
      WHERE bm2.bus_id = bm.bus_id
      AND bm2.scheduled_date = bm.next_maintenance_date
    )
  ) LOOP
    -- Create new maintenance record
    INSERT INTO bus_maintenance (
      bus_id,
      type,
      description,
      scheduled_date,
      cost,
      notes,
      tenant_id,
      status,
      technician_name
    ) VALUES (
      v_maintenance.bus_id,
      v_maintenance.type,
      'Scheduled ' || v_maintenance.type || ' maintenance (recurring)',
      v_maintenance.next_maintenance_date,
      v_maintenance.cost,
      v_maintenance.notes,
      v_maintenance.tenant_id,
      'scheduled',
      v_maintenance.technician_name
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add realtime support for maintenance tables
alter publication supabase_realtime add table bus_maintenance;
alter publication supabase_realtime add table maintenance_parts;
alter publication supabase_realtime add table spare_parts;
