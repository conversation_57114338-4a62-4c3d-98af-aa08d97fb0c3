-- Fix infinite recursion in RLS policies by avoiding self-referencing queries
-- The issue is that policies on the users table cannot query the users table itself

-- First, drop all existing problematic policies
DROP POLICY IF EXISTS "Admin users can access all users" ON public.users;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Admin users can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin users can update all users" ON public.users;
DROP POLICY IF EXISTS "School managers can view users in their tenant" ON public.users;
DROP POLICY IF EXISTS "School managers can update users in their tenant" ON public.users;

-- Create a function to check if a user is admin (bypasses RLS)
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  -- Directly query the users table without RLS
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN COALESCE(user_role = 'admin', false);
END;
$$;

-- Create a function to get user role (bypasses RLS)
CREATE OR REPLACE FUNCTION public.get_user_role(user_id uuid DEFAULT auth.uid())
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role text;
BEGIN
  -- Directly query the users table without RLS
  SELECT role INTO user_role 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN user_role;
END;
$$;

-- Create a function to get user tenant_id (bypasses RLS)
CREATE OR REPLACE FUNCTION public.get_user_tenant_id(user_id uuid DEFAULT auth.uid())
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_tenant_id uuid;
BEGIN
  -- Directly query the users table without RLS
  SELECT tenant_id INTO user_tenant_id 
  FROM public.users 
  WHERE id = user_id;
  
  RETURN user_tenant_id;
END;
$$;

-- Now create new policies that use these functions instead of direct queries
-- Policy for users to view their own profile
CREATE POLICY "Users can view their own profile"
ON public.users
FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- Policy for users to update their own profile
CREATE POLICY "Users can update their own profile"
ON public.users
FOR UPDATE
TO authenticated
USING (auth.uid() = id);

-- Policy for admin users to view all users
CREATE POLICY "Admin users can view all users"
ON public.users
FOR SELECT
TO authenticated
USING (public.is_admin());

-- Policy for admin users to update all users
CREATE POLICY "Admin users can update all users"
ON public.users
FOR UPDATE
TO authenticated
USING (public.is_admin());

-- Policy for admin users to insert new users
CREATE POLICY "Admin users can insert users"
ON public.users
FOR INSERT
TO authenticated
WITH CHECK (public.is_admin());

-- Policy for admin users to delete users
CREATE POLICY "Admin users can delete users"
ON public.users
FOR DELETE
TO authenticated
USING (public.is_admin());

-- Policy for school managers to view users in their tenant
CREATE POLICY "School managers can view users in their tenant"
ON public.users
FOR SELECT
TO authenticated
USING (
  public.get_user_role() = 'school_manager' 
  AND tenant_id = public.get_user_tenant_id()
  AND public.get_user_tenant_id() IS NOT NULL
);

-- Policy for school managers to update users in their tenant
CREATE POLICY "School managers can update users in their tenant"
ON public.users
FOR UPDATE
TO authenticated
USING (
  public.get_user_role() = 'school_manager' 
  AND tenant_id = public.get_user_tenant_id()
  AND public.get_user_tenant_id() IS NOT NULL
);

-- Fix other table policies to use the new functions
-- Buses table
DROP POLICY IF EXISTS "Admin can access all buses" ON public.buses;
DROP POLICY IF EXISTS "Tenant users can view their buses" ON public.buses;

CREATE POLICY "Users can access buses"
ON public.buses
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id() OR
  driver_id = auth.uid()
);

-- Students table
DROP POLICY IF EXISTS "Admin can access all students" ON public.students;
DROP POLICY IF EXISTS "Tenant users can view their students" ON public.students;

CREATE POLICY "Users can access students"
ON public.students
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id() OR
  parent_id = auth.uid()
);

-- Routes table
DROP POLICY IF EXISTS "Admin can access all routes" ON public.routes;
DROP POLICY IF EXISTS "Tenant users can view their routes" ON public.routes;

CREATE POLICY "Users can access routes"
ON public.routes
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  tenant_id = public.get_user_tenant_id()
);

-- Tenants table
DROP POLICY IF EXISTS "Admin can access all schools" ON public.tenants;
DROP POLICY IF EXISTS "Users can view their tenant" ON public.tenants;

CREATE POLICY "Users can access tenants"
ON public.tenants
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  id = public.get_user_tenant_id()
);

-- Attendance table
DROP POLICY IF EXISTS "Admin can access all attendance" ON public.attendance;

CREATE POLICY "Users can access attendance"
ON public.attendance
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  EXISTS (
    SELECT 1 FROM public.students s 
    WHERE s.id = student_id 
    AND (s.tenant_id = public.get_user_tenant_id() OR s.parent_id = auth.uid())
  )
);

-- Notifications table
DROP POLICY IF EXISTS "Admin can access all notifications" ON public.notifications;

CREATE POLICY "Users can access notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  user_id = auth.uid() OR
  tenant_id = public.get_user_tenant_id()
);

-- Bus maintenance table
DROP POLICY IF EXISTS "Admin can access all maintenance" ON public.bus_maintenance;

CREATE POLICY "Users can access bus maintenance"
ON public.bus_maintenance
FOR ALL
TO authenticated
USING (
  public.is_admin() OR
  EXISTS (
    SELECT 1 FROM public.buses b 
    WHERE b.id = bus_id 
    AND (b.tenant_id = public.get_user_tenant_id() OR b.driver_id = auth.uid())
  )
);

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION public.is_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_tenant_id(uuid) TO authenticated;