import { User, UserRole } from "../types";
import {
  RBACManager,
  Permission,
  DataScope,
  ResourceType,
  Action,
} from "./rbac";

/**
 * فئة التحكم في الوصول - تحتوي على منطق التحقق من الصلاحيات
 */
export class AccessControl {
  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لصفحة معينة
   */
  static canAccessRoute(user: User | null, route: string): boolean {
    if (!user) return false;

    const userRole = user.role as UserRole;

    // تعريف الصفحات والصلاحيات المطلوبة
    const routePermissions: Record<string, Permission[]> = {
      "/dashboard": [], // متاح للجميع
      "/dashboard/schools": [Permission.VIEW_ALL_SCHOOLS],
      "/dashboard/users": [
        Permission.VIEW_ALL_USERS,
        Permission.VIEW_TENANT_USERS,
      ],
      "/dashboard/buses": [
        Permission.VIEW_ALL_BUSES,
        Permission.VIEW_TENANT_BUSES,
      ],
      "/dashboard/routes": [
        Permission.VIEW_ALL_ROUTES,
        Permission.VIEW_TENANT_ROUTES,
      ],
      "/dashboard/my-route": [Permission.VIEW_ASSIGNED_ROUTE],
      "/dashboard/students": [
        Permission.VIEW_ALL_STUDENTS,
        Permission.VIEW_TENANT_STUDENTS,
      ],
      "/dashboard/children": [Permission.VIEW_OWN_CHILDREN],
      "/dashboard/attendance": [Permission.MANAGE_ATTENDANCE],
      "/dashboard/driver-attendance": [Permission.MANAGE_ATTENDANCE],
      "/dashboard/tracking": [Permission.TRACK_BUS],
      "/dashboard/reports": [
        Permission.VIEW_SYSTEM_REPORTS,
        Permission.VIEW_TENANT_REPORTS,
      ],
      "/dashboard/notifications": [
        Permission.VIEW_ALL_NOTIFICATIONS,
        Permission.SEND_NOTIFICATIONS,
      ],
      "/dashboard/evaluation": [], // متاح لمعظم الأدوار
      "/dashboard/settings": [], // متاح للجميع
      "/dashboard/profile": [], // متاح للجميع
    };

    const requiredPermissions = routePermissions[route];

    // إذا لم تكن هناك صلاحيات مطلوبة، فالصفحة متاحة
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    // فحص ما إذا كان المستخدم لديه أي من الصلاحيات المطلوبة
    return requiredPermissions.some((permission) =>
      RBACManager.hasPermission(userRole, permission),
    );
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه رؤية عنصر في القائمة
   */
  static canViewMenuItem(user: User | null, menuItem: string): boolean {
    if (!user) return false;

    const routeMap: Record<string, string> = {
      dashboard: "/dashboard",
      schools: "/dashboard/schools",
      users: "/dashboard/users",
      buses: "/dashboard/buses",
      routes: "/dashboard/routes",
      students: "/dashboard/students",
      attendance: "/dashboard/attendance",
      tracking: "/dashboard/tracking",
      reports: "/dashboard/reports",
      notifications: "/dashboard/notifications",
      evaluation: "/dashboard/evaluation",
      settings: "/dashboard/settings",
      profile: "/dashboard/profile",
    };

    const route = routeMap[menuItem];
    return route ? this.canAccessRoute(user, route) : false;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه تعديل بيانات مستخدم آخر
   */
  static canEditUser(currentUser: User | null, targetUser: User): boolean {
    if (!currentUser) return false;

    const currentRole = currentUser.role as UserRole;
    const targetRole = targetUser.role as UserRole;

    // المستخدم يمكنه تعديل بياناته الشخصية
    if (currentUser.id === targetUser.id) {
      return true;
    }

    // فحص التسلسل الهرمي للأدوار
    return (
      RBACManager.canManageRole(currentRole, targetRole) &&
      this.isSameTenantOrAdmin(currentUser, targetUser)
    );
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه حذف مستخدم آخر
   */
  static canDeleteUser(currentUser: User | null, targetUser: User): boolean {
    if (!currentUser) return false;

    // المستخدم لا يمكنه حذف نفسه
    if (currentUser.id === targetUser.id) {
      return false;
    }

    return this.canEditUser(currentUser, targetUser);
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه إنشاء مستخدم بدور معين
   */
  static canCreateUserWithRole(
    currentUser: User | null,
    targetRole: UserRole,
  ): boolean {
    if (!currentUser) return false;

    const currentRole = currentUser.role as UserRole;
    return RBACManager.canManageRole(currentRole, targetRole);
  }

  /**
   * فحص ما إذا كان المستخدمان في نفس المدرسة أو أن المستخدم الحالي أدمن
   */
  private static isSameTenantOrAdmin(
    currentUser: User,
    targetUser: User,
  ): boolean {
    // الأدمن يمكنه الوصول لكل شيء
    if (currentUser.role === UserRole.ADMIN) {
      return true;
    }

    // فحص ما إذا كانا في نفس المدرسة
    return currentUser.tenant_id === targetUser.tenant_id;
  }

  /**
   * الحصول على البيانات المفلترة حسب صلاحيات المستخدم
   */
  static filterDataByPermissions<T extends { tenant_id?: string; id?: string }>(
    user: User | null,
    data: T[],
    resourceType: ResourceType,
    ownerIdField?: keyof T,
  ): T[] {
    if (!user) return [];

    const userRole = user.role as UserRole;

    // الأدمن يرى كل البيانات
    if (userRole === UserRole.ADMIN) {
      return data;
    }

    // فحص نطاق البيانات المسموح
    const allowedScopes = RBACManager.getRoleDataScopes(userRole);

    return data.filter((item) => {
      // البيانات الشخصية
      if (
        allowedScopes.includes(DataScope.PERSONAL) &&
        ownerIdField &&
        item[ownerIdField] === user.id
      ) {
        return true;
      }

      // البيانات على مستوى المدرسة
      if (
        allowedScopes.includes(DataScope.TENANT) &&
        item.tenant_id === user.tenant_id
      ) {
        return true;
      }

      // البيانات المخصصة (للسائقين مثلاً)
      if (allowedScopes.includes(DataScope.ASSIGNED)) {
        // منطق إضافي للبيانات المخصصة حسب نوع المورد
        return this.isAssignedData(user, item, resourceType);
      }

      return false;
    });
  }

  /**
   * فحص ما إذا كانت البيانات مخصصة للمستخدم
   */
  private static isAssignedData<T extends { tenant_id?: string; id?: string }>(
    user: User,
    item: T,
    resourceType: ResourceType,
  ): boolean {
    // منطق مخصص حسب نوع المورد
    switch (resourceType) {
      case ResourceType.BUS:
        // السائق يرى الحافلة المخصصة له
        return (item as any).driver_id === user.id;

      case ResourceType.ROUTE:
        // السائق يرى المسار المخصص لحافلته
        return (item as any).bus?.driver_id === user.id;

      case ResourceType.STUDENT:
        // ولي الأمر يرى أطفاله
        return (item as any).parent_id === user.id;

      default:
        return false;
    }
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه تنفيذ عملية على مورد معين
   */
  static canPerformActionOnResource<
    T extends { tenant_id?: string; id?: string },
  >(
    user: User | null,
    resource: T,
    resourceType: ResourceType,
    action: Action,
    ownerIdField?: keyof T,
  ): boolean {
    if (!user) return false;

    const userRole = user.role as UserRole;

    // فحص الصلاحية الأساسية
    const hasBasicPermission = RBACManager.canPerformAction(
      userRole,
      resourceType,
      action,
      {
        userId: user.id,
        tenantId: user.tenant_id || undefined,
        resourceOwnerId: ownerIdField
          ? (resource[ownerIdField] as string)
          : undefined,
        resourceTenantId: resource.tenant_id,
      },
    );

    return hasBasicPermission;
  }

  /**
   * الحصول على رسالة خطأ مناسبة عند عدم وجود صلاحية
   */
  static getAccessDeniedMessage(user: User | null, action: string): string {
    if (!user) {
      return "يجب تسجيل الدخول للوصول لهذه الصفحة";
    }

    const roleNames: Record<UserRole, string> = {
      [UserRole.ADMIN]: "مدير النظام",
      [UserRole.SCHOOL_MANAGER]: "مدير المدرسة",
      [UserRole.SUPERVISOR]: "المشرف",
      [UserRole.DRIVER]: "السائق",
      [UserRole.PARENT]: "ولي الأمر",
      [UserRole.STUDENT]: "الطالب",
    };

    const roleName = roleNames[user.role as UserRole] || "المستخدم";

    return `عذراً، ${roleName} لا يملك صلاحية ${action}. يرجى التواصل مع مدير النظام.`;
  }
}

export default AccessControl;
