-- This migration fixes the infinite recursion in RLS policies
-- by creating more efficient policies that avoid circular references

-- First, drop all existing policies on the users table to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Admin users can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin users can update all users" ON public.users;
DROP POLICY IF EXISTS "School managers can view users in their tenant" ON public.users;
DROP POLICY IF EXISTS "School managers can update users in their tenant" ON public.users;

-- Create simplified policies for the users table
-- Policy for users to view their own profile (no tenant check needed)
CREATE POLICY "Users can view their own profile"
ON public.users
FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- Policy for users to update their own profile (no tenant check needed)
CREATE POLICY "Users can update their own profile"
ON public.users
FOR UPDATE
TO authenticated
USING (auth.uid() = id);

-- Policy for admin users to view all users (no tenant check needed)
CREATE POLICY "Admin users can view all users"
ON public.users
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Policy for admin users to update all users (no tenant check needed)
CREATE POLICY "Admin users can update all users"
ON public.users
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Policy for school managers to view users in their tenant
CREATE POLICY "School managers can view users in their tenant"
ON public.users
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'school_manager' AND tenant_id IS NOT NULL
  ) AND
  tenant_id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  )
);

-- Policy for school managers to update users in their tenant
CREATE POLICY "School managers can update users in their tenant"
ON public.users
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'school_manager' AND tenant_id IS NOT NULL
  ) AND
  tenant_id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  )
);

-- Fix buses table policies
DROP POLICY IF EXISTS "Tenant users can view their buses" ON public.buses;
DROP POLICY IF EXISTS "Admin users can view all buses" ON public.buses;

CREATE POLICY "Tenant users can view their buses"
ON public.buses
FOR SELECT
TO authenticated
USING (
  tenant_id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Fix students table policies
DROP POLICY IF EXISTS "Tenant users can view their students" ON public.students;
DROP POLICY IF EXISTS "Admin users can view all students" ON public.students;

CREATE POLICY "Tenant users can view their students"
ON public.students
FOR SELECT
TO authenticated
USING (
  tenant_id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Fix routes table policies
DROP POLICY IF EXISTS "Tenant users can view their routes" ON public.routes;
DROP POLICY IF EXISTS "Admin users can view all routes" ON public.routes;

CREATE POLICY "Tenant users can view their routes"
ON public.routes
FOR SELECT
TO authenticated
USING (
  tenant_id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Fix tenants table policies
DROP POLICY IF EXISTS "Users can view their tenant" ON public.tenants;
DROP POLICY IF EXISTS "Admin users can view all tenants" ON public.tenants;

CREATE POLICY "Users can view their tenant"
ON public.tenants
FOR SELECT
TO authenticated
USING (
  id = (
    SELECT tenant_id FROM public.users WHERE id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Create a new RPC function to get all data for admin users
CREATE OR REPLACE FUNCTION public.get_admin_data()
RETURNS TABLE (
  table_name text,
  record_count bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  RETURN QUERY
  SELECT 'users'::text, COUNT(*)::bigint FROM public.users
  UNION ALL
  SELECT 'buses'::text, COUNT(*)::bigint FROM public.buses
  UNION ALL
  SELECT 'students'::text, COUNT(*)::bigint FROM public.students
  UNION ALL
  SELECT 'routes'::text, COUNT(*)::bigint FROM public.routes
  UNION ALL
  SELECT 'tenants'::text, COUNT(*)::bigint FROM public.tenants;
  
  RETURN;
END;
$$;

-- Create RPC functions to safely get data for admin users
CREATE OR REPLACE FUNCTION public.get_all_buses()
RETURNS SETOF public.buses
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  RETURN QUERY
  SELECT * FROM public.buses ORDER BY created_at DESC;
  
  RETURN;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_students()
RETURNS SETOF public.students
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  RETURN QUERY
  SELECT * FROM public.students ORDER BY created_at DESC;
  
  RETURN;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_routes()
RETURNS SETOF public.routes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  RETURN QUERY
  SELECT * FROM public.routes ORDER BY created_at DESC;
  
  RETURN;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_all_tenants()
RETURNS SETOF public.tenants
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin') THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  RETURN QUERY
  SELECT * FROM public.tenants ORDER BY created_at DESC;
  
  RETURN;
END;
$$;
