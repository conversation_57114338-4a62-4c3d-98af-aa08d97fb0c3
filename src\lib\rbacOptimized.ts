/**
 * Optimized RBAC Implementation
 * Fixes critical issues found in audit
 */

import { UserRole } from "../types";

// Fixed and Enhanced Permission System
export enum Permission {
  // System Administration - Global Level
  SYSTEM_ADMIN = "system:admin",
  SYSTEM_SETTINGS = "system:settings",
  SYSTEM_AUDIT = "system:audit",
  SYSTEM_BACKUP = "system:backup",
  SYSTEM_ANALYTICS = "system:analytics",
  SYSTEM_BILLING = "system:billing",

  // School Management - Tenant Level
  SCHOOLS_VIEW_ALL = "schools:view_all",
  SCHOOLS_VIEW_OWN = "schools:view_own",
  SCHOOLS_CREATE = "schools:create",
  SCHOOLS_UPDATE = "schools:update",
  SCHOOLS_DELETE = "schools:delete",
  SCHOOLS_MANAGE_SETTINGS = "schools:manage_settings",
  SCHOOLS_MANAGE_BRANDING = "schools:manage_branding",

  // User Management - Multi-level
  USERS_VIEW_ALL = "users:view_all",
  USERS_VIEW_TENANT = "users:view_tenant",
  USERS_VIEW_OWN = "users:view_own",
  USERS_CREATE = "users:create",
  USERS_UPDATE_ALL = "users:update_all",
  USERS_UPDATE_TENANT = "users:update_tenant",
  USERS_UPDATE_OWN = "users:update_own",
  USERS_DELETE_ALL = "users:delete_all",
  USERS_DELETE_TENANT = "users:delete_tenant",
  USERS_ASSIGN_ROLES = "users:assign_roles",
  USERS_MANAGE_PERMISSIONS = "users:manage_permissions",

  // Bus Management
  BUSES_VIEW_ALL = "buses:view_all",
  BUSES_VIEW_TENANT = "buses:view_tenant",
  BUSES_VIEW_ASSIGNED = "buses:view_assigned",
  BUSES_CREATE = "buses:create",
  BUSES_UPDATE = "buses:update",
  BUSES_DELETE = "buses:delete",
  BUSES_ASSIGN_DRIVERS = "buses:assign_drivers",
  BUSES_TRACK = "buses:track",
  BUSES_MANAGE_MAINTENANCE = "buses:manage_maintenance",

  // Student Management
  STUDENTS_VIEW_ALL = "students:view_all",
  STUDENTS_VIEW_TENANT = "students:view_tenant",
  STUDENTS_VIEW_CHILDREN = "students:view_children",
  STUDENTS_VIEW_OWN = "students:view_own",
  STUDENTS_CREATE = "students:create",
  STUDENTS_UPDATE = "students:update",
  STUDENTS_DELETE = "students:delete",
  STUDENTS_MANAGE_ATTENDANCE = "students:manage_attendance",
  STUDENTS_VIEW_ATTENDANCE = "students:view_attendance",
  STUDENTS_ASSIGN_ROUTES = "students:assign_routes",

  // Route Management
  ROUTES_VIEW_ALL = "routes:view_all",
  ROUTES_VIEW_TENANT = "routes:view_tenant",
  ROUTES_VIEW_ASSIGNED = "routes:view_assigned",
  ROUTES_CREATE = "routes:create",
  ROUTES_UPDATE = "routes:update",
  ROUTES_DELETE = "routes:delete",
  ROUTES_OPTIMIZE = "routes:optimize",
  ROUTES_ASSIGN_BUSES = "routes:assign_buses",

  // Reports and Analytics
  REPORTS_VIEW_SYSTEM = "reports:view_system",
  REPORTS_VIEW_TENANT = "reports:view_tenant",
  REPORTS_VIEW_PERSONAL = "reports:view_personal",
  REPORTS_CREATE = "reports:create",
  REPORTS_EXPORT = "reports:export",
  REPORTS_SCHEDULE = "reports:schedule",
  REPORTS_ANALYTICS = "reports:analytics",

  // Notification Management
  NOTIFICATIONS_VIEW_ALL = "notifications:view_all",
  NOTIFICATIONS_VIEW_TENANT = "notifications:view_tenant",
  NOTIFICATIONS_VIEW_OWN = "notifications:view_own",
  NOTIFICATIONS_SEND_SYSTEM = "notifications:send_system",
  NOTIFICATIONS_SEND_TENANT = "notifications:send_tenant",
  NOTIFICATIONS_MANAGE_TEMPLATES = "notifications:manage_templates",
  NOTIFICATIONS_MANAGE_SETTINGS = "notifications:manage_settings",

  // Maintenance Management
  MAINTENANCE_VIEW_ALL = "maintenance:view_all",
  MAINTENANCE_VIEW_TENANT = "maintenance:view_tenant",
  MAINTENANCE_VIEW_ASSIGNED = "maintenance:view_assigned",
  MAINTENANCE_CREATE = "maintenance:create",
  MAINTENANCE_UPDATE = "maintenance:update",
  MAINTENANCE_DELETE = "maintenance:delete",
  MAINTENANCE_SCHEDULE = "maintenance:schedule",
  MAINTENANCE_APPROVE = "maintenance:approve",

  // Evaluation and Feedback
  EVALUATION_VIEW_ALL = "evaluation:view_all",
  EVALUATION_VIEW_TENANT = "evaluation:view_tenant",
  EVALUATION_VIEW_OWN = "evaluation:view_own",
  EVALUATION_CREATE = "evaluation:create",
  EVALUATION_RESPOND = "evaluation:respond",
  EVALUATION_ANALYZE = "evaluation:analyze",
  EVALUATION_MODERATE = "evaluation:moderate",

  // Emergency and Safety
  EMERGENCY_TRIGGER = "emergency:trigger",
  EMERGENCY_RESPOND = "emergency:respond",
  EMERGENCY_MANAGE = "emergency:manage",
  SAFETY_MONITOR = "safety:monitor",
  SAFETY_ALERTS = "safety:alerts",
}

// Enhanced Data Scopes
export enum DataScope {
  GLOBAL = "global",
  TENANT = "tenant",
  PERSONAL = "personal",
  ASSIGNED = "assigned",
  CHILDREN = "children",
  DEPARTMENT = "department",
  ROUTE_BASED = "route_based",
  BUS_BASED = "bus_based",
  CLASS_BASED = "class_based",
}

// Enhanced Resource Types
export enum ResourceType {
  SYSTEM = "system",
  SCHOOL = "school",
  USER = "user",
  BUS = "bus",
  ROUTE = "route",
  STUDENT = "student",
  ATTENDANCE = "attendance",
  NOTIFICATION = "notification",
  REPORT = "report",
  MAINTENANCE = "maintenance",
  EVALUATION = "evaluation",
  EMERGENCY = "emergency",
  SAFETY = "safety",
  ANALYTICS = "analytics",
  AUDIT = "audit",
}

// Enhanced Actions
export enum Action {
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  MANAGE = "manage",
  ASSIGN = "assign",
  TRACK = "track",
  EXPORT = "export",
  SCHEDULE = "schedule",
  ANALYZE = "analyze",
  APPROVE = "approve",
  MONITOR = "monitor",
  ALERT = "alert",
  CONFIGURE = "configure",
}

// Fixed Role Permissions - Corrected all undefined references
export const OPTIMIZED_ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // System permissions
    Permission.SYSTEM_ADMIN,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_AUDIT,
    Permission.SYSTEM_BACKUP,
    Permission.SYSTEM_ANALYTICS,
    Permission.SYSTEM_BILLING,

    // School permissions
    Permission.SCHOOLS_VIEW_ALL,
    Permission.SCHOOLS_CREATE,
    Permission.SCHOOLS_UPDATE,
    Permission.SCHOOLS_DELETE,
    Permission.SCHOOLS_MANAGE_SETTINGS,
    Permission.SCHOOLS_MANAGE_BRANDING,

    // User permissions
    Permission.USERS_VIEW_ALL,
    Permission.USERS_VIEW_TENANT,
    Permission.USERS_VIEW_OWN,
    Permission.USERS_CREATE,
    Permission.USERS_UPDATE_ALL,
    Permission.USERS_UPDATE_TENANT,
    Permission.USERS_UPDATE_OWN,
    Permission.USERS_DELETE_ALL,
    Permission.USERS_DELETE_TENANT,
    Permission.USERS_ASSIGN_ROLES,
    Permission.USERS_MANAGE_PERMISSIONS,

    // Bus permissions
    Permission.BUSES_VIEW_ALL,
    Permission.BUSES_VIEW_TENANT,
    Permission.BUSES_VIEW_ASSIGNED,
    Permission.BUSES_CREATE,
    Permission.BUSES_UPDATE,
    Permission.BUSES_DELETE,
    Permission.BUSES_ASSIGN_DRIVERS,
    Permission.BUSES_TRACK,
    Permission.BUSES_MANAGE_MAINTENANCE,

    // Student permissions
    Permission.STUDENTS_VIEW_ALL,
    Permission.STUDENTS_VIEW_TENANT,
    Permission.STUDENTS_VIEW_CHILDREN,
    Permission.STUDENTS_VIEW_OWN,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_UPDATE,
    Permission.STUDENTS_DELETE,
    Permission.STUDENTS_MANAGE_ATTENDANCE,
    Permission.STUDENTS_VIEW_ATTENDANCE,
    Permission.STUDENTS_ASSIGN_ROUTES,

    // Route permissions
    Permission.ROUTES_VIEW_ALL,
    Permission.ROUTES_VIEW_TENANT,
    Permission.ROUTES_VIEW_ASSIGNED,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_UPDATE,
    Permission.ROUTES_DELETE,
    Permission.ROUTES_OPTIMIZE,
    Permission.ROUTES_ASSIGN_BUSES,

    // Report permissions
    Permission.REPORTS_VIEW_SYSTEM,
    Permission.REPORTS_VIEW_TENANT,
    Permission.REPORTS_VIEW_PERSONAL,
    Permission.REPORTS_CREATE,
    Permission.REPORTS_EXPORT,
    Permission.REPORTS_SCHEDULE,
    Permission.REPORTS_ANALYTICS,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_ALL,
    Permission.NOTIFICATIONS_VIEW_TENANT,
    Permission.NOTIFICATIONS_VIEW_OWN,
    Permission.NOTIFICATIONS_SEND_SYSTEM,
    Permission.NOTIFICATIONS_SEND_TENANT,
    Permission.NOTIFICATIONS_MANAGE_TEMPLATES,
    Permission.NOTIFICATIONS_MANAGE_SETTINGS,

    // Maintenance permissions
    Permission.MAINTENANCE_VIEW_ALL,
    Permission.MAINTENANCE_VIEW_TENANT,
    Permission.MAINTENANCE_VIEW_ASSIGNED,
    Permission.MAINTENANCE_CREATE,
    Permission.MAINTENANCE_UPDATE,
    Permission.MAINTENANCE_DELETE,
    Permission.MAINTENANCE_SCHEDULE,
    Permission.MAINTENANCE_APPROVE,

    // Evaluation permissions
    Permission.EVALUATION_VIEW_ALL,
    Permission.EVALUATION_VIEW_TENANT,
    Permission.EVALUATION_VIEW_OWN,
    Permission.EVALUATION_CREATE,
    Permission.EVALUATION_RESPOND,
    Permission.EVALUATION_ANALYZE,
    Permission.EVALUATION_MODERATE,

    // Emergency permissions
    Permission.EMERGENCY_TRIGGER,
    Permission.EMERGENCY_RESPOND,
    Permission.EMERGENCY_MANAGE,
    Permission.SAFETY_MONITOR,
    Permission.SAFETY_ALERTS,
  ],

  [UserRole.SCHOOL_MANAGER]: [
    // School permissions
    Permission.SCHOOLS_VIEW_OWN,
    Permission.SCHOOLS_MANAGE_SETTINGS,
    Permission.SCHOOLS_MANAGE_BRANDING,

    // User permissions
    Permission.USERS_VIEW_TENANT,
    Permission.USERS_VIEW_OWN,
    Permission.USERS_CREATE,
    Permission.USERS_UPDATE_TENANT,
    Permission.USERS_UPDATE_OWN,
    Permission.USERS_DELETE_TENANT,
    Permission.USERS_ASSIGN_ROLES,

    // Bus permissions
    Permission.BUSES_VIEW_TENANT,
    Permission.BUSES_VIEW_ASSIGNED,
    Permission.BUSES_CREATE,
    Permission.BUSES_UPDATE,
    Permission.BUSES_ASSIGN_DRIVERS,
    Permission.BUSES_TRACK,
    Permission.BUSES_MANAGE_MAINTENANCE,

    // Student permissions
    Permission.STUDENTS_VIEW_TENANT,
    Permission.STUDENTS_VIEW_CHILDREN,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_UPDATE,
    Permission.STUDENTS_DELETE,
    Permission.STUDENTS_MANAGE_ATTENDANCE,
    Permission.STUDENTS_VIEW_ATTENDANCE,
    Permission.STUDENTS_ASSIGN_ROUTES,

    // Route permissions
    Permission.ROUTES_VIEW_TENANT,
    Permission.ROUTES_VIEW_ASSIGNED,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_UPDATE,
    Permission.ROUTES_DELETE,
    Permission.ROUTES_OPTIMIZE,
    Permission.ROUTES_ASSIGN_BUSES,

    // Report permissions
    Permission.REPORTS_VIEW_TENANT,
    Permission.REPORTS_CREATE,
    Permission.REPORTS_EXPORT,
    Permission.REPORTS_SCHEDULE,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_TENANT,
    Permission.NOTIFICATIONS_VIEW_OWN,
    Permission.NOTIFICATIONS_SEND_TENANT,
    Permission.NOTIFICATIONS_MANAGE_TEMPLATES,
    Permission.NOTIFICATIONS_MANAGE_SETTINGS,

    // Maintenance permissions
    Permission.MAINTENANCE_VIEW_TENANT,
    Permission.MAINTENANCE_CREATE,
    Permission.MAINTENANCE_UPDATE,
    Permission.MAINTENANCE_SCHEDULE,
    Permission.MAINTENANCE_APPROVE,

    // Evaluation permissions
    Permission.EVALUATION_VIEW_TENANT,
    Permission.EVALUATION_CREATE,
    Permission.EVALUATION_RESPOND,
    Permission.EVALUATION_ANALYZE,
    Permission.EVALUATION_MODERATE,

    // Emergency permissions
    Permission.EMERGENCY_RESPOND,
    Permission.SAFETY_MONITOR,
    Permission.SAFETY_ALERTS,
  ],

  [UserRole.SUPERVISOR]: [
    // User permissions
    Permission.USERS_VIEW_TENANT,
    Permission.USERS_VIEW_OWN,
    Permission.USERS_UPDATE_OWN,

    // Bus permissions
    Permission.BUSES_VIEW_TENANT,
    Permission.BUSES_TRACK,

    // Student permissions
    Permission.STUDENTS_VIEW_TENANT,
    Permission.STUDENTS_MANAGE_ATTENDANCE,
    Permission.STUDENTS_VIEW_ATTENDANCE,

    // Route permissions
    Permission.ROUTES_VIEW_TENANT,

    // Report permissions
    Permission.REPORTS_VIEW_TENANT,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_TENANT,
    Permission.NOTIFICATIONS_SEND_TENANT,

    // Maintenance permissions
    Permission.MAINTENANCE_VIEW_TENANT,

    // Evaluation permissions
    Permission.EVALUATION_VIEW_TENANT,
    Permission.EVALUATION_CREATE,
    Permission.EVALUATION_RESPOND,

    // Safety permissions
    Permission.SAFETY_MONITOR,
  ],

  [UserRole.DRIVER]: [
    // User permissions
    Permission.USERS_VIEW_OWN,
    Permission.USERS_UPDATE_OWN,

    // Bus permissions
    Permission.BUSES_VIEW_ASSIGNED,
    Permission.BUSES_TRACK,

    // Student permissions
    Permission.STUDENTS_MANAGE_ATTENDANCE,
    Permission.STUDENTS_VIEW_ATTENDANCE,

    // Route permissions
    Permission.ROUTES_VIEW_ASSIGNED,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_OWN,

    // Maintenance permissions
    Permission.MAINTENANCE_VIEW_ASSIGNED,
    Permission.MAINTENANCE_CREATE,

    // Evaluation permissions
    Permission.EVALUATION_RESPOND,

    // Emergency permissions
    Permission.EMERGENCY_TRIGGER,
    Permission.SAFETY_ALERTS,
  ],

  [UserRole.PARENT]: [
    // User permissions
    Permission.USERS_VIEW_OWN,
    Permission.USERS_UPDATE_OWN,

    // Bus permissions
    Permission.BUSES_TRACK,

    // Student permissions
    Permission.STUDENTS_VIEW_CHILDREN,
    Permission.STUDENTS_VIEW_ATTENDANCE,

    // Route permissions
    Permission.ROUTES_VIEW_ASSIGNED,

    // Report permissions
    Permission.REPORTS_VIEW_PERSONAL,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_OWN,

    // Evaluation permissions
    Permission.EVALUATION_CREATE,
    Permission.EVALUATION_RESPOND,

    // Emergency permissions
    Permission.EMERGENCY_TRIGGER,
  ],

  [UserRole.STUDENT]: [
    // User permissions
    Permission.USERS_VIEW_OWN,
    Permission.USERS_UPDATE_OWN,

    // Bus permissions
    Permission.BUSES_TRACK,

    // Student permissions
    Permission.STUDENTS_VIEW_OWN,
    Permission.STUDENTS_VIEW_ATTENDANCE,

    // Route permissions
    Permission.ROUTES_VIEW_ASSIGNED,

    // Notification permissions
    Permission.NOTIFICATIONS_VIEW_OWN,
  ],
};

// Enhanced Data Scope Definitions
export const OPTIMIZED_ROLE_DATA_SCOPE: Record<UserRole, DataScope[]> = {
  [UserRole.ADMIN]: [
    DataScope.GLOBAL,
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
    DataScope.CHILDREN,
    DataScope.DEPARTMENT,
    DataScope.ROUTE_BASED,
    DataScope.BUS_BASED,
    DataScope.CLASS_BASED,
  ],
  [UserRole.SCHOOL_MANAGER]: [
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
    DataScope.CHILDREN,
    DataScope.DEPARTMENT,
    DataScope.ROUTE_BASED,
    DataScope.BUS_BASED,
    DataScope.CLASS_BASED,
  ],
  [UserRole.SUPERVISOR]: [
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
    DataScope.ROUTE_BASED,
    DataScope.BUS_BASED,
  ],
  [UserRole.DRIVER]: [
    DataScope.ASSIGNED,
    DataScope.PERSONAL,
    DataScope.BUS_BASED,
    DataScope.ROUTE_BASED,
  ],
  [UserRole.PARENT]: [DataScope.PERSONAL, DataScope.CHILDREN],
  [UserRole.STUDENT]: [DataScope.PERSONAL],
};

// Enhanced Role Hierarchy
export const OPTIMIZED_ROLE_HIERARCHY: Record<UserRole, UserRole[]> = {
  [UserRole.ADMIN]: [
    UserRole.SCHOOL_MANAGER,
    UserRole.SUPERVISOR,
    UserRole.DRIVER,
    UserRole.PARENT,
    UserRole.STUDENT,
  ],
  [UserRole.SCHOOL_MANAGER]: [
    UserRole.SUPERVISOR,
    UserRole.DRIVER,
    UserRole.PARENT,
    UserRole.STUDENT,
  ],
  [UserRole.SUPERVISOR]: [UserRole.DRIVER],
  [UserRole.DRIVER]: [],
  [UserRole.PARENT]: [],
  [UserRole.STUDENT]: [],
};

// JSON Permission Structure for each role
export const JSON_PERMISSION_STRUCTURES = {
  [UserRole.ADMIN]: {
    role: "admin",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.ADMIN],
    scope: {
      level: "global",
      restrictions: {
        school_id: "none",
        department_id: "none",
        route_id: "none",
        bus_id: "none",
      },
    },
    actions: {
      system: ["create", "read", "update", "delete", "manage", "configure"],
      school: ["create", "read", "update", "delete", "manage"],
      user: ["create", "read", "update", "delete", "manage", "assign"],
      bus: ["create", "read", "update", "delete", "manage", "assign", "track"],
      route: ["create", "read", "update", "delete", "manage", "assign"],
      student: ["create", "read", "update", "delete", "manage"],
      attendance: ["create", "read", "update", "delete", "manage"],
      report: ["create", "read", "export", "schedule", "analyze"],
      notification: ["create", "read", "update", "delete", "manage"],
      maintenance: [
        "create",
        "read",
        "update",
        "delete",
        "manage",
        "schedule",
        "approve",
      ],
      evaluation: ["create", "read", "update", "delete", "analyze"],
      emergency: ["create", "read", "update", "manage", "monitor"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },

  [UserRole.SCHOOL_MANAGER]: {
    role: "school_manager",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.SCHOOL_MANAGER],
    scope: {
      level: "tenant",
      restrictions: {
        school_id: "strict",
        department_id: "flexible",
        route_id: "flexible",
        bus_id: "flexible",
      },
    },
    actions: {
      school: ["read", "update", "manage"],
      user: ["create", "read", "update", "delete", "manage"],
      bus: ["create", "read", "update", "assign", "track"],
      route: ["create", "read", "update", "delete", "assign"],
      student: ["create", "read", "update", "delete", "manage"],
      attendance: ["create", "read", "update", "manage"],
      report: ["read", "export", "schedule"],
      notification: ["create", "read", "manage"],
      maintenance: ["create", "read", "update", "schedule"],
      evaluation: ["create", "read", "analyze"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },

  [UserRole.SUPERVISOR]: {
    role: "supervisor",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.SUPERVISOR],
    scope: {
      level: "tenant",
      restrictions: {
        school_id: "strict",
        department_id: "strict",
        route_id: "flexible",
        bus_id: "flexible",
      },
    },
    actions: {
      user: ["read"],
      bus: ["read", "track", "monitor"],
      route: ["read", "monitor"],
      student: ["read", "manage"],
      attendance: ["create", "read", "update", "manage"],
      report: ["read"],
      notification: ["create", "read"],
      maintenance: ["read"],
      evaluation: ["create", "read"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },

  [UserRole.DRIVER]: {
    role: "driver",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.DRIVER],
    scope: {
      level: "assigned",
      restrictions: {
        school_id: "strict",
        department_id: "none",
        route_id: "strict",
        bus_id: "strict",
      },
    },
    actions: {
      user: ["read", "update"],
      bus: ["read", "track", "monitor"],
      route: ["read"],
      student: ["read"],
      attendance: ["create", "read", "update"],
      maintenance: ["create", "read"],
      evaluation: ["read", "update"],
      emergency: ["trigger", "alert"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },

  [UserRole.PARENT]: {
    role: "parent",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.PARENT],
    scope: {
      level: "children",
      restrictions: {
        school_id: "strict",
        department_id: "none",
        route_id: "none",
        bus_id: "none",
      },
    },
    actions: {
      user: ["read", "update"],
      bus: ["read", "track"],
      route: ["read"],
      student: ["read"],
      attendance: ["read"],
      report: ["read"],
      evaluation: ["create", "update"],
      notification: ["read"],
      emergency: ["trigger"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },

  [UserRole.STUDENT]: {
    role: "student",
    permissions: OPTIMIZED_ROLE_PERMISSIONS[UserRole.STUDENT],
    scope: {
      level: "personal",
      restrictions: {
        school_id: "strict",
        department_id: "none",
        route_id: "none",
        bus_id: "none",
      },
    },
    actions: {
      user: ["read", "update"],
      bus: ["read", "track"],
      route: ["read"],
      student: ["read"],
      attendance: ["read"],
      notification: ["read"],
    },
    metadata: {
      created_at: "2024-01-25T00:00:00Z",
      updated_at: "2024-01-25T00:00:00Z",
      version: "2.0.0",
      created_by: "system",
    },
  },
};

export default {
  OPTIMIZED_ROLE_PERMISSIONS,
  OPTIMIZED_ROLE_DATA_SCOPE,
  OPTIMIZED_ROLE_HIERARCHY,
  JSON_PERMISSION_STRUCTURES,
};
