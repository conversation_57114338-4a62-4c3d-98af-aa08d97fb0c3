import React, { createContext, useContext, useEffect, useState } from "react";
import { RealtimeChannel } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";
import { useAuth } from "./AuthContext";
import * as api from "../lib/api";
import type { Tables } from "../lib/api";
import { Tenant, User, Bus, Route, Student } from "../types";

interface RouteWithStops extends Tables<"routes"> {
  stops?: Tables<"route_stops">[];
}

interface DatabaseContextType {
  tenant: Tenant | null;
  tenants: Tenant[];
  users: User[];
  buses: Bus[];
  routes: RouteWithStops[];
  students: Student[];
  loading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  // Tenant-specific data management
  createBus: (busData: Partial<Bus>) => Promise<Bus | null>;
  updateBus: (id: string, updates: Partial<Bus>) => Promise<Bus | null>;
  deleteBus: (id: string) => Promise<boolean>;
  createStudent: (studentData: Partial<Student>) => Promise<Student | null>;
  updateStudent: (
    id: string,
    updates: Partial<Student>,
  ) => Promise<Student | null>;
  deleteStudent: (id: string) => Promise<boolean>;
  createRoute: (routeData: Partial<Route>) => Promise<Route | null>;
  updateRoute: (id: string, updates: Partial<Route>) => Promise<Route | null>;
  deleteRoute: (id: string) => Promise<boolean>;
  // Tenant management for super admin
  createTenant: (tenantData: Partial<Tenant>) => Promise<Tenant | null>;
  updateTenant: (
    id: string,
    updates: Partial<Tenant>,
  ) => Promise<Tenant | null>;
  deleteTenant: (id: string) => Promise<boolean>;
  assignSchoolManager: (tenantId: string, userId: string) => Promise<boolean>;
  assignBusDriver: (busId: string, driverId: string | null) => Promise<boolean>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(
  undefined,
);

function DatabaseProvider({ children }: { children: React.ReactNode }) {
  const { user, tenant: authTenant } = useAuth();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [buses, setBuses] = useState<Bus[]>([]);
  const [routes, setRoutes] = useState<RouteWithStops[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [subscriptions, setSubscriptions] = useState<RealtimeChannel[]>([]);

  const fetchData = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("Fetching data for user:", {
        id: user.id,
        role: user.role,
        tenant_id: user.tenant_id,
      });

      // Force refresh to ensure we get latest data
      console.log("Starting data fetch...");

      // Set tenant from auth context
      if (authTenant) {
        setTenant(authTenant);
      }

      if (user.role === "admin") {
        // Admin has full access to all data across all tenants
        console.log("DatabaseContext: Fetching all data for admin user");
        console.log("DatabaseContext: Current admin user:", {
          id: user.id,
          role: user.role,
          tenant_id: user.tenant_id,
          email: user.email,
        });

        // Force a fresh fetch by adding timestamp
        const timestamp = Date.now();
        console.log(
          `DatabaseContext: Admin data fetch started at: ${timestamp}`,
        );

        // Fetch all data for admin - regardless of tenant_id
        console.log("DatabaseContext: Fetching all data for admin...");

        try {
          console.log(
            "DatabaseContext: Fetching all users via RPC function...",
          );

          // Use the safe RPC function that bypasses RLS
          const { data: usersData, error: usersError } =
            await supabase.rpc("get_all_users");

          if (usersError) {
            console.error(
              "DatabaseContext: RPC users query failed:",
              usersError,
            );
            throw usersError;
          }

          if (usersData && usersData.length > 0) {
            console.log(
              "DatabaseContext: RPC users query successful, found",
              usersData.length,
              "users",
            );
            setUsers(usersData);
          } else {
            console.warn(
              "DatabaseContext: RPC users query returned no results",
            );
            setUsers([]);
          }
        } catch (usersError) {
          console.error(
            "DatabaseContext: Error fetching users via RPC:",
            usersError,
          );
          // Set empty array as fallback
          setUsers([]);
        }

        // Fetch other data
        console.log(
          "DatabaseContext: Fetching all data for admin via RPC functions...",
        );

        // Initialize variables outside try block to ensure they're always defined
        let busesData: any[] = [];
        let routesData: any[] = [];
        let studentsData: any[] = [];
        let tenantsData: any[] = [];

        try {
          // Try RPC functions first, fallback to direct queries for admin
          const [
            fetchedBusesData,
            fetchedRoutesData,
            fetchedStudentsData,
            fetchedTenantsData,
          ] = await Promise.allSettled([
            api.getAllBuses().catch(async () => {
              console.log(
                "DatabaseContext: Fallback to direct buses query for admin",
              );
              const { data } = await supabase.from("buses").select("*");
              return data || [];
            }),
            api.getAllRoutes().catch(async () => {
              console.log(
                "DatabaseContext: Fallback to direct routes query for admin",
              );
              const { data } = await supabase
                .from("routes")
                .select(
                  "id, name, is_active, bus_id, tenant_id, created_at, updated_at, schedule",
                );
              return data || [];
            }),
            api.getAllStudents().catch(async () => {
              console.log(
                "DatabaseContext: Fallback to direct students query for admin",
              );
              const { data } = await supabase.from("students").select("*");
              return data || [];
            }),
            api.getAllTenants().catch(async () => {
              console.log(
                "DatabaseContext: Fallback to direct tenants query for admin",
              );
              const { data } = await supabase.from("tenants").select("*");
              return data || [];
            }),
          ]);

          // Extract data from settled promises
          busesData =
            fetchedBusesData.status === "fulfilled"
              ? fetchedBusesData.value || []
              : [];
          routesData =
            fetchedRoutesData.status === "fulfilled"
              ? fetchedRoutesData.value || []
              : [];
          studentsData =
            fetchedStudentsData.status === "fulfilled"
              ? fetchedStudentsData.value || []
              : [];
          tenantsData =
            fetchedTenantsData.status === "fulfilled"
              ? fetchedTenantsData.value || []
              : [];

          console.log("DatabaseContext: Admin data fetched successfully");

          console.log("DatabaseContext: Admin data fetched:", {
            users: users.length,
            buses: busesData.length,
            routes: routesData.length,
            students: studentsData.length,
            tenants: tenantsData.length,
          });
        } catch (adminDataError) {
          console.error(
            "DatabaseContext: Error fetching admin data:",
            adminDataError,
          );
          // Variables are already initialized as empty arrays
          // Set empty arrays in state as fallback
          setBuses([]);
          setRoutes([]);
          setStudents([]);
          setTenants([]);
          // Don't throw the error, continue with empty data
        }

        // Additional debugging for user data
        if (users.length > 0) {
          console.log(
            "DatabaseContext: Sample admin users data:",
            users.slice(0, 3).map((u) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              role: u.role,
              tenant_id: u.tenant_id,
              is_active: u.is_active,
            })),
          );
        }

        // Set remaining data for admin user
        console.log(
          "DatabaseContext: Setting buses data for admin:",
          busesData.length,
        );
        setBuses(busesData);
        setRoutes(routesData);
        setStudents(studentsData);
        setTenants(tenantsData);

        console.log("Admin data successfully set:", {
          users: users.length,
          buses: busesData.length,
          routes: routesData.length,
          students: studentsData.length,
          tenants: tenantsData.length,
        });

        // Set up real-time subscriptions for all data (admin)
        setupRealtimeSubscriptionsForAllData();
      } else if (user.tenant_id) {
        // Tenant-scoped user sees only their tenant's data
        console.log("Fetching tenant users for tenant:", user.tenant_id);
        const [usersData, busesData, routesData, studentsData] =
          await Promise.all([
            api.getUsersByTenant(user.tenant_id),
            api.getBusesByTenant(user.tenant_id),
            api.getRoutesByTenant(user.tenant_id),
            api.getStudentsByTenant(user.tenant_id),
          ]);

        console.log("Tenant users fetched:", usersData?.length || 0, "users");
        console.log("Tenant buses fetched:", busesData?.length || 0, "buses");
        console.log(
          "Tenant routes fetched:",
          routesData?.length || 0,
          "routes",
        );
        console.log(
          "Tenant students fetched:",
          studentsData?.length || 0,
          "students",
        );

        setUsers(usersData || []);
        setBuses(busesData || []);
        setRoutes(routesData || []);
        setStudents(studentsData || []);

        // Set up real-time subscriptions for tenant data
        setupRealtimeSubscriptions(user.tenant_id);
      } else {
        // User without tenant_id and not admin - limited access
        console.log("Fetching limited data for user without tenant access");
        setUsers([]);
        setBuses([]);
        setRoutes([]);
        setStudents([]);
        setTenants([]);
      }
    } catch (err) {
      console.error("Error fetching data:", err);
      setError(err instanceof Error ? err : new Error("An error occurred"));
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscriptions = (tenantId: string) => {
    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    const newSubscriptions: RealtimeChannel[] = [];

    // Subscribe to buses changes
    const busesChannel = supabase
      .channel(`tenant-${tenantId}-buses`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "buses",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Buses change received:", payload);
          // Refresh buses data
          api.getBusesByTenant(tenantId).then((data) => {
            if (data) setBuses(data);
          });
        },
      )
      .subscribe();

    // Subscribe to users changes
    const usersChannel = supabase
      .channel(`tenant-${tenantId}-users`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "users",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Users change received:", payload);
          // Refresh users data
          api.getUsersByTenant(tenantId).then((data) => {
            if (data) {
              console.log(
                "Updated users from subscription:",
                data.length,
                "users",
              );
              setUsers(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to students changes
    const studentsChannel = supabase
      .channel(`tenant-${tenantId}-students`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "students",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Students change received:", payload);
          // Refresh students data
          api.getStudentsByTenant(tenantId).then((data) => {
            if (data) setStudents(data);
          });
        },
      )
      .subscribe();

    // Subscribe to routes changes
    const routesChannel = supabase
      .channel(`tenant-${tenantId}-routes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "routes",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Routes change received:", payload);
          // Refresh routes data
          api.getRoutesByTenant(tenantId).then((data) => {
            if (data) setRoutes(data);
          });
        },
      )
      .subscribe();

    newSubscriptions.push(
      busesChannel,
      usersChannel,
      studentsChannel,
      routesChannel,
    );
    setSubscriptions(newSubscriptions);
  };

  // Setup subscriptions for all data (for admin users)
  const setupRealtimeSubscriptionsForAllData = () => {
    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    const newSubscriptions: RealtimeChannel[] = [];

    // Subscribe to all users changes
    const usersChannel = supabase
      .channel(`all-users`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "users",
        },
        (payload) => {
          console.log("All users change received:", payload);
          // Refresh all users data using RPC function
          supabase.rpc("get_all_users").then(({ data, error }) => {
            if (!error && data) {
              console.log(
                "Updated all users from subscription:",
                data.length,
                "users",
              );
              setUsers(data);
            } else if (error) {
              console.error("Error refreshing users from subscription:", error);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all buses changes
    const busesChannel = supabase
      .channel(`all-buses`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "buses",
        },
        (payload) => {
          console.log("All buses change received:", payload);
          // Refresh all buses data
          api.getAllBuses().then((data) => {
            if (data) {
              console.log(
                "Updated all buses from subscription:",
                data.length,
                "buses",
              );
              setBuses(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all routes changes
    const routesChannel = supabase
      .channel(`all-routes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "routes",
        },
        (payload) => {
          console.log("All routes change received:", payload);
          // Refresh all routes data
          api.getAllRoutes().then((data) => {
            if (data) {
              console.log(
                "Updated all routes from subscription:",
                data.length,
                "routes",
              );
              setRoutes(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all students changes
    const studentsChannel = supabase
      .channel(`all-students`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "students",
        },
        (payload) => {
          console.log("All students change received:", payload);
          // Refresh all students data
          api.getAllStudents().then((data) => {
            if (data) {
              console.log(
                "Updated all students from subscription:",
                data.length,
                "students",
              );
              setStudents(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all tenants changes
    const tenantsChannel = supabase
      .channel(`all-tenants`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "tenants",
        },
        (payload) => {
          console.log("All tenants change received:", payload);
          // Refresh all data since tenant changes affect multiple entities
          fetchData();
        },
      )
      .subscribe();

    newSubscriptions.push(
      usersChannel,
      busesChannel,
      routesChannel,
      studentsChannel,
      tenantsChannel,
    );
    setSubscriptions(newSubscriptions);
  };

  // Tenant-specific CRUD operations
  const createBus = async (busData: Partial<Bus>): Promise<Bus | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newBus = await api.createBus({
        ...busData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newBus) {
        setBuses((prev) => [...prev, newBus as Bus]);
      }

      return newBus as Bus;
    } catch (error) {
      console.error("Error creating bus:", error);
      return null;
    }
  };

  const updateBus = async (
    id: string,
    updates: Partial<Bus>,
  ): Promise<Bus | null> => {
    try {
      const updatedBus = await api.updateBus(id, updates as any);

      if (updatedBus) {
        setBuses((prev) =>
          prev.map((bus) => (bus.id === id ? (updatedBus as Bus) : bus)),
        );
      }

      return updatedBus as Bus;
    } catch (error) {
      console.error("Error updating bus:", error);
      return null;
    }
  };

  const deleteBus = async (id: string): Promise<boolean> => {
    try {
      await api.deleteBus(id);
      setBuses((prev) => prev.filter((bus) => bus.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting bus:", error);
      return false;
    }
  };

  const createStudent = async (
    studentData: Partial<Student>,
  ): Promise<Student | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newStudent = await api.createStudent({
        ...studentData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newStudent) {
        setStudents((prev) => [...prev, newStudent as Student]);
      }

      return newStudent as Student;
    } catch (error) {
      console.error("Error creating student:", error);
      return null;
    }
  };

  const updateStudent = async (
    id: string,
    updates: Partial<Student>,
  ): Promise<Student | null> => {
    try {
      const updatedStudent = await api.updateStudent(id, updates as any);

      if (updatedStudent) {
        setStudents((prev) =>
          prev.map((student) =>
            student.id === id ? (updatedStudent as Student) : student,
          ),
        );
      }

      return updatedStudent as Student;
    } catch (error) {
      console.error("Error updating student:", error);
      return null;
    }
  };

  const deleteStudent = async (id: string): Promise<boolean> => {
    try {
      await api.deleteStudent(id);
      setStudents((prev) => prev.filter((student) => student.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting student:", error);
      return false;
    }
  };

  const createRoute = async (
    routeData: Partial<Route>,
  ): Promise<Route | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newRoute = await api.createRoute({
        ...routeData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newRoute) {
        setRoutes((prev) => [...prev, newRoute as RouteWithStops]);
      }

      return newRoute as Route;
    } catch (error) {
      console.error("Error creating route:", error);
      return null;
    }
  };

  const updateRoute = async (
    id: string,
    updates: Partial<Route>,
  ): Promise<Route | null> => {
    try {
      const updatedRoute = await api.updateRoute(id, updates as any);

      if (updatedRoute) {
        setRoutes((prev) =>
          prev.map((route) =>
            route.id === id ? (updatedRoute as RouteWithStops) : route,
          ),
        );
      }

      return updatedRoute as Route;
    } catch (error) {
      console.error("Error updating route:", error);
      return null;
    }
  };

  const deleteRoute = async (id: string): Promise<boolean> => {
    try {
      await api.deleteRoute(id);
      setRoutes((prev) => prev.filter((route) => route.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting route:", error);
      return false;
    }
  };

  // Tenant management functions for super admin
  const createTenant = async (
    tenantData: Partial<Tenant>,
  ): Promise<Tenant | null> => {
    try {
      const newTenant = await api.createSchool(tenantData as any);
      if (newTenant) {
        setTenants((prev) => [...prev, newTenant as Tenant]);
      }
      return newTenant as Tenant;
    } catch (error) {
      console.error("Error creating tenant:", error);
      return null;
    }
  };

  const updateTenant = async (
    id: string,
    updates: Partial<Tenant>,
  ): Promise<Tenant | null> => {
    try {
      const updatedTenant = await api.updateSchool(id, updates as any);
      if (updatedTenant) {
        setTenants((prev) =>
          prev.map((tenant) =>
            tenant.id === id ? (updatedTenant as Tenant) : tenant,
          ),
        );
      }
      return updatedTenant as Tenant;
    } catch (error) {
      console.error("Error updating tenant:", error);
      return null;
    }
  };

  const deleteTenant = async (id: string): Promise<boolean> => {
    try {
      await api.deleteSchool(id);
      setTenants((prev) => prev.filter((tenant) => tenant.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting tenant:", error);
      return false;
    }
  };

  const assignSchoolManager = async (
    tenantId: string,
    userId: string,
  ): Promise<boolean> => {
    try {
      console.log(
        `DatabaseContext: Assigning user ${userId} as manager for tenant ${tenantId}`,
      );

      // Find the user first to ensure they exist
      const userToUpdate = users.find((u) => u.id === userId);
      if (!userToUpdate) {
        throw new Error(
          `User with ID ${userId} not found in current users list`,
        );
      }

      console.log("DatabaseContext: Found user to update:", {
        id: userToUpdate.id,
        name: userToUpdate.name,
        email: userToUpdate.email,
        currentRole: userToUpdate.role,
        currentTenantId: userToUpdate.tenant_id,
      });

      // Use the dedicated school manager assignment function
      const { data: updatedUser, error } = await supabase.rpc(
        "assign_school_manager",
        {
          user_id: userId,
          tenant_id: tenantId,
        },
      );

      if (error) {
        console.error("DatabaseContext: Assignment failed:", error);
        throw error;
      }

      if (updatedUser) {
        console.log("DatabaseContext: User successfully updated:", updatedUser);

        // Update local state immediately
        setUsers((prev) => {
          const newUsers = prev.map((user) =>
            user.id === userId
              ? {
                  ...user,
                  tenant_id: tenantId,
                  role: "school_manager",
                  updated_at: new Date().toISOString(),
                }
              : user,
          );
          console.log(
            `DatabaseContext: Updated users in state, found user: ${newUsers.some((u) => u.id === userId)}`,
          );
          return newUsers;
        });

        // Force refresh data to ensure we have the latest from database
        setTimeout(() => {
          fetchData();
        }, 500);

        return true;
      }
      console.warn(
        "DatabaseContext: No updated user returned from assignment function",
      );
      return false;
    } catch (error) {
      console.error("DatabaseContext: Error assigning school manager:", error);
      throw error; // Propagate the error to handle it in the UI
    }
  };

  const assignBusDriver = async (
    busId: string,
    driverId: string | null,
  ): Promise<boolean> => {
    try {
      console.log(
        `DatabaseContext: Assigning driver ${driverId} to bus ${busId}`,
      );

      // Update the bus with the new driver
      const { data: updatedBus, error } = await supabase
        .from("buses")
        .update({ driver_id: driverId, updated_at: new Date().toISOString() })
        .eq("id", busId)
        .select()
        .single();

      if (error) {
        console.error("DatabaseContext: Bus driver assignment failed:", error);
        throw error;
      }

      if (updatedBus) {
        console.log(
          "DatabaseContext: Bus driver successfully updated:",
          updatedBus,
        );

        // Update local state immediately
        setBuses((prev) => {
          const newBuses = prev.map((bus) =>
            bus.id === busId
              ? {
                  ...bus,
                  driver_id: driverId,
                  updated_at: new Date().toISOString(),
                }
              : bus,
          );
          return newBuses;
        });

        // Create notification for driver assignment
        if (driverId && user?.tenant_id) {
          try {
            await supabase.from("notifications").insert({
              user_id: driverId,
              tenant_id: user.tenant_id,
              title: "Bus Assignment",
              message: `You have been assigned to bus ${updatedBus.plate_number}`,
              type: "assignment",
              priority: "normal",
              metadata: {
                type: "bus_assignment",
                busId: busId,
                plateNumber: updatedBus.plate_number,
              },
            });
          } catch (notificationError) {
            console.warn(
              "Failed to create assignment notification:",
              notificationError,
            );
          }
        }

        // Force refresh data to ensure we have the latest from database
        setTimeout(() => {
          fetchData();
        }, 500);

        return true;
      }
      console.warn(
        "DatabaseContext: No updated bus returned from assignment function",
      );
      return false;
    } catch (error) {
      console.error("DatabaseContext: Error assigning bus driver:", error);
      throw error;
    }
  };

  useEffect(() => {
    fetchData();
  }, [user?.id, user?.role, user?.tenant_id, authTenant]);

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      subscriptions.forEach((sub) => sub.unsubscribe());
    };
  }, [subscriptions]);

  return (
    <DatabaseContext.Provider
      value={{
        tenant,
        tenants,
        users,
        buses,
        routes,
        students,
        loading,
        error,
        refreshData: fetchData,
        createBus,
        updateBus,
        deleteBus,
        createStudent,
        updateStudent,
        deleteStudent,
        createRoute,
        updateRoute,
        deleteRoute,
        createTenant,
        updateTenant,
        deleteTenant,
        assignSchoolManager,
        assignBusDriver,
      }}
    >
      {children}
    </DatabaseContext.Provider>
  );
}

// Custom hook for accessing database context
const useDatabase = () => {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error("useDatabase must be used within a DatabaseProvider");
  }
  return context;
};

// Named exports for Fast Refresh compatibility
export { DatabaseProvider, useDatabase };

// Default export for compatibility
export default DatabaseProvider;
