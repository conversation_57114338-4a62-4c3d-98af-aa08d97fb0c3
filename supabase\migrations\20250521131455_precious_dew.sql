/*
  # Add Student Management and Attendance Features

  1. New Tables
    - `students`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `name` (text)
      - `grade` (text)
      - `parent_id` (uuid, foreign key)
      - `route_stop_id` (uuid, foreign key)
      - `is_active` (boolean)
      - `photo_url` (text)
      - `metadata` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `attendance`
      - `id` (uuid, primary key)
      - `tenant_id` (uuid, foreign key)
      - `student_id` (uuid, foreign key)
      - `bus_id` (uuid, foreign key)
      - `type` (enum: pickup, dropoff)
      - `location` (point)
      - `recorded_at` (timestamp)
      - `recorded_by` (uuid, foreign key)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on both tables
    - Add policies for tenant isolation
    - Add role-based access policies
*/

-- Create attendance type enum if not exists
DO $$ BEGIN
  CREATE TYPE attendance_type AS ENUM ('pickup', 'dropoff');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  name text NOT NULL,
  grade text NOT NULL,
  parent_id uuid REFERENCES users(id),
  route_stop_id uuid REFERENCES route_stops(id),
  is_active boolean DEFAULT true,
  photo_url text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create attendance table
CREATE TABLE IF NOT EXISTS attendance (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  student_id uuid NOT NULL REFERENCES students(id),
  bus_id uuid NOT NULL REFERENCES buses(id),
  type attendance_type NOT NULL,
  location geometry(Point,4326) NOT NULL,
  recorded_at timestamptz NOT NULL DEFAULT now(),
  recorded_by uuid NOT NULL REFERENCES users(id),
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for students
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'students' 
    AND policyname = 'Students are viewable by users of the same tenant'
  ) THEN
    CREATE POLICY "Students are viewable by users of the same tenant"
      ON students
      FOR SELECT
      TO authenticated
      USING (
        tenant_id IN (
          SELECT tenant_id FROM users WHERE id = auth.uid()
        )
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'students' 
    AND policyname = 'Students are manageable by school staff'
  ) THEN
    CREATE POLICY "Students are manageable by school staff"
      ON students
      FOR ALL
      TO authenticated
      USING (
        auth.uid() IN (
          SELECT id FROM users 
          WHERE tenant_id = students.tenant_id 
          AND role IN ('school_manager', 'supervisor')
        )
      );
  END IF;
END $$;

-- Create RLS policies for attendance
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'attendance' 
    AND policyname = 'Attendance records are viewable by users of the same tenant'
  ) THEN
    CREATE POLICY "Attendance records are viewable by users of the same tenant"
      ON attendance
      FOR SELECT
      TO authenticated
      USING (
        tenant_id IN (
          SELECT tenant_id FROM users WHERE id = auth.uid()
        )
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'attendance' 
    AND policyname = 'Attendance can be recorded by drivers and supervisors'
  ) THEN
    CREATE POLICY "Attendance can be recorded by drivers and supervisors"
      ON attendance
      FOR INSERT
      TO authenticated
      WITH CHECK (
        auth.uid() IN (
          SELECT id FROM users 
          WHERE tenant_id = attendance.tenant_id 
          AND role IN ('driver', 'supervisor')
        )
      );
  END IF;
END $$;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_id ON attendance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_recorded_at ON attendance(recorded_at);
CREATE INDEX IF NOT EXISTS idx_attendance_location ON attendance USING GIST(location);

-- Create updated_at trigger for students
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_students_updated_at'
  ) THEN
    CREATE TRIGGER update_students_updated_at
        BEFORE UPDATE ON students
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;