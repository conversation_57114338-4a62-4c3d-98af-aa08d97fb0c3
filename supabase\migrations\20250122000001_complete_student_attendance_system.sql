-- Complete Student Attendance System Migration
-- This migration creates all necessary tables and functions for the student attendance system

-- Create students table if not exists
CREATE TABLE IF NOT EXISTS students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    grade VARCHAR(10) NOT NULL,
    parent_id UUID REFERENCES users(id) ON DELETE SET NULL,
    route_stop_id UUID,
    photo_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create attendance table if not exists
CREATE TABLE IF NOT EXISTS attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    bus_id UUID REFERENCES buses(id) ON DELETE SET NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('pickup', 'dropoff')),
    location GEOMETRY(POINT, 4326),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    recorded_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create attendance_summary table for daily summaries
CREATE TABLE IF NOT EXISTS attendance_summary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    present BOOLEAN DEFAULT false,
    pickup_time TIMESTAMP WITH TIME ZONE,
    dropoff_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, student_id, date)
);

-- Create student_parent_relationships table for multiple parent support
CREATE TABLE IF NOT EXISTS student_parent_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) DEFAULT 'parent',
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, parent_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_students_parent_id ON students(parent_id);
CREATE INDEX IF NOT EXISTS idx_students_route_stop_id ON students(route_stop_id);
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_id ON attendance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_recorded_at ON attendance(recorded_at);
CREATE INDEX IF NOT EXISTS idx_attendance_summary_tenant_student_date ON attendance_summary(tenant_id, student_id, date);
CREATE INDEX IF NOT EXISTS idx_student_parent_relationships_student_id ON student_parent_relationships(student_id);
CREATE INDEX IF NOT EXISTS idx_student_parent_relationships_parent_id ON student_parent_relationships(parent_id);

-- Function to update attendance summary
CREATE OR REPLACE FUNCTION update_attendance_summary()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO attendance_summary (tenant_id, student_id, date, present, pickup_time, dropoff_time)
    VALUES (
        NEW.tenant_id,
        NEW.student_id,
        DATE(NEW.recorded_at),
        true,
        CASE WHEN NEW.type = 'pickup' THEN NEW.recorded_at ELSE NULL END,
        CASE WHEN NEW.type = 'dropoff' THEN NEW.recorded_at ELSE NULL END
    )
    ON CONFLICT (tenant_id, student_id, date)
    DO UPDATE SET
        present = true,
        pickup_time = CASE 
            WHEN NEW.type = 'pickup' THEN NEW.recorded_at 
            ELSE attendance_summary.pickup_time 
        END,
        dropoff_time = CASE 
            WHEN NEW.type = 'dropoff' THEN NEW.recorded_at 
            ELSE attendance_summary.dropoff_time 
        END,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for attendance summary updates
DROP TRIGGER IF EXISTS trigger_update_attendance_summary ON attendance;
CREATE TRIGGER trigger_update_attendance_summary
    AFTER INSERT ON attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_attendance_summary();

-- Function to get attendance statistics for a tenant
CREATE OR REPLACE FUNCTION get_attendance_stats(p_tenant_id UUID)
RETURNS TABLE(
    total_students INTEGER,
    total_school_days INTEGER,
    average_attendance_rate NUMERIC,
    present_today INTEGER,
    absent_today INTEGER,
    frequent_absentees INTEGER,
    perfect_attendance INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH 
        school_days_count AS (
            SELECT COUNT(DISTINCT DATE(recorded_at)) as total_days
            FROM attendance 
            WHERE tenant_id = p_tenant_id
                AND recorded_at >= CURRENT_DATE - INTERVAL '30 days'
        ),
        student_stats AS (
            SELECT 
                s.id,
                COUNT(DISTINCT DATE(a.recorded_at)) as school_days_attended
            FROM students s
            LEFT JOIN attendance a ON s.id = a.student_id 
                AND a.tenant_id = p_tenant_id
                AND a.recorded_at >= CURRENT_DATE - INTERVAL '30 days'
            WHERE s.tenant_id = p_tenant_id AND s.is_active = true
            GROUP BY s.id
        ),
        today_attendance AS (
            SELECT COUNT(DISTINCT student_id) as present_count
            FROM attendance 
            WHERE tenant_id = p_tenant_id
                AND DATE(recorded_at) = CURRENT_DATE
        )
    SELECT 
        (SELECT COUNT(*) FROM students WHERE tenant_id = p_tenant_id AND is_active = true)::INTEGER,
        (SELECT total_days FROM school_days_count)::INTEGER,
        COALESCE(
            (SELECT AVG(school_days_attended::NUMERIC / NULLIF((SELECT total_days FROM school_days_count), 0) * 100) 
             FROM student_stats), 0
        )::NUMERIC,
        COALESCE((SELECT present_count FROM today_attendance), 0)::INTEGER,
        ((SELECT COUNT(*) FROM students WHERE tenant_id = p_tenant_id AND is_active = true) - 
         COALESCE((SELECT present_count FROM today_attendance), 0))::INTEGER,
        (SELECT COUNT(*) FROM student_stats 
         WHERE school_days_attended::NUMERIC / NULLIF((SELECT total_days FROM school_days_count), 0) < 0.8)::INTEGER,
        (SELECT COUNT(*) FROM student_stats 
         WHERE school_days_attended = (SELECT total_days FROM school_days_count))::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Enable realtime for attendance tracking
alter publication supabase_realtime add table students;
alter publication supabase_realtime add table attendance;
alter publication supabase_realtime add table attendance_summary;
alter publication supabase_realtime add table student_parent_relationships;