import { supabase } from "./supabase";
import {
  sendPushNotification,
  sendPushNotificationToTenant,
} from "./pushNotifications";
import type { Tables } from "./database.types";

interface NotificationData {
  title: string;
  message: string;
  type:
    | "geofence"
    | "attendance"
    | "maintenance"
    | "announcements"
    | "emergency"
    | "route_changes";
  priority?: "low" | "normal" | "high";
  metadata?: any;
  userIds?: string[];
  tenantId: string;
  scheduleFor?: Date;
}

interface AttendanceNotificationData {
  studentId: string;
  studentName: string;
  busId: string;
  busNumber: string;
  type: "pickup" | "dropoff";
  location: string;
  timestamp: Date;
  tenantId: string;
}

interface MaintenanceNotificationData {
  busId: string;
  busNumber: string;
  maintenanceType: "routine" | "repair" | "inspection";
  description: string;
  scheduledDate: Date;
  cost?: number;
  tenantId: string;
}

interface EmergencyNotificationData {
  title: string;
  message: string;
  location?: { lat: number; lng: number };
  busId?: string;
  emergencyType: "accident" | "breakdown" | "medical" | "security" | "weather";
  tenantId: string;
}

interface RouteChangeNotificationData {
  routeId: string;
  routeName: string;
  changeType: "schedule" | "stops" | "driver" | "bus" | "cancellation";
  description: string;
  effectiveDate: Date;
  tenantId: string;
}

class NotificationService {
  // Create a notification in the database
  async createNotification(data: NotificationData): Promise<string | null> {
    try {
      const notificationData = {
        title: data.title,
        message: data.message,
        type: data.type,
        priority: data.priority || "normal",
        metadata: data.metadata || {},
        tenant_id: data.tenantId,
        read: false,
      };

      // If specific user IDs are provided, create individual notifications
      if (data.userIds && data.userIds.length > 0) {
        const notifications = data.userIds.map((userId) => ({
          ...notificationData,
          user_id: userId,
        }));

        const { data: insertedNotifications, error } = await supabase
          .from("notifications")
          .insert(notifications)
          .select();

        if (error) throw error;

        // Send push notifications
        await this.sendPushNotifications(
          data.userIds,
          {
            title: data.title,
            body: data.message,
            icon: "/bus-icon.svg",
            tag: data.type,
            data: {
              type: data.type,
              priority: data.priority,
              ...data.metadata,
            },
          },
          data.tenantId,
        );

        return insertedNotifications[0]?.id || null;
      } else {
        // Create notification for all users in tenant
        const { data: users } = await supabase
          .from("users")
          .select("id")
          .eq("tenant_id", data.tenantId)
          .eq("is_active", true);

        if (users && users.length > 0) {
          const notifications = users.map((user) => ({
            ...notificationData,
            user_id: user.id,
          }));

          const { data: insertedNotifications, error } = await supabase
            .from("notifications")
            .insert(notifications)
            .select();

          if (error) throw error;

          // Send push notifications to all users
          await this.sendPushNotifications(
            users.map((u) => u.id),
            {
              title: data.title,
              body: data.message,
              icon: "/bus-icon.svg",
              tag: data.type,
              data: {
                type: data.type,
                priority: data.priority,
                ...data.metadata,
              },
            },
            data.tenantId,
          );

          return insertedNotifications[0]?.id || null;
        }
      }

      return null;
    } catch (error) {
      console.error("Error creating notification:", error);
      throw error;
    }
  }

  // Send attendance notifications
  async sendAttendanceNotification(
    data: AttendanceNotificationData,
  ): Promise<void> {
    try {
      // Get student and parent information
      const { data: student } = await supabase
        .from("students")
        .select(
          `
          id,
          name,
          parent_id,
          users!parent_id(id, name, email)
        `,
        )
        .eq("id", data.studentId)
        .single();

      if (!student || !student.parent_id) {
        console.warn(`No parent found for student ${data.studentName}`);
        return;
      }

      const actionText = data.type === "pickup" ? "picked up" : "dropped off";
      const title = `Student ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`;
      const message = `${data.studentName} has been ${actionText} at ${data.location} by bus ${data.busNumber}`;

      await this.createNotification({
        title,
        message,
        type: "attendance",
        priority: "normal",
        userIds: [student.parent_id],
        tenantId: data.tenantId,
        metadata: {
          studentId: data.studentId,
          studentName: data.studentName,
          busId: data.busId,
          busNumber: data.busNumber,
          attendanceType: data.type,
          location: data.location,
          timestamp: data.timestamp.toISOString(),
        },
      });

      // Also notify school administrators
      const { data: admins } = await supabase
        .from("users")
        .select("id")
        .eq("tenant_id", data.tenantId)
        .in("role", ["admin", "school_manager", "supervisor"])
        .eq("is_active", true);

      if (admins && admins.length > 0) {
        await this.createNotification({
          title: `Attendance Update - ${data.studentName}`,
          message: `${data.studentName} ${actionText} at ${data.location}`,
          type: "attendance",
          priority: "low",
          userIds: admins.map((a) => a.id),
          tenantId: data.tenantId,
          metadata: {
            studentId: data.studentId,
            studentName: data.studentName,
            busId: data.busId,
            busNumber: data.busNumber,
            attendanceType: data.type,
            location: data.location,
            timestamp: data.timestamp.toISOString(),
            isAdminNotification: true,
          },
        });
      }
    } catch (error) {
      console.error("Error sending attendance notification:", error);
      throw error;
    }
  }

  // Send maintenance notifications
  async sendMaintenanceNotification(
    data: MaintenanceNotificationData,
  ): Promise<void> {
    try {
      const title = `Bus Maintenance - ${data.busNumber}`;
      const message = `${data.maintenanceType.charAt(0).toUpperCase() + data.maintenanceType.slice(1)} maintenance scheduled for bus ${data.busNumber}: ${data.description}`;

      // Notify maintenance staff and administrators
      const { data: users } = await supabase
        .from("users")
        .select("id")
        .eq("tenant_id", data.tenantId)
        .in("role", ["admin", "school_manager", "supervisor"])
        .eq("is_active", true);

      if (users && users.length > 0) {
        await this.createNotification({
          title,
          message,
          type: "maintenance",
          priority: data.maintenanceType === "repair" ? "high" : "normal",
          userIds: users.map((u) => u.id),
          tenantId: data.tenantId,
          metadata: {
            busId: data.busId,
            busNumber: data.busNumber,
            maintenanceType: data.maintenanceType,
            description: data.description,
            scheduledDate: data.scheduledDate.toISOString(),
            cost: data.cost,
          },
        });
      }

      // If it's urgent maintenance, also notify drivers
      if (data.maintenanceType === "repair") {
        const { data: drivers } = await supabase
          .from("users")
          .select("id")
          .eq("tenant_id", data.tenantId)
          .eq("role", "driver")
          .eq("is_active", true);

        if (drivers && drivers.length > 0) {
          await this.createNotification({
            title: `Urgent: Bus ${data.busNumber} Maintenance`,
            message: `Bus ${data.busNumber} requires immediate maintenance. Please check with administration before use.`,
            type: "maintenance",
            priority: "high",
            userIds: drivers.map((d) => d.id),
            tenantId: data.tenantId,
            metadata: {
              busId: data.busId,
              busNumber: data.busNumber,
              maintenanceType: data.maintenanceType,
              description: data.description,
              isUrgent: true,
            },
          });
        }
      }
    } catch (error) {
      console.error("Error sending maintenance notification:", error);
      throw error;
    }
  }

  // Send emergency notifications
  async sendEmergencyNotification(
    data: EmergencyNotificationData,
  ): Promise<void> {
    try {
      await this.createNotification({
        title: `🚨 EMERGENCY: ${data.title}`,
        message: data.message,
        type: "emergency",
        priority: "high",
        tenantId: data.tenantId,
        metadata: {
          emergencyType: data.emergencyType,
          location: data.location,
          busId: data.busId,
          timestamp: new Date().toISOString(),
        },
      });

      // Send immediate push notification to all users except students
      const { data: users } = await supabase
        .from("users")
        .select("id")
        .eq("tenant_id", data.tenantId)
        .neq("role", "student")
        .eq("is_active", true);

      if (users && users.length > 0) {
        await sendPushNotification(
          users.map((u) => u.id),
          {
            title: `🚨 EMERGENCY: ${data.title}`,
            body: data.message,
            icon: "/bus-icon.svg",
            tag: "emergency",
            requireInteraction: true,
            data: {
              type: "emergency",
              emergencyType: data.emergencyType,
              location: data.location,
              busId: data.busId,
              priority: "high",
            },
          },
          data.tenantId,
        );
      }
    } catch (error) {
      console.error("Error sending emergency notification:", error);
      throw error;
    }
  }

  // Send route change notifications
  async sendRouteChangeNotification(
    data: RouteChangeNotificationData,
  ): Promise<void> {
    try {
      const title = `Route Update - ${data.routeName}`;
      const message = `${data.description} Effective: ${data.effectiveDate.toLocaleDateString()}`;

      // Get all students and parents affected by this route
      const { data: students } = await supabase
        .from("students")
        .select(
          `
          id,
          name,
          parent_id,
          route_stops!inner(
            route_id
          )
        `,
        )
        .eq("route_stops.route_id", data.routeId)
        .eq("is_active", true);

      const parentIds =
        students?.filter((s) => s.parent_id).map((s) => s.parent_id!) || [];

      if (parentIds.length > 0) {
        await this.createNotification({
          title,
          message,
          type: "route_changes",
          priority: data.changeType === "cancellation" ? "high" : "normal",
          userIds: parentIds,
          tenantId: data.tenantId,
          metadata: {
            routeId: data.routeId,
            routeName: data.routeName,
            changeType: data.changeType,
            description: data.description,
            effectiveDate: data.effectiveDate.toISOString(),
            affectedStudents: students?.map((s) => ({
              id: s.id,
              name: s.name,
            })),
          },
        });
      }

      // Also notify administrators and drivers
      const { data: staff } = await supabase
        .from("users")
        .select("id")
        .eq("tenant_id", data.tenantId)
        .in("role", ["admin", "school_manager", "supervisor", "driver"])
        .eq("is_active", true);

      if (staff && staff.length > 0) {
        await this.createNotification({
          title: `Staff Alert: ${title}`,
          message: `Route change notification sent to parents. ${message}`,
          type: "route_changes",
          priority: "normal",
          userIds: staff.map((s) => s.id),
          tenantId: data.tenantId,
          metadata: {
            routeId: data.routeId,
            routeName: data.routeName,
            changeType: data.changeType,
            description: data.description,
            effectiveDate: data.effectiveDate.toISOString(),
            isStaffNotification: true,
            affectedParentsCount: parentIds.length,
          },
        });
      }
    } catch (error) {
      console.error("Error sending route change notification:", error);
      throw error;
    }
  }

  // Send bus delay notifications
  async sendBusDelayNotification({
    busId,
    busNumber,
    routeId,
    routeName,
    delayMinutes,
    reason,
    estimatedArrival,
    tenantId,
  }: {
    busId: string;
    busNumber: string;
    routeId: string;
    routeName: string;
    delayMinutes: number;
    reason: string;
    estimatedArrival: Date;
    tenantId: string;
  }): Promise<void> {
    try {
      const title = `Bus Delay - ${busNumber}`;
      const message = `Bus ${busNumber} on route ${routeName} is delayed by ${delayMinutes} minutes. ${reason}. New estimated arrival: ${estimatedArrival.toLocaleTimeString()}`;

      // Get all students and parents on this route
      const { data: students } = await supabase
        .from("students")
        .select(
          `
          id,
          name,
          parent_id,
          route_stops!inner(
            route_id
          )
        `,
        )
        .eq("route_stops.route_id", routeId)
        .eq("is_active", true);

      const parentIds =
        students?.filter((s) => s.parent_id).map((s) => s.parent_id!) || [];

      if (parentIds.length > 0) {
        await this.createNotification({
          title,
          message,
          type: "geofence",
          priority: delayMinutes > 15 ? "high" : "normal",
          userIds: parentIds,
          tenantId,
          metadata: {
            busId,
            busNumber,
            routeId,
            routeName,
            delayMinutes,
            reason,
            estimatedArrival: estimatedArrival.toISOString(),
            notificationType: "delay",
          },
        });
      }

      // Record delay in database
      await supabase.from("route_delays").insert({
        route_id: routeId,
        bus_id: busId,
        tenant_id: tenantId,
        date: new Date().toISOString().split("T")[0],
        delay_minutes: delayMinutes,
        reason,
      });
    } catch (error) {
      console.error("Error sending bus delay notification:", error);
      throw error;
    }
  }

  // Send custom announcement
  async sendAnnouncement({
    title,
    message,
    targetRoles,
    priority = "normal",
    tenantId,
    scheduleFor,
  }: {
    title: string;
    message: string;
    targetRoles?: string[];
    priority?: "low" | "normal" | "high";
    tenantId: string;
    scheduleFor?: Date;
  }): Promise<void> {
    try {
      let userIds: string[] = [];

      if (targetRoles && targetRoles.length > 0) {
        const { data: users } = await supabase
          .from("users")
          .select("id")
          .eq("tenant_id", tenantId)
          .in("role", targetRoles)
          .eq("is_active", true);

        userIds = users?.map((u) => u.id) || [];
      }

      await this.createNotification({
        title,
        message,
        type: "announcements",
        priority,
        userIds: userIds.length > 0 ? userIds : undefined,
        tenantId,
        scheduleFor,
        metadata: {
          targetRoles,
          isAnnouncement: true,
          scheduledFor: scheduleFor?.toISOString(),
        },
      });
    } catch (error) {
      console.error("Error sending announcement:", error);
      throw error;
    }
  }

  // Send low rating notification
  async sendLowRatingNotification(
    tenantId: string,
    targetType: string,
    targetId: string,
    rating: number,
    threshold: number = 2,
  ): Promise<void> {
    if (rating > threshold) return;

    try {
      // Get supervisors and managers
      const { data: users } = await supabase
        .from("users")
        .select("id, name, email")
        .eq("tenant_id", tenantId)
        .in("role", ["supervisor", "school_manager", "admin"]);

      if (!users || users.length === 0) return;

      const targetName = await this.getTargetName(targetType, targetId);

      const notifications = users.map((user) => ({
        user_id: user.id,
        tenant_id: tenantId,
        title: "Low Rating Alert",
        message: `${targetName} received a low rating (${rating}/5). Please review and take appropriate action.`,
        type: "low_rating",
        priority: "high" as const,
        metadata: {
          target_type: targetType,
          target_id: targetId,
          rating,
          threshold,
        },
      }));

      await supabase.from("notifications").insert(notifications);

      // Send push notifications
      const userIds = users.map((user) => user.id);
      await this.sendPushNotifications(
        userIds,
        {
          title: "Low Rating Alert",
          body: `${targetName} received a low rating (${rating}/5)`,
          icon: "/bus-icon.svg",
          tag: `low-rating-${targetType}-${targetId}`,
          data: {
            type: "low_rating",
            target_type: targetType,
            target_id: targetId,
            rating,
          },
          requireInteraction: true,
        },
        tenantId,
      );
    } catch (error) {
      console.error("Error sending low rating notification:", error);
    }
  }

  // Get target name for notifications
  private async getTargetName(
    targetType: string,
    targetId: string,
  ): Promise<string> {
    try {
      switch (targetType) {
        case "driver":
          const { data: driver } = await supabase
            .from("users")
            .select("name")
            .eq("id", targetId)
            .single();
          return driver?.name || "Driver";

        case "route":
          const { data: route } = await supabase
            .from("routes")
            .select("name")
            .eq("id", targetId)
            .single();
          return route?.name || "Route";

        case "service":
          return "Transport Service";

        default:
          return targetType;
      }
    } catch (error) {
      console.error("Error getting target name:", error);
      return targetType;
    }
  }

  // Helper method to send push notifications
  private async sendPushNotifications(
    userIds: string[],
    payload: any,
    tenantId: string,
  ): Promise<void> {
    try {
      await sendPushNotification(userIds, payload, tenantId);
    } catch (error) {
      console.error("Error sending push notifications:", error);
      // Don't throw error as notification was already saved to database
    }
  }

  // Get notification templates
  async getNotificationTemplates(
    tenantId: string,
    type?: string,
  ): Promise<any[]> {
    try {
      let query = supabase
        .from("notification_templates")
        .select("*")
        .eq("tenant_id", tenantId)
        .eq("is_active", true)
        .order("created_at", { ascending: false });

      if (type) {
        query = query.eq("type", type);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error("Error fetching notification templates:", error);
      return [];
    }
  }

  // Send notification using template
  async sendNotificationFromTemplate(
    templateId: string,
    variables: Record<string, string>,
    userIds?: string[],
  ): Promise<void> {
    try {
      const { data: template, error } = await supabase
        .from("notification_templates")
        .select("*")
        .eq("id", templateId)
        .single();

      if (error) throw error;
      if (!template) throw new Error("Template not found");

      // Replace variables in title and message
      let title = template.title;
      let message = template.message;

      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        title = title.replace(new RegExp(placeholder, "g"), value);
        message = message.replace(new RegExp(placeholder, "g"), value);
      });

      await this.createNotification({
        title,
        message,
        type: template.type as any,
        priority: "normal",
        userIds,
        tenantId: template.tenant_id,
        metadata: {
          templateId,
          variables,
          isFromTemplate: true,
        },
      });
    } catch (error) {
      console.error("Error sending notification from template:", error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
export default notificationService;
