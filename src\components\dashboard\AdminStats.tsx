import React, { useState, useEffect, useCallback } from "react";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import { StatCard } from "./StatCard";
import {
  Users,
  Bus,
  Route,
  GraduationCap,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Shield,
} from "lucide-react";
import { UserRole } from "../../types";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission } from "../../lib/rbac";
import { RBACSecurityAudit } from "../../lib/rbacAudit";
import { DatabaseSecurityAudit } from "../../lib/databaseSecurityAudit";

interface AdminStatsData {
  totalUsers: number;
  totalBuses: number;
  totalRoutes: number;
  totalStudents: number;
  activeUsers: number;
  activeBuses: number;
  activeRoutes: number;
  activeStudents: number;
  usersByRole: Record<string, number>;
  recentActivity: any[];
  securityScore?: number;
  complianceStatus?: string;
  criticalAlerts?: number;
}

export const AdminStats: React.FC = () => {
  const { users, buses, routes, students, loading, error } = useDatabase();
  const { user } = useAuth();
  const { hasPermission, isAdmin } = usePermissions();
  const [stats, setStats] = useState<AdminStatsData>({
    totalUsers: 0,
    totalBuses: 0,
    totalRoutes: 0,
    totalStudents: 0,
    activeUsers: 0,
    activeBuses: 0,
    activeRoutes: 0,
    activeStudents: 0,
    usersByRole: {},
    recentActivity: [],
  });
  const [lastFetch, setLastFetch] = useState<number>(0);
  const [cachedStats, setCachedStats] = useState<AdminStatsData | null>(null);
  const [securityAudit, setSecurityAudit] = useState<any>(null);

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;

  // Memoized calculation function
  const calculateStats = useCallback((): AdminStatsData => {
    // Performance optimization: Use Map for faster lookups
    const userRoleMap = new Map<string, number>();
    let activeUserCount = 0;

    users.forEach((user) => {
      if (user.is_active) activeUserCount++;
      const role = user.role;
      userRoleMap.set(role, (userRoleMap.get(role) || 0) + 1);
    });

    const usersByRole = Object.fromEntries(userRoleMap);

    const activeBusCount = buses.filter((bus) => bus.is_active).length;
    const activeRouteCount = routes.filter((route) => route.is_active).length;
    const activeStudentCount = students.filter(
      (student) => student.is_active,
    ).length;

    // Generate security metrics for admin users
    let securityScore = 0;
    let complianceStatus = "unknown";
    let criticalAlerts = 0;

    if (isAdmin) {
      const auditReport = RBACSecurityAudit.generateAuditReport();
      const dbValidation = DatabaseSecurityAudit.validateSchemaForRBAC();

      securityScore = auditReport.overallSecurityScore;
      complianceStatus = auditReport.complianceStatus;
      criticalAlerts = auditReport.realTimeThreats?.length || 0;
    }

    return {
      totalUsers: users.length,
      totalBuses: buses.length,
      totalRoutes: routes.length,
      totalStudents: students.length,
      activeUsers: activeUserCount,
      activeBuses: activeBusCount,
      activeRoutes: activeRouteCount,
      activeStudents: activeStudentCount,
      usersByRole,
      recentActivity: [],
      securityScore,
      complianceStatus,
      criticalAlerts,
    };
  }, [users, buses, routes, students, isAdmin]);

  useEffect(() => {
    const now = Date.now();

    // Use cached data if available and not expired
    if (cachedStats && now - lastFetch < CACHE_DURATION) {
      setStats(cachedStats);
      return;
    }

    if (
      users.length > 0 ||
      buses.length > 0 ||
      routes.length > 0 ||
      students.length > 0
    ) {
      const newStats = calculateStats();
      setCachedStats(newStats);
      setLastFetch(now);
      setStats(newStats);
    }
  }, [users, buses, routes, students, cachedStats, lastFetch, calculateStats]);

  // Generate security audit for admin users
  useEffect(() => {
    if (isAdmin && !securityAudit) {
      const audit = RBACSecurityAudit.generateAuditReport();
      setSecurityAudit(audit);
    }
  }, [isAdmin, securityAudit]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 h-32 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
          <p className="text-red-800 dark:text-red-200">
            Error loading statistics: {error}
          </p>
        </div>
      </div>
    );
  }

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "text-green-600";
      case "needs_attention":
        return "text-yellow-600";
      case "critical":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Users Statistics */}
        {hasPermission(Permission.VIEW_ALL_USERS) ||
        hasPermission(Permission.VIEW_TENANT_USERS) ? (
          <StatCard
            title={isAdmin ? "Total Users (All Schools)" : "Users"}
            value={stats.totalUsers}
            icon={<Users className="h-6 w-6" />}
            trend={{
              value: stats.activeUsers,
              label: "Active",
              isPositive: stats.activeUsers > 0,
            }}
            color="blue"
          />
        ) : null}

        {/* Buses Statistics */}
        {hasPermission(Permission.VIEW_ALL_BUSES) ||
        hasPermission(Permission.VIEW_TENANT_BUSES) ? (
          <StatCard
            title={isAdmin ? "Total Buses (All Schools)" : "Buses"}
            value={stats.totalBuses}
            icon={<Bus className="h-6 w-6" />}
            trend={{
              value: stats.activeBuses,
              label: "Active",
              isPositive: stats.activeBuses > 0,
            }}
            color="green"
          />
        ) : null}

        {/* Routes Statistics */}
        {hasPermission(Permission.VIEW_ALL_ROUTES) ||
        hasPermission(Permission.VIEW_TENANT_ROUTES) ? (
          <StatCard
            title={isAdmin ? "Total Routes (All Schools)" : "Routes"}
            value={stats.totalRoutes}
            icon={<Route className="h-6 w-6" />}
            trend={{
              value: stats.activeRoutes,
              label: "Active",
              isPositive: stats.activeRoutes > 0,
            }}
            color="purple"
          />
        ) : null}

        {/* Students Statistics */}
        {hasPermission(Permission.VIEW_ALL_STUDENTS) ||
        hasPermission(Permission.VIEW_TENANT_STUDENTS) ? (
          <StatCard
            title={isAdmin ? "Total Students (All Schools)" : "Students"}
            value={stats.totalStudents}
            icon={<GraduationCap className="h-6 w-6" />}
            trend={{
              value: stats.activeStudents,
              label: "Active",
              isPositive: stats.activeStudents > 0,
            }}
            color="orange"
          />
        ) : null}
      </div>

      {/* Security Dashboard for Admin Users */}
      {isAdmin && stats.securityScore !== undefined && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard
            title="Security Score"
            value={`${stats.securityScore}/100`}
            icon={<Shield className="h-6 w-6" />}
            trend={{
              value: stats.securityScore,
              label: "Overall Security",
              isPositive: stats.securityScore >= 80,
            }}
            color={
              stats.securityScore >= 80
                ? "green"
                : stats.securityScore >= 60
                  ? "yellow"
                  : "red"
            }
            className={getSecurityScoreColor(stats.securityScore)}
          />

          <StatCard
            title="Compliance Status"
            value={
              stats.complianceStatus?.replace("_", " ").toUpperCase() ||
              "UNKNOWN"
            }
            icon={<CheckCircle className="h-6 w-6" />}
            trend={{
              value:
                stats.complianceStatus === "compliant"
                  ? 100
                  : stats.complianceStatus === "needs_attention"
                    ? 70
                    : 30,
              label: "Compliance Level",
              isPositive: stats.complianceStatus === "compliant",
            }}
            color={
              stats.complianceStatus === "compliant"
                ? "green"
                : stats.complianceStatus === "needs_attention"
                  ? "yellow"
                  : "red"
            }
            className={getComplianceStatusColor(stats.complianceStatus || "")}
          />

          <StatCard
            title="Critical Alerts"
            value={stats.criticalAlerts || 0}
            icon={<AlertTriangle className="h-6 w-6" />}
            trend={{
              value: stats.criticalAlerts || 0,
              label: "Active Threats",
              isPositive: (stats.criticalAlerts || 0) === 0,
            }}
            color={(stats.criticalAlerts || 0) > 0 ? "red" : "green"}
          />
        </div>
      )}

      {/* User Role Distribution for Admin */}
      {isAdmin && Object.keys(stats.usersByRole).length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            User Distribution by Role
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(stats.usersByRole).map(([role, count]) => {
              const roleNames: Record<string, string> = {
                [UserRole.ADMIN]: "Admins",
                [UserRole.SCHOOL_MANAGER]: "School Managers",
                [UserRole.SUPERVISOR]: "Supervisors",
                [UserRole.DRIVER]: "Drivers",
                [UserRole.PARENT]: "Parents",
                [UserRole.STUDENT]: "Students",
              };

              const roleColors: Record<string, string> = {
                [UserRole.ADMIN]:
                  "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
                [UserRole.SCHOOL_MANAGER]:
                  "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                [UserRole.SUPERVISOR]:
                  "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                [UserRole.DRIVER]:
                  "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                [UserRole.PARENT]:
                  "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
                [UserRole.STUDENT]:
                  "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
              };

              return (
                <div key={role} className="text-center">
                  <div
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${roleColors[role] || "bg-gray-100 text-gray-800"}`}
                  >
                    {roleNames[role] || role}
                  </div>
                  <div className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
                    {count}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Security Recommendations for Admin */}
      {isAdmin && securityAudit?.actionableItems && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Security Recommendations
          </h3>

          {securityAudit.actionableItems.immediate.length > 0 && (
            <div className="mb-4">
              <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
                🚨 Immediate Actions Required
              </h4>
              <ul className="space-y-1">
                {securityAudit.actionableItems.immediate
                  .slice(0, 3)
                  .map((item: string, index: number) => (
                    <li
                      key={index}
                      className="text-sm text-red-700 dark:text-red-300 flex items-start"
                    >
                      <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
              </ul>
            </div>
          )}

          {securityAudit.actionableItems.shortTerm.length > 0 && (
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                ⚠️ Short-term Improvements
              </h4>
              <ul className="space-y-1">
                {securityAudit.actionableItems.shortTerm
                  .slice(0, 3)
                  .map((item: string, index: number) => (
                    <li
                      key={index}
                      className="text-sm text-yellow-700 dark:text-yellow-300 flex items-start"
                    >
                      <Clock className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminStats;
