/*
  # Fix users table RLS policies

  1. Changes
    - Remove recursive policy for users table
    - Add new policies for different user roles:
      - <PERSON><PERSON> can view all users
      - Users can view their own record
      - Users can view other users in their tenant
  
  2. Security
    - Maintains RLS protection
    - Prevents infinite recursion
    - Ensures proper access control based on user roles and tenant
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users are viewable by users of the same tenant" ON users;
DROP POLICY IF EXISTS "Users can update their own record" ON users;
DROP POLICY IF EXISTS "Allow insert for anyone" ON users;

-- Create new policies
CREATE POLICY "Ad<PERSON> can view all users"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Users can view own record"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can view users in same tenant"
  ON users
  FOR SELECT
  TO authenticated
  USING (
    tenant_id IS NOT NULL 
    AND tenant_id = (
      SELECT tenant_id 
      FROM users 
      WHERE id = auth.uid()
    )
  );

CREATE POLICY "Users can update own record"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Allow signup"
  ON users
  FOR INSERT
  TO public
  WITH CHECK (true);