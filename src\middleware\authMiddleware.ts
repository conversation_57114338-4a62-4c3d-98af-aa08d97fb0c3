import { User, UserRole } from "../types";
import {
  RBACManager,
  Permission,
  DataScope,
  ResourceType,
  Action,
  PermissionContext,
} from "../lib/rbac";
import { RBACSecurityAudit, EnhancedRoleDefinition } from "../lib/rbacAudit";
import { DatabaseSecurityAudit } from "../lib/databaseSecurityAudit";

/**
 * Enhanced Security Middleware with Comprehensive RBAC
 * Implements multi-layered security controls for multi-tenant architecture
 */
export class AuthMiddleware {
  private static securityConfig =
    DatabaseSecurityAudit.getRecommendedSecurityConfig();
  private static auditEnabled = true;
  private static rateLimitCache = new Map<
    string,
    { count: number; resetTime: number }
  >();
  /**
   * Enhanced resource access control with comprehensive security checks
   */
  static checkResourceAccess(
    user: User,
    resource: ResourceType,
    action: Action,
    context?: {
      resourceId?: string;
      resourceOwnerId?: string;
      resourceTenantId?: string;
      resourceData?: any;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): { allowed: boolean; error?: string; securityLevel?: string } {
    if (!user) {
      this.logSecurityEvent(
        "unauthorized_access_attempt",
        "medium",
        "Unauthenticated user attempted resource access",
        {
          resource,
          action,
          ipAddress: context?.ipAddress,
          userAgent: context?.userAgent,
        },
      );
      return {
        allowed: false,
        error: "User not authenticated",
        securityLevel: "high",
      };
    }

    // Enhanced security checks
    const securityChecks = this.performSecurityChecks(user, context);
    if (!securityChecks.passed) {
      return {
        allowed: false,
        error: securityChecks.error,
        securityLevel: securityChecks.level,
      };
    }

    const userRole = user.role as UserRole;
    const permissionContext = RBACManager.createPermissionContext(
      user.id,
      user.tenant_id || undefined,
      context?.resourceOwnerId,
      context?.resourceTenantId,
      context?.resourceData,
    );

    // Check rate limiting
    const rateLimitCheck = this.checkRateLimit(
      user.id,
      `${resource}_${action}`,
      this.getRateLimitForAction(action),
      1, // 1 minute window
    );

    if (!rateLimitCheck.allowed) {
      this.logSecurityEvent(
        "rate_limit_exceeded",
        "high",
        "User exceeded rate limit",
        {
          userId: user.id,
          resource,
          action,
          remainingRequests: rateLimitCheck.remainingRequests,
        },
      );
      return {
        allowed: false,
        error: "Rate limit exceeded. Please try again later.",
        securityLevel: "medium",
      };
    }

    const hasPermission = RBACManager.checkPermissionWithLogging(
      userRole,
      resource,
      action,
      permissionContext,
    );

    if (!hasPermission) {
      this.logSecurityEvent(
        "permission_denied",
        "medium",
        "User attempted unauthorized action",
        {
          userId: user.id,
          userRole,
          resource,
          action,
          tenantId: user.tenant_id,
        },
      );
      return {
        allowed: false,
        error: this.getAccessDeniedMessage(user, resource, action),
        securityLevel: "medium",
      };
    }

    // Log successful access for audit
    if (this.auditEnabled) {
      this.logSecurityEvent(
        "resource_access_granted",
        "low",
        "User accessed resource",
        {
          userId: user.id,
          userRole,
          resource,
          action,
          tenantId: user.tenant_id,
          resourceId: context?.resourceId,
        },
      );
    }

    return { allowed: true, securityLevel: "low" };
  }

  /**
   * فحص صلاحية تعديل المستخدم
   */
  static checkUserEditPermission(
    currentUser: User,
    targetUser: User,
  ): { allowed: boolean; error?: string } {
    if (!currentUser) {
      return { allowed: false, error: "المستخدم غير مصرح له" };
    }

    const currentRole = currentUser.role as UserRole;
    const targetRole = targetUser.role as UserRole;

    // المستخدم يمكنه تعديل بياناته الشخصية
    if (currentUser.id === targetUser.id) {
      return { allowed: true };
    }

    // فحص التسلسل الهرمي للأدوار
    if (!RBACManager.canManageRole(currentRole, targetRole)) {
      return {
        allowed: false,
        error: `لا يمكن لـ ${this.getRoleName(currentRole)} إدارة ${this.getRoleName(targetRole)}`,
      };
    }

    // فحص نطاق المدرسة
    if (currentRole !== UserRole.ADMIN) {
      if (currentUser.tenant_id !== targetUser.tenant_id) {
        return {
          allowed: false,
          error: "لا يمكن إدارة مستخدمين من مدارس أخرى",
        };
      }
    }

    return { allowed: true };
  }

  /**
   * فحص صلاحية حذف المستخدم
   */
  static checkUserDeletePermission(
    currentUser: User,
    targetUser: User,
  ): { allowed: boolean; error?: string } {
    if (!currentUser) {
      return { allowed: false, error: "المستخدم غير مصرح له" };
    }

    // المستخدم لا يمكنه حذف نفسه
    if (currentUser.id === targetUser.id) {
      return { allowed: false, error: "لا يمكن حذف الحساب الشخصي" };
    }

    return this.checkUserEditPermission(currentUser, targetUser);
  }

  /**
   * فحص صلاحية إنشاء مستخدم بدور معين
   */
  static checkUserCreatePermission(
    currentUser: User,
    targetRole: UserRole,
    targetTenantId?: string,
  ): { allowed: boolean; error?: string } {
    if (!currentUser) {
      return { allowed: false, error: "المستخدم غير مصرح له" };
    }

    const currentRole = currentUser.role as UserRole;

    // فحص التسلسل الهرمي للأدوار
    if (!RBACManager.canManageRole(currentRole, targetRole)) {
      return {
        allowed: false,
        error: `لا يمكن لـ ${this.getRoleName(currentRole)} إنشاء ${this.getRoleName(targetRole)}`,
      };
    }

    // فحص نطاق المدرسة
    if (currentRole !== UserRole.ADMIN && targetTenantId) {
      if (currentUser.tenant_id !== targetTenantId) {
        return {
          allowed: false,
          error: "لا يمكن إنشاء مستخدمين في مدارس أخرى",
        };
      }
    }

    return { allowed: true };
  }

  /**
   * فلترة البيانات حسب صلاحيات المستخدم
   */
  static filterDataByPermissions<T extends { tenant_id?: string; id?: string }>(
    user: User | null,
    data: T[],
    resourceType: ResourceType,
    ownerIdField?: keyof T,
  ): T[] {
    if (!user) return [];

    const userRole = user.role as UserRole;

    // الأدمن يرى كل البيانات بدون أي فلترة
    if (userRole === UserRole.ADMIN) {
      return data;
    }

    // فحص نطاق البيانات المسموح
    const allowedScopes = RBACManager.getRoleDataScopes(userRole);

    return data.filter((item) => {
      // البيانات الشخصية
      if (
        allowedScopes.includes(DataScope.PERSONAL) &&
        ownerIdField &&
        item[ownerIdField] === user.id
      ) {
        return true;
      }

      // البيانات على مستوى المدرسة
      if (
        allowedScopes.includes(DataScope.TENANT) &&
        item.tenant_id === user.tenant_id
      ) {
        return true;
      }

      // البيانات المخصصة (للسائقين مثلاً)
      if (allowedScopes.includes(DataScope.ASSIGNED)) {
        return this.isAssignedData(user, item, resourceType);
      }

      // بيانات الأطفال (لأولياء الأمور)
      if (
        allowedScopes.includes(DataScope.CHILDREN) &&
        resourceType === ResourceType.STUDENT
      ) {
        return (item as any).parent_id === user.id;
      }

      return false;
    });
  }

  /**
   * فحص ما إذا كانت البيانات مخصصة للمستخدم
   */
  private static isAssignedData<T extends { tenant_id?: string; id?: string }>(
    user: User,
    item: T,
    resourceType: ResourceType,
  ): boolean {
    switch (resourceType) {
      case ResourceType.BUS:
        return (item as any).driver_id === user.id;
      case ResourceType.ROUTE:
        return (item as any).bus?.driver_id === user.id;
      case ResourceType.STUDENT:
        return (item as any).route_stop?.route?.bus?.driver_id === user.id;
      default:
        return false;
    }
  }

  /**
   * إنشاء سجل تدقيق للعمليات
   */
  static createAuditLog(
    user: User,
    action: string,
    resource: string,
    resourceId?: string,
    details?: any,
  ): void {
    const auditLog = {
      userId: user.id,
      userRole: user.role,
      tenantId: user.tenant_id,
      action,
      resource,
      resourceId,
      details,
      timestamp: new Date().toISOString(),
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent(),
    };

    // في التطبيق الحقيقي، يجب حفظ سجل التدقيق في قاعدة البيانات
    console.log("Audit Log:", auditLog);

    // يمكن إرسال السجل إلى خدمة التدقيق الخارجية
    this.sendToAuditService(auditLog);
  }

  /**
   * الحصول على عنوان IP للعميل
   */
  private static getClientIP(): string {
    // في بيئة المتصفح، لا يمكن الحصول على IP الحقيقي
    return "unknown";
  }

  /**
   * الحصول على معلومات المتصفح
   */
  private static getUserAgent(): string {
    if (typeof navigator !== "undefined") {
      return navigator.userAgent;
    }
    return "unknown";
  }

  /**
   * إرسال سجل التدقيق إلى خدمة خارجية
   */
  private static sendToAuditService(auditLog: any): void {
    try {
      // في التطبيق الحقيقي، يمكن إرسال السجل إلى خدمة مثل Elasticsearch أو CloudWatch
      // fetch('/api/audit-logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(auditLog)
      // });
    } catch (error) {
      console.error("Failed to send audit log:", error);
    }
  }

  /**
   * التحقق من صحة البيانات المدخلة
   */
  static validateInput(
    data: any,
    rules: Record<
      string,
      {
        required?: boolean;
        type?: "string" | "number" | "boolean" | "email" | "array" | "object";
        minLength?: number;
        maxLength?: number;
        pattern?: RegExp;
        allowedValues?: any[];
        customValidator?: (value: any) => boolean;
      }
    >,
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const [field, rule] of Object.entries(rules)) {
      const value = data[field];

      // فحص الحقول المطلوبة
      if (
        rule.required &&
        (value === undefined || value === null || value === "")
      ) {
        errors.push(`${field} مطلوب`);
        continue;
      }

      // إذا كانت القيمة فارغة وليست مطلوبة، تجاهل باقي الفحوصات
      if (value === undefined || value === null || value === "") {
        continue;
      }

      // فحص نوع البيانات
      if (rule.type) {
        switch (rule.type) {
          case "string":
            if (typeof value !== "string") {
              errors.push(`${field} يجب أن يكون نص`);
            }
            break;
          case "number":
            if (typeof value !== "number" || isNaN(value)) {
              errors.push(`${field} يجب أن يكون رقم صحيح`);
            }
            break;
          case "boolean":
            if (typeof value !== "boolean") {
              errors.push(`${field} يجب أن يكون قيمة منطقية`);
            }
            break;
          case "email":
            if (
              typeof value !== "string" ||
              !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
            ) {
              errors.push(`${field} يجب أن يكون بريد إلكتروني صحيح`);
            }
            break;
          case "array":
            if (!Array.isArray(value)) {
              errors.push(`${field} يجب أن يكون مصفوفة`);
            }
            break;
          case "object":
            if (typeof value !== "object" || Array.isArray(value)) {
              errors.push(`${field} يجب أن يكون كائن`);
            }
            break;
        }
      }

      // فحص الطول الأدنى
      if (
        rule.minLength &&
        typeof value === "string" &&
        value.length < rule.minLength
      ) {
        errors.push(`${field} يجب أن يكون على الأقل ${rule.minLength} أحرف`);
      }

      // فحص الطول الأقصى
      if (
        rule.maxLength &&
        typeof value === "string" &&
        value.length > rule.maxLength
      ) {
        errors.push(`${field} يجب أن يكون على الأكثر ${rule.maxLength} أحرف`);
      }

      // فحص النمط
      if (
        rule.pattern &&
        typeof value === "string" &&
        !rule.pattern.test(value)
      ) {
        errors.push(`${field} تنسيق غير صحيح`);
      }

      // فحص القيم المسموحة
      if (rule.allowedValues && !rule.allowedValues.includes(value)) {
        errors.push(
          `${field} يجب أن يكون أحد القيم: ${rule.allowedValues.join(", ")}`,
        );
      }

      // فحص مخصص
      if (rule.customValidator && !rule.customValidator(value)) {
        errors.push(`${field} قيمة غير صحيحة`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * تشفير البيانات الحساسة
   */
  static encryptSensitiveData(data: string, key?: string): string {
    try {
      // في التطبيق الحقيقي، يجب استخدام مكتبة تشفير قوية مثل crypto-js
      const encodedData = btoa(data);
      return encodedData;
    } catch (error) {
      console.error("Encryption failed:", error);
      return data;
    }
  }

  /**
   * فك تشفير البيانات الحساسة
   */
  static decryptSensitiveData(encryptedData: string, key?: string): string {
    try {
      const decodedData = atob(encryptedData);
      return decodedData;
    } catch (error) {
      console.error("Decryption failed:", error);
      return encryptedData;
    }
  }

  /**
   * إنشاء توكن مؤقت للعمليات الحساسة
   */
  static generateTemporaryToken(
    user: User,
    action: string,
    expiresInMinutes: number = 15,
  ): string {
    const payload = {
      userId: user.id,
      action,
      exp: Math.floor(Date.now() / 1000) + expiresInMinutes * 60,
      iat: Math.floor(Date.now() / 1000),
    };

    // في التطبيق الحقيقي، يجب استخدام مكتبة JWT مثل jsonwebtoken
    const token = btoa(JSON.stringify(payload));
    return token;
  }

  /**
   * التحقق من صحة التوكن المؤقت
   */
  static validateTemporaryToken(
    token: string,
    expectedAction: string,
    userId: string,
  ): { valid: boolean; error?: string } {
    try {
      const payload = JSON.parse(atob(token));

      // فحص انتهاء الصلاحية
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        return { valid: false, error: "انتهت صلاحية الرمز" };
      }

      // فحص المستخدم
      if (payload.userId !== userId) {
        return { valid: false, error: "مستخدم غير صحيح" };
      }

      // فحص العملية
      if (payload.action !== expectedAction) {
        return { valid: false, error: "عملية غير صحيحة" };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: "تنسيق رمز غير صحيح" };
    }
  }

  /**
   * فحص معدل الطلبات (Rate Limiting) مع تحسينات للأداء
   */
  static checkRateLimit(
    userId: string,
    action: string,
    maxRequests: number = 10,
    windowMinutes: number = 1,
  ): {
    allowed: boolean;
    remainingRequests?: number;
    resetTime?: Date;
  } {
    const key = `rate_limit_${userId}_${action}`;
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;

    try {
      // Use in-memory cache first, fallback to localStorage
      let data = this.rateLimitCache.get(key);

      if (!data) {
        const stored = localStorage.getItem(key);
        data = stored
          ? JSON.parse(stored)
          : { count: 0, resetTime: now + windowMs };
      }

      // إعادة تعيين النافذة إذا انتهت
      if (now >= data.resetTime) {
        data = { count: 0, resetTime: now + windowMs };
      }

      // فحص الحد الأقصى
      if (data.count >= maxRequests) {
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime: new Date(data.resetTime),
        };
      }

      // زيادة العداد
      data.count++;

      // Update both cache and localStorage
      this.rateLimitCache.set(key, data);
      localStorage.setItem(key, JSON.stringify(data));

      return {
        allowed: true,
        remainingRequests: maxRequests - data.count,
        resetTime: new Date(data.resetTime),
      };
    } catch (error) {
      // في حالة الخطأ، اسمح بالطلب
      console.warn("Rate limiting error:", error);
      return { allowed: true };
    }
  }

  /**
   * تنظيف البيانات من المحتوى الضار (Sanitization)
   */
  static sanitizeInput(input: any): any {
    if (typeof input === "string") {
      // إزالة العلامات الضارة
      return input
        .replace(/<script[^>]*>.*?<\/script>/gi, "")
        .replace(/<[^>]*>/g, "")
        .replace(/javascript:/gi, "")
        .replace(/on\w+=/gi, "")
        .trim();
    }

    if (Array.isArray(input)) {
      return input.map((item) => this.sanitizeInput(item));
    }

    if (typeof input === "object" && input !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }

    return input;
  }

  /**
   * فحص قوة كلمة المرور
   */
  static validatePasswordStrength(password: string): {
    valid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // فحص الطول
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب أن تكون على الأقل 8 أحرف");
    }

    // فحص الأحرف الكبيرة
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");
    }

    // فحص الأحرف الصغيرة
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");
    }

    // فحص الأرقام
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");
    }

    // فحص الرموز الخاصة
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل");
    }

    // فحص التكرار
    if (!/(..).*\1/.test(password)) {
      score += 1;
    } else {
      feedback.push("كلمة المرور يجب ألا تحتوي على أنماط متكررة");
    }

    return {
      valid: score >= 4,
      score,
      feedback,
    };
  }

  /**
   * إنشاء رمز التحقق الثنائي (2FA)
   */
  static generate2FACode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * التحقق من رمز التحقق الثنائي
   */
  static verify2FACode(
    providedCode: string,
    storedCode: string,
    expirationTime: Date,
  ): { valid: boolean; error?: string } {
    if (Date.now() > expirationTime.getTime()) {
      return { valid: false, error: "انتهت صلاحية رمز التحقق" };
    }

    if (providedCode !== storedCode) {
      return { valid: false, error: "رمز التحقق غير صحيح" };
    }

    return { valid: true };
  }

  /**
   * الحصول على اسم الدور
   */
  private static getRoleName(role: UserRole): string {
    const roleNames: Record<UserRole, string> = {
      [UserRole.ADMIN]: "مدير النظام",
      [UserRole.SCHOOL_MANAGER]: "مدير المدرسة",
      [UserRole.SUPERVISOR]: "المشرف",
      [UserRole.DRIVER]: "السائق",
      [UserRole.PARENT]: "ولي الأمر",
      [UserRole.STUDENT]: "الطالب",
    };
    return roleNames[role] || "مستخدم";
  }

  /**
   * الحصول على رسالة خطأ مناسبة عند عدم وجود صلاحية
   */
  private static getAccessDeniedMessage(
    user: User,
    resource: ResourceType,
    action: Action,
  ): string {
    const roleName = this.getRoleName(user.role as UserRole);
    const resourceNames: Record<ResourceType, string> = {
      [ResourceType.SYSTEM]: "النظام",
      [ResourceType.SCHOOL]: "المدرسة",
      [ResourceType.USER]: "المستخدم",
      [ResourceType.BUS]: "الحافلة",
      [ResourceType.ROUTE]: "المسار",
      [ResourceType.STUDENT]: "الطالب",
      [ResourceType.ATTENDANCE]: "الحضور",
      [ResourceType.NOTIFICATION]: "الإشعار",
      [ResourceType.REPORT]: "التقرير",
      [ResourceType.MAINTENANCE]: "الصيانة",
      [ResourceType.EVALUATION]: "التقييم",
      [ResourceType.BILLING]: "الفواتير",
      [ResourceType.EMERGENCY]: "الطوارئ",
      [ResourceType.SAFETY]: "السلامة",
      [ResourceType.ANALYTICS]: "التحليلات",
      [ResourceType.AUDIT]: "التدقيق",
      [ResourceType.INTEGRATION]: "التكامل",
      [ResourceType.GEOFENCE]: "السياج الجغرافي",
      [ResourceType.SCHEDULE]: "الجدولة",
      [ResourceType.COMMUNICATION]: "التواصل",
    };

    const actionNames: Record<Action, string> = {
      [Action.CREATE]: "إنشاء",
      [Action.READ]: "عرض",
      [Action.UPDATE]: "تعديل",
      [Action.DELETE]: "حذف",
      [Action.MANAGE]: "إدارة",
      [Action.ASSIGN]: "تخصيص",
      [Action.TRACK]: "تتبع",
      [Action.EXPORT]: "تصدير",
      [Action.SCHEDULE]: "جدولة",
      [Action.ANALYZE]: "تحليل",
      [Action.APPROVE]: "موافقة",
      [Action.REJECT]: "رفض",
      [Action.SUSPEND]: "تعليق",
      [Action.ACTIVATE]: "تفعيل",
      [Action.MONITOR]: "مراقبة",
      [Action.ALERT]: "تنبيه",
      [Action.BACKUP]: "نسخ احتياطي",
      [Action.RESTORE]: "استعادة",
      [Action.AUDIT]: "تدقيق",
      [Action.CONFIGURE]: "تكوين",
      [Action.UNASSIGN]: "إلغاء تخصيص",
      [Action.IMPORT]: "استيراد",
    };

    const resourceName = resourceNames[resource] || resource;
    const actionName = actionNames[action] || action;

    return `عذراً، ${roleName} لا يملك صلاحية ${actionName} ${resourceName}. يرجى التواصل مع مدير النظام.`;
  }

  /**
   * فحص الصلاحيات السياقية المحدث
   */
  private static checkContextualPermission(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
    context?: PermissionContext,
  ): boolean {
    if (!context) return true;

    const allowedScopes = RBACManager.getRoleDataScopes(userRole);

    switch (userRole) {
      case UserRole.ADMIN:
        // الأدمن له صلاحية كاملة لجميع الموارد والعمليات بدون قيود
        return true;

      case UserRole.SCHOOL_MANAGER:
        // مدير المدرسة يمكنه الوصول لبيانات مدرسته فقط
        if (!this.checkTenantAccess(context)) {
          return false;
        }
        // إضافة فحص إضافي للبيانات المخصصة
        if (allowedScopes.includes(DataScope.ASSIGNED)) {
          return this.checkPersonalOrAssignedAccess(context, resource);
        }
        return true;

      case UserRole.SUPERVISOR:
        // المشرف يمكنه الوصول لبيانات مدرسته فقط
        return this.checkTenantAccess(context);

      case UserRole.DRIVER:
        // السائق يمكنه الوصول لبياناته الشخصية أو المخصصة له فقط
        return this.checkPersonalOrAssignedAccess(context, resource);

      case UserRole.PARENT:
        // ولي الأمر يمكنه الوصول لبياناته الشخصية أو بيانات أطفاله
        return this.checkParentAccess(context, resource);

      case UserRole.STUDENT:
        // الطالب يمكنه الوصول لبياناته الشخصية فقط
        return this.checkPersonalAccess(context);

      default:
        return false;
    }
  }

  /**
   * Enhanced security checks with comprehensive validation
   */
  private static performSecurityChecks(
    user: User,
    context?: {
      resourceId?: string;
      resourceOwnerId?: string;
      resourceTenantId?: string;
      resourceData?: any;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): { passed: boolean; error?: string; level?: string } {
    // Check for suspicious activity patterns
    const suspiciousPatterns = this.detectSuspiciousActivity(user, context);
    if (suspiciousPatterns.length > 0) {
      this.logSecurityEvent(
        "suspicious_activity_detected",
        "high",
        `Suspicious patterns detected: ${suspiciousPatterns.join(", ")}`,
        {
          userId: user.id,
          patterns: suspiciousPatterns,
          context,
        },
      );
      return {
        passed: false,
        error: "Suspicious activity detected. Access temporarily restricted.",
        level: "critical",
      };
    }

    // Check if user account is active
    if (!user.is_active) {
      return {
        passed: false,
        error: "Account is deactivated",
        level: "high",
      };
    }

    // Check for account lockout
    if (user.metadata?.account_locked_until) {
      const lockoutTime = new Date(user.metadata.account_locked_until);
      if (lockoutTime > new Date()) {
        return {
          passed: false,
          error: "Account is temporarily locked",
          level: "high",
        };
      }
    }

    // Check session validity
    if (context?.sessionId) {
      // In a real implementation, validate session against database
      // For now, we'll assume session is valid
    }

    // Check IP restrictions (if configured)
    if (context?.ipAddress && user.metadata?.allowed_ips) {
      const allowedIPs = user.metadata.allowed_ips as string[];
      if (!allowedIPs.includes(context.ipAddress)) {
        this.logSecurityEvent(
          "ip_restriction_violation",
          "high",
          "Access attempt from unauthorized IP",
          {
            userId: user.id,
            attemptedIP: context.ipAddress,
            allowedIPs,
          },
        );
        return {
          passed: false,
          error: "Access denied from this IP address",
          level: "high",
        };
      }
    }

    return { passed: true };
  }

  /**
   * Detect suspicious activity patterns
   */
  private static detectSuspiciousActivity(user: User, context?: any): string[] {
    const patterns: string[] = [];

    // Check for rapid successive requests
    const recentRequests = this.getRecentRequests(user.id);
    if (recentRequests > 50) {
      patterns.push("rapid_requests");
    }

    // Check for unusual access patterns
    if (context?.ipAddress && this.isUnusualIP(user.id, context.ipAddress)) {
      patterns.push("unusual_ip");
    }

    // Check for privilege escalation attempts
    if (this.isPrivilegeEscalationAttempt(user, context)) {
      patterns.push("privilege_escalation");
    }

    return patterns;
  }

  /**
   * Get recent request count for user
   */
  private static getRecentRequests(userId: string): number {
    const key = `requests_${userId}`;
    const stored = localStorage.getItem(key);
    if (!stored) return 0;

    try {
      const data = JSON.parse(stored);
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      return (
        data.requests?.filter((timestamp: number) => timestamp > fiveMinutesAgo)
          ?.length || 0
      );
    } catch {
      return 0;
    }
  }

  /**
   * Check if IP is unusual for user
   */
  private static isUnusualIP(userId: string, ipAddress: string): boolean {
    // In production, this would check against user's historical IPs
    return false; // Simplified for demo
  }

  /**
   * Check for privilege escalation attempts
   */
  private static isPrivilegeEscalationAttempt(
    user: User,
    context?: any,
  ): boolean {
    // Check if user is trying to access resources above their privilege level
    if (
      user.role === UserRole.STUDENT &&
      context?.resourceType === ResourceType.SYSTEM
    ) {
      return true;
    }
    if (user.role === UserRole.PARENT && context?.action === Action.DELETE) {
      return true;
    }
    return false;
  }

  /**
   * Get rate limit for specific action
   */
  private static getRateLimitForAction(action: Action): number {
    const rateLimits: Record<Action, number> = {
      [Action.CREATE]: 10,
      [Action.UPDATE]: 20,
      [Action.DELETE]: 5,
      [Action.READ]: 100,
      [Action.MANAGE]: 15,
      [Action.ASSIGN]: 10,
      [Action.TRACK]: 50,
      [Action.EXPORT]: 3,
      [Action.SCHEDULE]: 5,
      [Action.ANALYZE]: 10,
      [Action.APPROVE]: 10,
      [Action.REJECT]: 10,
      [Action.SUSPEND]: 5,
      [Action.ACTIVATE]: 10,
      [Action.MONITOR]: 30,
      [Action.ALERT]: 20,
      [Action.BACKUP]: 1,
      [Action.RESTORE]: 1,
      [Action.AUDIT]: 5,
      [Action.CONFIGURE]: 5,
      [Action.UNASSIGN]: 10,
      [Action.IMPORT]: 3,
    };
    return rateLimits[action] || 10;
  }

  /**
   * Enhanced security event logging with database storage
   */
  private static async logSecurityEvent(
    eventType: string,
    severity: string,
    description: string,
    metadata?: any,
  ): Promise<void> {
    const securityEvent = {
      eventType,
      severity,
      description,
      metadata,
      timestamp: new Date().toISOString(),
      userAgent:
        typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
    };

    // Log to console for immediate visibility
    console.log("🚨 Security Event:", securityEvent);

    // Store in database (in production)
    try {
      // await supabase.from('security_events').insert(securityEvent);
    } catch (error) {
      console.error("Failed to store security event:", error);
    }

    // Send to external monitoring service
    this.sendToSecurityMonitoring(securityEvent);
  }

  /**
   * Send security events to external monitoring
   */
  private static sendToSecurityMonitoring(event: any): void {
    // In production, integrate with services like DataDog, Sentry, etc.
    if (event.severity === "critical" || event.severity === "high") {
      // Trigger immediate alerts
      console.warn("🚨 HIGH SEVERITY SECURITY EVENT:", event);
    }
  }

  /**
   * فحص الوصول على مستوى المدرسة
   */
  private static checkTenantAccess(context: PermissionContext): boolean {
    if (context.resourceTenantId && context.tenantId) {
      return context.resourceTenantId === context.tenantId;
    }
    return true;
  }

  /**
   * فحص الوصول الشخصي
   */
  private static checkPersonalAccess(context: PermissionContext): boolean {
    if (context.resourceOwnerId && context.userId) {
      return context.resourceOwnerId === context.userId;
    }
    return true;
  }

  /**
   * فحص الوصول الشخصي أو المخصص
   */
  private static checkPersonalOrAssignedAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    // فحص الوصول الشخصي
    if (this.checkPersonalAccess(context)) {
      return true;
    }

    // فحص الوصول للبيانات المخصصة
    return this.checkAssignedAccess(context, resource);
  }

  /**
   * فحص الوصول للبيانات المخصصة
   */
  private static checkAssignedAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    if (!context.resourceData || !context.userId) {
      return false;
    }

    switch (resource) {
      case ResourceType.BUS:
        return context.resourceData.driver_id === context.userId;
      case ResourceType.ROUTE:
        return context.resourceData.bus?.driver_id === context.userId;
      case ResourceType.STUDENT:
        return (
          context.resourceData.route_stop?.route?.bus?.driver_id ===
          context.userId
        );
      default:
        return false;
    }
  }

  /**
   * فحص وصول ولي الأمر
   */
  private static checkParentAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    // فحص الوصول الشخصي
    if (this.checkPersonalAccess(context)) {
      return true;
    }

    // فحص الوصول لبيانات الأطفال
    if (resource === ResourceType.STUDENT && context.resourceData) {
      return context.resourceData.parent_id === context.userId;
    }

    return false;
  }
}

/**
 * نوع البيانات لطلب API
 */
export interface APIRequest {
  user: User;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  path: string;
  body?: any;
  query?: Record<string, any>;
  headers?: Record<string, string>;
}

/**
 * نوع البيانات لاستجابة API
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode: number;
}

/**
 * فئة معالجة طلبات API مع التحقق من الصلاحيات
 */
export class APIHandler {
  /**
   * معالجة طلب API مع التحقق من الصلاحيات
   */
  static async handleRequest<T>(
    request: APIRequest,
    handler: (req: APIRequest) => Promise<T>,
  ): Promise<APIResponse<T>> {
    try {
      // تنظيف البيانات المدخلة
      const sanitizedRequest = {
        ...request,
        body: AuthMiddleware.sanitizeInput(request.body),
        query: AuthMiddleware.sanitizeInput(request.query),
      };

      // فحص معدل الطلبات
      const rateLimitCheck = AuthMiddleware.checkRateLimit(
        request.user.id,
        `${request.method}_${request.path}`,
        100, // 100 طلب
        1, // في الدقيقة
      );

      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          error: "تم تجاوز الحد الأقصى للطلبات",
          statusCode: 429,
        };
      }

      // تنفيذ المعالج
      const result = await handler(sanitizedRequest);

      // إنشاء سجل تدقيق للعملية الناجحة
      AuthMiddleware.createAuditLog(
        request.user,
        `${request.method} ${request.path}`,
        "API",
        undefined,
        { body: request.body, query: request.query },
      );

      return {
        success: true,
        data: result,
        statusCode: 200,
      };
    } catch (error) {
      // إنشاء سجل تدقيق للخطأ
      AuthMiddleware.createAuditLog(
        request.user,
        `${request.method} ${request.path} - ERROR`,
        "API",
        undefined,
        { error: error instanceof Error ? error.message : "Unknown error" },
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : "خطأ داخلي في الخادم",
        statusCode: 500,
      };
    }
  }

  /**
   * معالجة طلب مع التحقق من صلاحية المورد
   */
  static async handleResourceRequest<T>(
    request: APIRequest,
    resource: ResourceType,
    action: Action,
    handler: (req: APIRequest) => Promise<T>,
    context?: {
      resourceId?: string;
      resourceOwnerId?: string;
      resourceTenantId?: string;
      resourceData?: any;
    },
  ): Promise<APIResponse<T>> {
    // فحص صلاحية الوصول للمورد
    const accessCheck = AuthMiddleware.checkResourceAccess(
      request.user,
      resource,
      action,
      context,
    );

    if (!accessCheck.allowed) {
      return {
        success: false,
        error: accessCheck.error,
        statusCode: 403,
      };
    }

    // تنفيذ الطلب
    return this.handleRequest(request, handler);
  }
}

export default AuthMiddleware;
