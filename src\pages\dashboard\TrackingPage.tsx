import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bus,
  Search,
  MapPin,
  Clock,
  Users,
  Route,
  AlertTriangle,
} from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { LiveMap } from "../../components/map/LiveMap";
import { LiveBusTracking } from "../../components/buses/LiveBusTracking";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

export const TrackingPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses, routes, loading, error } = useDatabase();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBus, setSelectedBus] = useState<Tables<"buses"> | null>(null);
  const [viewMode, setViewMode] = useState<"overview" | "detailed">("overview");

  // Check if there's a specific bus ID in URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const busId = urlParams.get("bus");
    if (busId) {
      const bus = buses.find((b) => b.id === busId);
      if (bus) {
        setSelectedBus(bus);
        setViewMode("detailed");
      }
    }
  }, [buses]);

  // Filter buses based on search query
  const filteredBuses = buses.filter((bus) => {
    if (!searchQuery) return true;
    return bus.plate_number.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <AlertTriangle
                  size={48}
                  className="mx-auto mb-4 text-red-500"
                />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {t("common.error")}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{error}</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("tracking.liveTracking")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t("tracking.trackBusesRealTime")}
                </p>
              </div>
            </div>

            {viewMode === "detailed" && selectedBus ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setViewMode("overview");
                      setSelectedBus(null);
                      // Remove bus param from URL
                      const url = new URL(window.location.href);
                      url.searchParams.delete("bus");
                      window.history.replaceState({}, "", url.toString());
                    }}
                  >
                    ← {t("common.back")}
                  </Button>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedBus.plate_number} - {t("tracking.detailedView")}
                  </h2>
                </div>
                <LiveBusTracking busId={selectedBus.id} />
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-1">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <div className="mb-4">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Search size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="text"
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          placeholder={t("common.search")}
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      {filteredBuses.map((bus) => (
                        <div key={bus.id} className="space-y-2">
                          <button
                            onClick={() => setSelectedBus(bus)}
                            className={`w-full flex items-center p-3 rounded-lg transition-colors ${
                              selectedBus?.id === bus.id
                                ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400"
                                : "hover:bg-gray-50 dark:hover:bg-gray-700"
                            }`}
                          >
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-lg flex items-center justify-center text-primary-600 dark:text-primary-400">
                                <Bus size={20} />
                              </div>
                            </div>
                            <div className="ml-3 text-left flex-1">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {bus.plate_number}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {routes.find((r) => r.bus_id === bus.id)
                                  ?.name || t("buses.noRoute")}
                              </p>
                            </div>
                          </button>
                          {selectedBus?.id === bus.id && (
                            <Button
                              size="sm"
                              className="w-full"
                              onClick={() => setViewMode("detailed")}
                            >
                              {t("tracking.viewDetails")}
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="lg:col-span-3">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <div className="h-[600px]">
                      <LiveMap
                        selectedBusId={selectedBus?.id}
                        onBusSelect={setSelectedBus}
                        showRoutes={true}
                        showGeofences={true}
                        showTraffic={false}
                        showSpeedInfo={true}
                        showETA={true}
                      />
                    </div>
                  </div>

                  {selectedBus && (
                    <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        {t("tracking.routeProgress")}
                      </h2>

                      <div className="space-y-4">
                        {routes
                          .find((r) => r.bus_id === selectedBus.id)
                          ?.stops?.map((stop, index) => (
                            <div key={stop.id} className="flex items-center">
                              <div
                                className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${
                                  index === 0
                                    ? "bg-accent-100 text-accent-700 dark:bg-accent-900/20 dark:text-accent-400"
                                    : "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400"
                                }`}
                              >
                                {index + 1}
                              </div>
                              <div className="ml-4 flex-1">
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                  {stop.name}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {t("tracking.eta")}: {stop.arrival_time}
                                </p>
                              </div>
                              <div className="flex-shrink-0">
                                <span
                                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    index === 0
                                      ? "bg-accent-100 text-accent-800 dark:bg-accent-900/20 dark:text-accent-400"
                                      : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"
                                  }`}
                                >
                                  {index === 0
                                    ? t("tracking.nextStop")
                                    : `${index + 1}/${routes.find((r) => r.bus_id === selectedBus.id)?.stops?.length || 0}`}
                                </span>
                              </div>
                            </div>
                          )) || (
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {t("routes.noStopsConfigured")}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};
