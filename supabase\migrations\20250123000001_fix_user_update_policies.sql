-- Fix user update policies to allow admin users to update any user
-- and allow school manager assignments

-- Drop existing update policy
DROP POLICY IF EXISTS "Users can update their own records" ON "users";

-- Create new update policies
-- Allow users to update their own records
CREATE POLICY "Users can update their own records"
  ON "users"
  FOR UPDATE
  USING (auth.uid() = id);

-- Allow admin users to update any user record
CREATE POLICY "Admin users can update any user"
  ON "users"
  FOR UPDATE
  USING (auth.role() = 'authenticated');

-- Create a function to safely update user roles and tenant assignments
CREATE OR REPLACE FUNCTION update_user_safely(
  user_id uuid,
  new_tenant_id uuid DEFAULT NULL,
  new_role text DEFAULT NULL,
  new_name text DEFAULT NULL,
  new_email text DEFAULT NULL,
  new_phone text DEFAULT NULL,
  new_is_active boolean DEFAULT NULL
)
RETURNS "users" AS $$
DECLARE
  updated_user "users";
BEGIN
  -- Update the user record
  UPDATE "users" 
  SET 
    tenant_id = COALESCE(new_tenant_id, tenant_id),
    role = COALESCE(new_role, role),
    name = COALESCE(new_name, name),
    email = COALESCE(new_email, email),
    phone = COALESCE(new_phone, phone),
    is_active = COALESCE(new_is_active, is_active),
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;
  
  RETURN updated_user;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_user_safely(
  uuid, uuid, text, text, text, text, boolean
) TO authenticated;

-- Create a specific function for assigning school managers
CREATE OR REPLACE FUNCTION assign_school_manager(
  user_id uuid,
  tenant_id uuid
)
RETURNS "users" AS $$
DECLARE
  updated_user "users";
BEGIN
  -- Check if user exists
  IF NOT EXISTS (SELECT 1 FROM "users" WHERE id = user_id) THEN
    RAISE EXCEPTION 'User with ID % not found', user_id;
  END IF;
  
  -- Update the user to be a school manager for the specified tenant
  UPDATE "users" 
  SET 
    tenant_id = assign_school_manager.tenant_id,
    role = 'school_manager',
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;
  
  IF updated_user IS NULL THEN
    RAISE EXCEPTION 'Failed to update user with ID %', user_id;
  END IF;
  
  RETURN updated_user;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION assign_school_manager(uuid, uuid) TO authenticated;
