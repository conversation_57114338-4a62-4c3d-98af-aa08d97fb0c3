-- RBAC Security Implementation Migration
-- Comprehensive security enhancement for multi-tenant school transport system
-- Generated on: 2025-01-23

-- Enable RLS on all existing tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
<PERSON>TER TABLE buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE evaluations ENABLE ROW LEVEL SECURITY;
<PERSON>TE<PERSON> TABLE complaints ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> audit log table for security tracking
CREATE TABLE IF NOT EXISTS audit_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  operation text NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
  old_data jsonb,
  new_data jsonb,
  user_id uuid REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  timestamp timestamptz DEFAULT now(),
  ip_address inet,
  user_agent text,
  session_id text
);

-- Create security events table for monitoring
CREATE TABLE IF NOT EXISTS security_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  description text NOT NULL,
  user_id uuid REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  metadata jsonb,
  timestamp timestamptz DEFAULT now(),
  resolved boolean DEFAULT false,
  resolved_by uuid REFERENCES auth.users(id),
  resolved_at timestamptz
);

-- Create user sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  session_token text UNIQUE NOT NULL,
  ip_address inet,
  user_agent text,
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  is_active boolean DEFAULT true,
  last_activity timestamptz DEFAULT now(),
  logout_reason text
);

-- Create role permissions table for dynamic RBAC
CREATE TABLE IF NOT EXISTS role_permissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  role text NOT NULL,
  permission text NOT NULL,
  resource_type text NOT NULL,
  action text NOT NULL,
  scope text NOT NULL,
  conditions jsonb,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(role, permission, resource_type, action)
);

-- Create permission contexts table for fine-grained access control
CREATE TABLE IF NOT EXISTS permission_contexts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  resource_type text NOT NULL,
  resource_id uuid,
  permissions jsonb NOT NULL,
  expires_at timestamptz,
  created_at timestamptz DEFAULT now(),
  created_by uuid REFERENCES auth.users(id)
);

-- Add security-related columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login timestamptz;
ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts integer DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until timestamptz;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at timestamptz DEFAULT now();
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_enabled boolean DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_secret text;
ALTER TABLE users ADD COLUMN IF NOT EXISTS security_questions jsonb;
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_notifications boolean DEFAULT true;

-- Add security settings to tenants table
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS security_settings jsonb DEFAULT '{
  "password_policy": {
    "min_length": 8,
    "require_uppercase": true,
    "require_lowercase": true,
    "require_numbers": true,
    "require_special_chars": true,
    "max_age_days": 90
  },
  "session_settings": {
    "timeout_minutes": 480,
    "max_concurrent_sessions": 3,
    "require_mfa_for_admin": true
  },
  "access_restrictions": {
    "allowed_ip_ranges": [],
    "allowed_hours": {"start": "06:00", "end": "22:00"},
    "allowed_days": [1,2,3,4,5,6,7]
  }
}'::jsonb;

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(resolved);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role);
CREATE INDEX IF NOT EXISTS idx_permission_contexts_user_id ON permission_contexts(user_id);
CREATE INDEX IF NOT EXISTS idx_users_tenant_role ON users(tenant_id, role);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id uuid;
  current_tenant_id uuid;
BEGIN
  -- Get current user and tenant from JWT
  current_user_id := auth.uid();
  current_tenant_id := (auth.jwt() ->> 'tenant_id')::uuid;
  
  -- Insert audit record
  INSERT INTO audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    tenant_id,
    session_id
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    current_user_id,
    COALESCE(
      CASE WHEN TG_OP = 'DELETE' THEN OLD.tenant_id ELSE NEW.tenant_id END,
      current_tenant_id
    ),
    (auth.jwt() ->> 'session_id')
  );
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create security event logging function
CREATE OR REPLACE FUNCTION log_security_event(
  p_event_type text,
  p_severity text,
  p_description text,
  p_metadata jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  event_id uuid;
BEGIN
  INSERT INTO security_events (
    event_type,
    severity,
    description,
    user_id,
    tenant_id,
    metadata
  ) VALUES (
    p_event_type,
    p_severity,
    p_description,
    auth.uid(),
    (auth.jwt() ->> 'tenant_id')::uuid,
    p_metadata
  ) RETURNING id INTO event_id;
  
  RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create session validation function
CREATE OR REPLACE FUNCTION validate_user_session(
  p_session_token text,
  p_user_id uuid
)
RETURNS boolean AS $$
DECLARE
  session_valid boolean := false;
BEGIN
  -- Check if session exists and is valid
  SELECT EXISTS(
    SELECT 1 FROM user_sessions
    WHERE session_token = p_session_token
      AND user_id = p_user_id
      AND is_active = true
      AND expires_at > now()
  ) INTO session_valid;
  
  -- Update last activity if session is valid
  IF session_valid THEN
    UPDATE user_sessions
    SET last_activity = now()
    WHERE session_token = p_session_token
      AND user_id = p_user_id;
  END IF;
  
  RETURN session_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check user permissions
CREATE OR REPLACE FUNCTION check_user_permission(
  p_user_id uuid,
  p_resource_type text,
  p_action text,
  p_resource_id uuid DEFAULT NULL
)
RETURNS boolean AS $$
DECLARE
  user_role text;
  has_permission boolean := false;
BEGIN
  -- Get user role
  SELECT role INTO user_role FROM users WHERE id = p_user_id;
  
  -- Check if user has the required permission
  SELECT EXISTS(
    SELECT 1 FROM role_permissions
    WHERE role = user_role
      AND resource_type = p_resource_type
      AND action = p_action
      AND is_active = true
  ) INTO has_permission;
  
  -- Additional context-based checks can be added here
  
  RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to cleanup expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS integer AS $$
DECLARE
  deleted_count integer;
BEGIN
  -- Mark expired sessions as inactive
  UPDATE user_sessions 
  SET is_active = false, logout_reason = 'expired'
  WHERE (expires_at < now() OR last_activity < now() - interval '24 hours')
    AND is_active = true;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log cleanup event
  PERFORM log_security_event(
    'session_cleanup',
    'low',
    format('Cleaned up %s expired sessions', deleted_count),
    jsonb_build_object('cleaned_sessions', deleted_count)
  );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add audit triggers to sensitive tables
DROP TRIGGER IF EXISTS audit_users_trigger ON users;
CREATE TRIGGER audit_users_trigger
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_tenants_trigger ON tenants;
CREATE TRIGGER audit_tenants_trigger
  AFTER INSERT OR UPDATE OR DELETE ON tenants
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_buses_trigger ON buses;
CREATE TRIGGER audit_buses_trigger
  AFTER INSERT OR UPDATE OR DELETE ON buses
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_students_trigger ON students;
CREATE TRIGGER audit_students_trigger
  AFTER INSERT OR UPDATE OR DELETE ON students
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_attendance_trigger ON attendance;
CREATE TRIGGER audit_attendance_trigger
  AFTER INSERT OR UPDATE OR DELETE ON attendance
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Insert default role permissions
INSERT INTO role_permissions (role, permission, resource_type, action, scope) VALUES
-- Admin permissions
('admin', 'system_admin', 'system', 'manage', 'global'),
('admin', 'schools_view_all', 'school', 'read', 'global'),
('admin', 'schools_create', 'school', 'create', 'global'),
('admin', 'users_view_all', 'user', 'read', 'global'),
('admin', 'users_create', 'user', 'create', 'global'),
('admin', 'buses_view_all', 'bus', 'read', 'global'),
('admin', 'buses_create', 'bus', 'create', 'global'),

-- School Manager permissions
('school_manager', 'schools_view_own', 'school', 'read', 'tenant'),
('school_manager', 'users_view_tenant', 'user', 'read', 'tenant'),
('school_manager', 'users_create', 'user', 'create', 'tenant'),
('school_manager', 'buses_view_tenant', 'bus', 'read', 'tenant'),
('school_manager', 'buses_create', 'bus', 'create', 'tenant'),
('school_manager', 'students_view_tenant', 'student', 'read', 'tenant'),
('school_manager', 'students_create', 'student', 'create', 'tenant'),

-- Supervisor permissions
('supervisor', 'users_view_tenant', 'user', 'read', 'tenant'),
('supervisor', 'buses_view_tenant', 'bus', 'read', 'tenant'),
('supervisor', 'buses_track', 'bus', 'track', 'tenant'),
('supervisor', 'students_view_tenant', 'student', 'read', 'tenant'),
('supervisor', 'students_manage_attendance', 'attendance', 'manage', 'tenant'),

-- Driver permissions
('driver', 'users_view_own', 'user', 'read', 'personal'),
('driver', 'buses_view_assigned', 'bus', 'read', 'assigned'),
('driver', 'buses_track', 'bus', 'track', 'assigned'),
('driver', 'students_manage_attendance', 'attendance', 'manage', 'assigned'),

-- Parent permissions
('parent', 'users_view_own', 'user', 'read', 'personal'),
('parent', 'buses_track', 'bus', 'track', 'children'),
('parent', 'students_view_children', 'student', 'read', 'children'),
('parent', 'students_view_attendance', 'attendance', 'read', 'children'),

-- Student permissions
('student', 'users_view_own', 'user', 'read', 'personal'),
('student', 'buses_track', 'bus', 'track', 'personal'),
('student', 'students_view_own', 'student', 'read', 'personal'),
('student', 'students_view_attendance', 'attendance', 'read', 'personal')

ON CONFLICT (role, permission, resource_type, action) DO NOTHING;

-- Create RLS policies for new tables

-- Audit logs policies
CREATE POLICY "admin_audit_logs" ON audit_logs
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "tenant_audit_logs" ON audit_logs
  FOR SELECT USING (
    auth.jwt() ->> 'role' IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

-- Security events policies
CREATE POLICY "admin_security_events" ON security_events
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "tenant_security_events" ON security_events
  FOR SELECT USING (
    auth.jwt() ->> 'role' IN ('school_manager', 'supervisor') AND
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

-- User sessions policies
CREATE POLICY "admin_user_sessions" ON user_sessions
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "own_user_sessions" ON user_sessions
  FOR SELECT USING (user_id = auth.uid());

-- Role permissions policies
CREATE POLICY "admin_role_permissions" ON role_permissions
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "read_role_permissions" ON role_permissions
  FOR SELECT USING (true); -- All authenticated users can read permissions

-- Permission contexts policies
CREATE POLICY "admin_permission_contexts" ON permission_contexts
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "own_permission_contexts" ON permission_contexts
  FOR SELECT USING (user_id = auth.uid());

-- Enable RLS on new tables
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_contexts ENABLE ROW LEVEL SECURITY;

-- Create a view for user permissions (for easier querying)
CREATE OR REPLACE VIEW user_permissions AS
SELECT 
  u.id as user_id,
  u.role,
  u.tenant_id,
  rp.permission,
  rp.resource_type,
  rp.action,
  rp.scope,
  rp.conditions
FROM users u
JOIN role_permissions rp ON u.role = rp.role
WHERE rp.is_active = true;

-- Grant necessary permissions
GRANT SELECT ON user_permissions TO authenticated;
GRANT EXECUTE ON FUNCTION log_security_event TO authenticated;
GRANT EXECUTE ON FUNCTION validate_user_session TO authenticated;
GRANT EXECUTE ON FUNCTION check_user_permission TO authenticated;

COMMIT;
