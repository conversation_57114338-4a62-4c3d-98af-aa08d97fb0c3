import { useMemo } from "react";
import { useAuth } from "../contexts/AuthContext";
import {
  RBACManager,
  Permission,
  DataScope,
  ResourceType,
  Action,
} from "../lib/rbac";
import { UserRole } from "../types";

/**
 * Hook لإدارة الصلاحيات في المكونات
 */
export const usePermissions = () => {
  const { user } = useAuth();

  const permissions = useMemo(() => {
    if (!user?.role) {
      return {
        hasPermission: () => false,
        hasDataScope: () => false,
        canPerformAction: () => false,
        canManageRole: () => false,
        getRolePermissions: () => [],
        getRoleDataScopes: () => [],
        isAdmin: false,
        isSchoolManager: false,
        isSupervisor: false,
        isDriver: false,
        isParent: false,
        isStudent: false,
      };
    }

    const userRole = user.role as UserRole;

    return {
      /**
       * فحص ما إذا كان المستخدم لديه صلاحية معينة
       */
      hasPermission: (permission: Permission): boolean => {
        return RBACManager.hasPermission(userRole, permission);
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه الوصول لنطاق بيانات معين
       */
      hasDataScope: (scope: DataScope): boolean => {
        return RBACManager.hasDataScope(userRole, scope);
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه تنفيذ عملية على مورد معين
       */
      canPerformAction: (
        resource: ResourceType,
        action: Action,
        context?: {
          resourceOwnerId?: string;
          resourceTenantId?: string;
        },
      ): boolean => {
        return RBACManager.canPerformAction(userRole, resource, action, {
          userId: user.id,
          tenantId: user.tenant_id || undefined,
          ...context,
        });
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه إدارة دور آخر مع التحقق الأمني
       */
      canManageRole: (targetRole: UserRole, context?: any): boolean => {
        const permissionContext = context
          ? RBACManager.createPermissionContext(
              user.id,
              user.tenant_id || undefined,
              context.resourceOwnerId,
              context.resourceTenantId,
              context.resourceData,
            )
          : undefined;

        return RBACManager.canManageRole(
          userRole,
          targetRole,
          permissionContext,
        );
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه تعديل مستخدم آخر
       */
      canEditUser: (targetUser: any): boolean => {
        // المستخدم يمكنه تعديل بياناته الشخصية
        if (user.id === targetUser.id) {
          return true;
        }

        // فحص التسلسل الهرمي والنطاق
        const canManage = RBACManager.canManageRole(
          userRole,
          targetUser.role as UserRole,
        );
        if (!canManage) return false;

        // فحص نطاق المدرسة
        if (userRole !== UserRole.ADMIN) {
          return user.tenant_id === targetUser.tenant_id;
        }

        return true;
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه حذف مستخدم آخر
       */
      canDeleteUser: (targetUser: any): boolean => {
        // المستخدم لا يمكنه حذف نفسه
        if (user.id === targetUser.id) {
          return false;
        }

        // استخدام نفس منطق التعديل
        return permissions.canEditUser(targetUser);
      },

      /**
       * فحص ما إذا كان المستخدم يمكنه إنشاء مستخدم بدور معين
       */
      canCreateUserWithRole: (
        targetRole: UserRole,
        targetTenantId?: string,
      ): boolean => {
        // فحص التسلسل الهرمي للأدوار
        const canManage = RBACManager.canManageRole(userRole, targetRole);
        if (!canManage) return false;

        // فحص نطاق المدرسة
        if (userRole !== UserRole.ADMIN && targetTenantId) {
          return user.tenant_id === targetTenantId;
        }

        return true;
      },

      /**
       * فحص شامل للوصول للمورد مع رسائل خطأ مفصلة والتحقق الأمني المحسن
       */
      checkResourceAccess: (
        resource: ResourceType,
        action: Action,
        context?: {
          resourceId?: string;
          resourceOwnerId?: string;
          resourceTenantId?: string;
          resourceData?: any;
        },
      ): { allowed: boolean; error?: string; details?: any } => {
        const permissionContext = RBACManager.createPermissionContext(
          user.id,
          user.tenant_id || undefined,
          context?.resourceOwnerId,
          context?.resourceTenantId,
          context?.resourceData,
        );

        // إضافة معلومات إضافية للسياق
        const enhancedContext = {
          ...permissionContext,
          ipAddress:
            typeof window !== "undefined"
              ? window.location.hostname
              : "unknown",
          userAgent:
            typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
          timestamp: new Date(),
        };

        const hasPermission = RBACManager.checkPermissionWithLogging(
          userRole,
          resource,
          action,
          enhancedContext,
        );

        if (!hasPermission) {
          const errorDetails = {
            userRole,
            resource,
            action,
            tenantMismatch:
              context?.resourceTenantId &&
              user.tenant_id !== context.resourceTenantId,
            missingPermission: !RBACManager.hasPermission(
              userRole,
              getRequiredPermission(resource, action),
            ),
            timestamp: new Date().toISOString(),
          };

          return {
            allowed: false,
            error: getAccessDeniedMessage(userRole, resource, action),
            details: errorDetails,
          };
        }

        return { allowed: true };
      },

      /**
       * فلترة البيانات حسب صلاحيات المستخدم مع التحقق الأمني المحسن
       */
      filterDataByPermissions: <T extends { tenant_id?: string; id?: string }>(
        data: T[],
        resourceType: ResourceType,
        ownerIdField?: keyof T,
        additionalFilters?: (item: T) => boolean,
      ): T[] => {
        // التحقق من صحة البيانات المدخلة
        if (!Array.isArray(data)) {
          console.warn("Invalid data provided to filterDataByPermissions");
          return [];
        }

        // الأدمن يرى كل البيانات بدون أي فلترة (مع التحقق الإضافي)
        if (userRole === UserRole.ADMIN) {
          const filtered = additionalFilters
            ? data.filter(additionalFilters)
            : data;
          console.log(
            `Admin access: returning ${filtered.length} items of type ${resourceType}`,
          );
          return filtered;
        }

        // فحص نطاق البيانات المسموح
        const allowedScopes = RBACManager.getRoleDataScopes(userRole);

        const filtered = data.filter((item) => {
          // التحقق من صحة العنصر
          if (!item || typeof item !== "object") {
            return false;
          }

          // البيانات الشخصية
          if (
            allowedScopes.includes(DataScope.PERSONAL) &&
            ownerIdField &&
            item[ownerIdField] === user.id
          ) {
            return true;
          }

          // البيانات على مستوى المدرسة مع التحقق الصارم من المدرسة
          if (
            allowedScopes.includes(DataScope.TENANT) &&
            item.tenant_id &&
            user.tenant_id &&
            item.tenant_id === user.tenant_id
          ) {
            return true;
          }

          // البيانات المخصصة (للسائقين مثلاً)
          if (allowedScopes.includes(DataScope.ASSIGNED)) {
            return isAssignedData(user, item, resourceType);
          }

          // بيانات الأطفال (لأولياء الأمور) مع التحقق الإضافي
          if (
            allowedScopes.includes(DataScope.CHILDREN) &&
            resourceType === ResourceType.STUDENT
          ) {
            const isChild = (item as any).parent_id === user.id;
            const sameTenant = item.tenant_id === user.tenant_id;
            return isChild && sameTenant;
          }

          return false;
        });

        // تطبيق المرشحات الإضافية
        const finalFiltered = additionalFilters
          ? filtered.filter(additionalFilters)
          : filtered;

        // تسجيل عملية الفلترة للمراجعة
        console.log(
          `Data filtering for ${userRole}: ${data.length} -> ${finalFiltered.length} items (${resourceType})`,
        );

        // التحقق من محاولات الوصول المشبوهة
        if (
          data.length > 0 &&
          finalFiltered.length === 0 &&
          userRole !== UserRole.STUDENT
        ) {
          console.warn(
            `Suspicious: ${userRole} got 0 results from ${data.length} items for ${resourceType}`,
          );
        }

        return finalFiltered;
      },

      /**
       * الحصول على جميع الصلاحيات للمستخدم الحالي
       */
      getRolePermissions: (): Permission[] => {
        return RBACManager.getRolePermissions(userRole);
      },

      /**
       * الحصول على نطاقات البيانات للمستخدم الحالي
       */
      getRoleDataScopes: (): DataScope[] => {
        return RBACManager.getRoleDataScopes(userRole);
      },

      // خصائص سريعة للأدوار
      isAdmin: userRole === UserRole.ADMIN,
      isSchoolManager: userRole === UserRole.SCHOOL_MANAGER,
      isSupervisor: userRole === UserRole.SUPERVISOR,
      isDriver: userRole === UserRole.DRIVER,
      isParent: userRole === UserRole.PARENT,
      isStudent: userRole === UserRole.STUDENT,

      // معلومات المستخدم
      currentUser: user,
      userRole,
      userTenant: user.tenant_id,

      // دوال مساعدة إضافية
      canAccessTenant: (tenantId: string): boolean => {
        if (userRole === UserRole.ADMIN) return true;
        return user.tenant_id === tenantId;
      },

      canAccessUserData: (targetUserId: string): boolean => {
        if (userRole === UserRole.ADMIN) return true;
        if (user.id === targetUserId) return true;
        return false;
      },

      canAccessStudentData: (studentData: any): boolean => {
        if (userRole === UserRole.ADMIN) return true;
        if (
          userRole === UserRole.SCHOOL_MANAGER ||
          userRole === UserRole.SUPERVISOR
        ) {
          return studentData.tenant_id === user.tenant_id;
        }
        if (userRole === UserRole.PARENT) {
          return studentData.parent_id === user.id;
        }
        if (userRole === UserRole.DRIVER) {
          return studentData.route_stop?.route?.bus?.driver_id === user.id;
        }
        if (userRole === UserRole.STUDENT) {
          return studentData.id === user.id;
        }
        return false;
      },

      canAccessBusData: (busData: any): boolean => {
        if (userRole === UserRole.ADMIN) return true;
        if (
          userRole === UserRole.SCHOOL_MANAGER ||
          userRole === UserRole.SUPERVISOR
        ) {
          return busData.tenant_id === user.tenant_id;
        }
        if (userRole === UserRole.DRIVER) {
          return busData.driver_id === user.id;
        }
        return false;
      },

      canAccessRouteData: (routeData: any): boolean => {
        if (userRole === UserRole.ADMIN) return true;
        if (
          userRole === UserRole.SCHOOL_MANAGER ||
          userRole === UserRole.SUPERVISOR
        ) {
          return routeData.tenant_id === user.tenant_id;
        }
        if (userRole === UserRole.DRIVER) {
          return routeData.bus?.driver_id === user.id;
        }
        return false;
      },

      // دوال للتحقق من الصلاحيات المحددة
      canViewSystemReports: (): boolean => {
        return permissions.hasPermission(Permission.REPORTS_VIEW_SYSTEM);
      },

      canManageUsers: (): boolean => {
        return (
          permissions.hasPermission(Permission.USERS_CREATE) ||
          permissions.hasPermission(Permission.USERS_UPDATE_ALL) ||
          permissions.hasPermission(Permission.USERS_UPDATE_TENANT)
        );
      },

      canManageBuses: (): boolean => {
        return (
          permissions.hasPermission(Permission.BUSES_CREATE) ||
          permissions.hasPermission(Permission.BUSES_UPDATE) ||
          permissions.hasPermission(Permission.BUSES_DELETE)
        );
      },

      canManageRoutes: (): boolean => {
        return (
          permissions.hasPermission(Permission.ROUTES_CREATE) ||
          permissions.hasPermission(Permission.ROUTES_UPDATE) ||
          permissions.hasPermission(Permission.ROUTES_DELETE)
        );
      },

      canManageStudents: (): boolean => {
        return (
          permissions.hasPermission(Permission.STUDENTS_CREATE) ||
          permissions.hasPermission(Permission.STUDENTS_UPDATE) ||
          permissions.hasPermission(Permission.STUDENTS_DELETE)
        );
      },

      canTrackBuses: (): boolean => {
        return permissions.hasPermission(Permission.BUSES_TRACK);
      },

      canManageAttendance: (): boolean => {
        return permissions.hasPermission(Permission.STUDENTS_MANAGE_ATTENDANCE);
      },

      canSendNotifications: (): boolean => {
        return (
          permissions.hasPermission(Permission.NOTIFICATIONS_SEND_SYSTEM) ||
          permissions.hasPermission(Permission.NOTIFICATIONS_SEND_TENANT)
        );
      },

      canExportReports: (): boolean => {
        return permissions.hasPermission(Permission.REPORTS_EXPORT);
      },

      canManageMaintenance: (): boolean => {
        return (
          permissions.hasPermission(Permission.MAINTENANCE_CREATE) ||
          permissions.hasPermission(Permission.MAINTENANCE_UPDATE) ||
          permissions.hasPermission(Permission.MAINTENANCE_SCHEDULE)
        );
      },

      canManageEvaluations: (): boolean => {
        return (
          permissions.hasPermission(Permission.EVALUATION_CREATE) ||
          permissions.hasPermission(Permission.EVALUATION_ANALYZE)
        );
      },
    };
  }, [user]);

  return permissions;
};

/**
 * دوال مساعدة خارجية
 */

/**
 * فحص ما إذا كانت البيانات مخصصة للمستخدم
 */
function isAssignedData<T extends { tenant_id?: string; id?: string }>(
  user: any,
  item: T,
  resourceType: ResourceType,
): boolean {
  switch (resourceType) {
    case ResourceType.BUS:
      return (item as any).driver_id === user.id;
    case ResourceType.ROUTE:
      return (item as any).bus?.driver_id === user.id;
    case ResourceType.STUDENT:
      return (item as any).route_stop?.route?.bus?.driver_id === user.id;
    default:
      return false;
  }
}

/**
 * الحصول على رسالة خطأ مناسبة عند عدم وجود صلاحية
 */
function getAccessDeniedMessage(
  userRole: UserRole,
  resource: ResourceType,
  action: Action,
): string {
  const roleNames: Record<UserRole, string> = {
    [UserRole.ADMIN]: "مدير النظام",
    [UserRole.SCHOOL_MANAGER]: "مدير المدرسة",
    [UserRole.SUPERVISOR]: "المشرف",
    [UserRole.DRIVER]: "السائق",
    [UserRole.PARENT]: "ولي الأمر",
    [UserRole.STUDENT]: "الطالب",
  };

  const resourceNames: Record<ResourceType, string> = {
    [ResourceType.SYSTEM]: "النظام",
    [ResourceType.SCHOOL]: "المدرسة",
    [ResourceType.USER]: "المستخدم",
    [ResourceType.BUS]: "الحافلة",
    [ResourceType.ROUTE]: "المسار",
    [ResourceType.STUDENT]: "الطالب",
    [ResourceType.ATTENDANCE]: "الحضور",
    [ResourceType.NOTIFICATION]: "الإشعار",
    [ResourceType.REPORT]: "التقرير",
    [ResourceType.MAINTENANCE]: "الصيانة",
    [ResourceType.EVALUATION]: "التقييم",
  };

  const actionNames: Record<Action, string> = {
    [Action.CREATE]: "إنشاء",
    [Action.READ]: "عرض",
    [Action.UPDATE]: "تعديل",
    [Action.DELETE]: "حذف",
    [Action.MANAGE]: "إدارة",
    [Action.ASSIGN]: "تخصيص",
    [Action.TRACK]: "تتبع",
    [Action.EXPORT]: "تصدير",
    [Action.SCHEDULE]: "جدولة",
    [Action.ANALYZE]: "تحليل",
  };

  const roleName = roleNames[userRole] || "مستخدم";
  const resourceName = resourceNames[resource] || resource;
  const actionName = actionNames[action] || action;

  return `عذراً، ${roleName} لا يملك صلاحية ${actionName} ${resourceName}. يرجى التواصل مع مدير النظام.`;
}

/**
 * Hook للتحقق من صلاحية واحدة
 */
export const useHasPermission = (permission: Permission): boolean => {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
};

/**
 * Hook للتحقق من نطاق البيانات
 */
export const useHasDataScope = (scope: DataScope): boolean => {
  const { hasDataScope } = usePermissions();
  return hasDataScope(scope);
};

/**
 * Hook للتحقق من إمكانية تنفيذ عملية
 */
export const useCanPerformAction = (
  resource: ResourceType,
  action: Action,
  context?: {
    resourceOwnerId?: string;
    resourceTenantId?: string;
  },
): boolean => {
  const { canPerformAction } = usePermissions();
  return canPerformAction(resource, action, context);
};

/**
 * Hook للتحقق من إمكانية إدارة دور
 */
export const useCanManageRole = (targetRole: UserRole): boolean => {
  const { canManageRole } = usePermissions();
  return canManageRole(targetRole);
};

/**
 * دالة مساعدة للحصول على الصلاحية المطلوبة لمورد وعملية معينة
 */
function getRequiredPermission(
  resource: ResourceType,
  action: Action,
): Permission {
  const permissionMap: Record<string, Permission> = {
    [`${ResourceType.USER}_${Action.CREATE}`]: Permission.USERS_CREATE,
    [`${ResourceType.USER}_${Action.READ}`]: Permission.USERS_VIEW_OWN,
    [`${ResourceType.USER}_${Action.UPDATE}`]: Permission.USERS_UPDATE_OWN,
    [`${ResourceType.USER}_${Action.DELETE}`]: Permission.USERS_DELETE_TENANT,
    [`${ResourceType.BUS}_${Action.CREATE}`]: Permission.BUSES_CREATE,
    [`${ResourceType.BUS}_${Action.READ}`]: Permission.BUSES_VIEW_ASSIGNED,
    [`${ResourceType.BUS}_${Action.UPDATE}`]: Permission.BUSES_UPDATE,
    [`${ResourceType.BUS}_${Action.DELETE}`]: Permission.BUSES_DELETE,
    [`${ResourceType.STUDENT}_${Action.CREATE}`]: Permission.STUDENTS_CREATE,
    [`${ResourceType.STUDENT}_${Action.READ}`]: Permission.STUDENTS_VIEW_OWN,
    [`${ResourceType.STUDENT}_${Action.UPDATE}`]: Permission.STUDENTS_UPDATE,
    [`${ResourceType.STUDENT}_${Action.DELETE}`]: Permission.STUDENTS_DELETE,
    [`${ResourceType.ROUTE}_${Action.CREATE}`]: Permission.ROUTES_CREATE,
    [`${ResourceType.ROUTE}_${Action.READ}`]: Permission.ROUTES_VIEW_ASSIGNED,
    [`${ResourceType.ROUTE}_${Action.UPDATE}`]: Permission.ROUTES_UPDATE,
    [`${ResourceType.ROUTE}_${Action.DELETE}`]: Permission.ROUTES_DELETE,
    [`${ResourceType.ATTENDANCE}_${Action.CREATE}`]:
      Permission.STUDENTS_MANAGE_ATTENDANCE,
    [`${ResourceType.ATTENDANCE}_${Action.READ}`]:
      Permission.STUDENTS_VIEW_ATTENDANCE,
    [`${ResourceType.ATTENDANCE}_${Action.UPDATE}`]:
      Permission.STUDENTS_MANAGE_ATTENDANCE,
    [`${ResourceType.REPORT}_${Action.READ}`]: Permission.REPORTS_VIEW_TENANT,
    [`${ResourceType.REPORT}_${Action.EXPORT}`]: Permission.REPORTS_EXPORT,
    [`${ResourceType.NOTIFICATION}_${Action.CREATE}`]:
      Permission.NOTIFICATIONS_SEND_TENANT,
    [`${ResourceType.NOTIFICATION}_${Action.READ}`]:
      Permission.NOTIFICATIONS_VIEW_TENANT,
    [`${ResourceType.MAINTENANCE}_${Action.CREATE}`]:
      Permission.MAINTENANCE_CREATE,
    [`${ResourceType.MAINTENANCE}_${Action.READ}`]:
      Permission.MAINTENANCE_VIEW_TENANT,
    [`${ResourceType.MAINTENANCE}_${Action.UPDATE}`]:
      Permission.MAINTENANCE_UPDATE,
    [`${ResourceType.EVALUATION}_${Action.CREATE}`]:
      Permission.EVALUATION_CREATE,
    [`${ResourceType.EVALUATION}_${Action.READ}`]:
      Permission.EVALUATION_VIEW_TENANT,
    [`${ResourceType.SYSTEM}_${Action.READ}`]: Permission.SYSTEM_ADMIN,
    [`${ResourceType.SYSTEM}_${Action.MANAGE}`]: Permission.SYSTEM_ADMIN,
    [`${ResourceType.SCHOOL}_${Action.READ}`]: Permission.SCHOOLS_VIEW_OWN,
    [`${ResourceType.SCHOOL}_${Action.UPDATE}`]: Permission.SCHOOLS_UPDATE,
  };

  const key = `${resource}_${action}`;
  return permissionMap[key] || Permission.USERS_VIEW_OWN;
}

export default usePermissions;
