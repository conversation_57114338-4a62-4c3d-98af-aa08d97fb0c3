-- Complete fix for infinite recursion in users table RLS policies
-- This migration removes problematic policies and creates safe alternatives

-- First, drop all existing RLS policies on users table to eliminate recursion
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can update all users" ON users;
DROP POLICY IF EXISTS "School managers can view tenant users" ON users;
DROP POLICY IF EXISTS "School managers can update tenant users" ON users;
DROP POLICY IF EXISTS "Public access" ON users;
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;

-- Temporarily disable <PERSON><PERSON> to create safe functions
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Create safe user profile functions that bypass RLS
CREATE OR REPLACE FUNCTION get_user_profile(user_id UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- This function runs with elevated privileges and bypasses RLS
  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.name,
    u.phone,
    u.role,
    u.tenant_id,
    u.is_active,
    u.avatar_url,
    u.metadata,
    u.created_at,
    u.updated_at
  FROM users u
  WHERE u.id = user_id;
END;
$$;

-- Create function to get current user profile
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- This function runs with elevated privileges and bypasses RLS
  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.name,
    u.phone,
    u.role,
    u.tenant_id,
    u.is_active,
    u.avatar_url,
    u.metadata,
    u.created_at,
    u.updated_at
  FROM users u
  WHERE u.id = auth.uid();
END;
$$;

-- Create function to get all users (for admin)
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_role user_role;
BEGIN
  -- Get the current user's role
  SELECT role INTO current_user_role
  FROM users
  WHERE id = auth.uid();
  
  -- Only allow admin users to access all users
  IF current_user_role = 'admin' THEN
    RETURN QUERY
    SELECT 
      u.id,
      u.email,
      u.name,
      u.phone,
      u.role,
      u.tenant_id,
      u.is_active,
      u.avatar_url,
      u.metadata,
      u.created_at,
      u.updated_at
    FROM users u
    ORDER BY u.created_at DESC;
  ELSE
    -- Return empty result for non-admin users
    RETURN;
  END IF;
END;
$$;

-- Create function to safely update user
CREATE OR REPLACE FUNCTION update_user_safely(
  user_id UUID,
  new_tenant_id UUID DEFAULT NULL,
  new_role TEXT DEFAULT NULL,
  new_name TEXT DEFAULT NULL,
  new_email TEXT DEFAULT NULL,
  new_phone TEXT DEFAULT NULL,
  new_is_active BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_role user_role;
  target_user_tenant_id UUID;
BEGIN
  -- Get the current user's role
  SELECT u.role INTO current_user_role
  FROM users u
  WHERE u.id = auth.uid();
  
  -- Get target user's current tenant_id
  SELECT u.tenant_id INTO target_user_tenant_id
  FROM users u
  WHERE u.id = user_id;
  
  -- Check permissions
  IF current_user_role = 'admin' OR 
     (current_user_role = 'school_manager' AND target_user_tenant_id = (
       SELECT tenant_id FROM users WHERE id = auth.uid()
     )) OR
     auth.uid() = user_id THEN
    
    -- Update the user
    UPDATE users SET
      tenant_id = COALESCE(new_tenant_id, users.tenant_id),
      role = COALESCE(new_role::user_role, users.role),
      name = COALESCE(new_name, users.name),
      email = COALESCE(new_email, users.email),
      phone = COALESCE(new_phone, users.phone),
      is_active = COALESCE(new_is_active, users.is_active),
      updated_at = NOW()
    WHERE users.id = user_id;
    
    -- Return the updated user
    RETURN QUERY
    SELECT 
      u.id,
      u.email,
      u.name,
      u.phone,
      u.role,
      u.tenant_id,
      u.is_active,
      u.avatar_url,
      u.metadata,
      u.created_at,
      u.updated_at
    FROM users u
    WHERE u.id = user_id;
  ELSE
    RAISE EXCEPTION 'Insufficient permissions to update user';
  END IF;
END;
$$;

-- Create function to assign school manager
CREATE OR REPLACE FUNCTION assign_school_manager(
  user_id UUID,
  tenant_id UUID
)
RETURNS JSON
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_role user_role;
  updated_user RECORD;
BEGIN
  -- Get the current user's role
  SELECT role INTO current_user_role
  FROM users
  WHERE id = auth.uid();
  
  -- Only admin can assign school managers
  IF current_user_role != 'admin' THEN
    RETURN json_build_object('success', false, 'error', 'Only admin can assign school managers');
  END IF;
  
  -- Update the user
  UPDATE users SET
    tenant_id = assign_school_manager.tenant_id,
    role = 'school_manager',
    updated_at = NOW()
  WHERE id = user_id
  RETURNING * INTO updated_user;
  
  IF updated_user IS NULL THEN
    RETURN json_build_object('success', false, 'error', 'User not found');
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'user', row_to_json(updated_user)
  );
END;
$$;

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role user_role;
BEGIN
  SELECT role INTO user_role
  FROM users
  WHERE id = auth.uid();
  
  RETURN user_role = 'admin';
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_profile(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_profile() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_safely(UUID, UUID, TEXT, TEXT, TEXT, TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION assign_school_manager(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;

-- Re-enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies
-- Policy for users to view their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT
  USING (auth.uid() = id);

-- Policy for users to update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy for admin access (using the is_admin function)
CREATE POLICY "Admin full access" ON users
  FOR ALL
  USING (is_admin())
  WITH CHECK (is_admin());

-- Policy for authenticated users to insert (for signup)
CREATE POLICY "Enable insert for authenticated users" ON users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Note: Most operations should now use the RPC functions instead of direct table access
-- This eliminates the recursion while maintaining security
