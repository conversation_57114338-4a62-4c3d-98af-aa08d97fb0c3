/**
 * RBAC Audit and Security Analysis Module
 * Comprehensive audit system for role-based access control
 */

import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "./rbac";
import { ACCESS_CONTROL_MATRIX } from "./permissionMatrix";

// Enhanced Role Definition with JSON-based Structure
export interface EnhancedRoleDefinition {
  role: UserRole;
  displayName: string;
  description: string;
  permissions: Permission[];
  dataScopes: DataScope[];
  restrictions: {
    tenantScope?: boolean;
    personalScope?: boolean;
    assignedScope?: boolean;
    childrenScope?: boolean;
    departmentScope?: boolean;
    routeScope?: boolean;
    busScope?: boolean;
    classScope?: boolean;
  };
  allowedActions: {
    [key in ResourceType]?: Action[];
  };
  hierarchyLevel: number;
  canManageRoles: UserRole[];
  maxTenants?: number;
  sessionTimeout?: number; // in minutes
  requiresMFA?: boolean;
  ipRestrictions?: string[];
  timeRestrictions?: {
    allowedHours?: { start: string; end: string };
    allowedDays?: number[]; // 0-6 (Sunday-Saturday)
  };
}

// JSON-based Permission Structure
export interface JSONPermissionStructure {
  role: string;
  permissions: string[];
  scope: {
    level: "global" | "tenant" | "personal" | "assigned" | "children";
    restrictions: {
      school_id?: "strict" | "flexible" | "none";
      department_id?: "strict" | "flexible" | "none";
      route_id?: "strict" | "flexible" | "none";
      bus_id?: "strict" | "flexible" | "none";
    };
  };
  actions: {
    [resource: string]: string[];
  };
  metadata: {
    created_at: string;
    updated_at: string;
    version: string;
    created_by: string;
  };
}

// Enhanced Role Definitions
export const ENHANCED_ROLE_DEFINITIONS: Record<
  UserRole,
  EnhancedRoleDefinition
> = {
  [UserRole.ADMIN]: {
    role: UserRole.ADMIN,
    displayName: "System Administrator",
    description: "Full system access across all tenants and resources",
    permissions: [
      Permission.SYSTEM_ADMIN,
      Permission.SYSTEM_SETTINGS,
      Permission.SYSTEM_AUDIT,
      Permission.SYSTEM_BACKUP,
      Permission.SYSTEM_ANALYTICS,
      Permission.SYSTEM_BILLING,
      Permission.SCHOOLS_VIEW_ALL,
      Permission.SCHOOLS_CREATE,
      Permission.SCHOOLS_UPDATE,
      Permission.SCHOOLS_DELETE,
      Permission.SCHOOLS_MANAGE_SETTINGS,
      Permission.USERS_VIEW_ALL,
      Permission.USERS_CREATE,
      Permission.USERS_UPDATE_ALL,
      Permission.USERS_DELETE_ALL,
      Permission.USERS_ASSIGN_ROLES,
      Permission.BUSES_VIEW_ALL,
      Permission.BUSES_CREATE,
      Permission.BUSES_UPDATE,
      Permission.BUSES_DELETE,
      Permission.ROUTES_VIEW_ALL,
      Permission.ROUTES_CREATE,
      Permission.ROUTES_UPDATE,
      Permission.ROUTES_DELETE,
      Permission.STUDENTS_VIEW_ALL,
      Permission.STUDENTS_CREATE,
      Permission.STUDENTS_UPDATE,
      Permission.STUDENTS_DELETE,
      Permission.REPORTS_VIEW_SYSTEM,
      Permission.REPORTS_EXPORT,
      Permission.NOTIFICATIONS_SEND_SYSTEM,
      Permission.MAINTENANCE_VIEW_ALL,
      Permission.MAINTENANCE_CREATE,
      Permission.MAINTENANCE_UPDATE,
      Permission.EVALUATION_VIEW_ALL,
      Permission.EVALUATION_ANALYZE,
      Permission.BILLING_VIEW_ALL,
      Permission.BILLING_MANAGE,
      Permission.EMERGENCY_MANAGE,
    ],
    dataScopes: [
      DataScope.GLOBAL,
      DataScope.TENANT,
      DataScope.PERSONAL,
      DataScope.ASSIGNED,
      DataScope.CHILDREN,
    ],
    restrictions: {},
    allowedActions: {
      [ResourceType.SYSTEM]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.CONFIGURE,
        Action.BACKUP,
        Action.RESTORE,
      ],
      [ResourceType.SCHOOL]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.USER]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
      ],
      [ResourceType.BUS]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
        Action.TRACK,
      ],
      [ResourceType.ROUTE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
      ],
      [ResourceType.STUDENT]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.REPORT]: [
        Action.CREATE,
        Action.READ,
        Action.EXPORT,
        Action.SCHEDULE,
        Action.ANALYZE,
      ],
      [ResourceType.NOTIFICATION]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.MAINTENANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.SCHEDULE,
        Action.APPROVE,
      ],
      [ResourceType.EVALUATION]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.ANALYZE,
      ],
      [ResourceType.BILLING]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.EMERGENCY]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
        Action.MONITOR,
      ],
    },
    hierarchyLevel: 1,
    canManageRoles: [
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
      UserRole.PARENT,
      UserRole.STUDENT,
    ],
    requiresMFA: true,
    sessionTimeout: 480, // 8 hours
  },

  [UserRole.SCHOOL_MANAGER]: {
    role: UserRole.SCHOOL_MANAGER,
    displayName: "School Manager",
    description: "Full management access within assigned school",
    permissions: [
      Permission.SCHOOLS_VIEW_OWN,
      Permission.SCHOOLS_MANAGE_SETTINGS,
      Permission.USERS_VIEW_TENANT,
      Permission.USERS_CREATE,
      Permission.USERS_UPDATE_TENANT,
      Permission.USERS_DELETE_TENANT,
      Permission.BUSES_VIEW_TENANT,
      Permission.BUSES_CREATE,
      Permission.BUSES_UPDATE,
      Permission.BUSES_ASSIGN_DRIVERS,
      Permission.BUSES_TRACK,
      Permission.ROUTES_VIEW_TENANT,
      Permission.ROUTES_CREATE,
      Permission.ROUTES_UPDATE,
      Permission.ROUTES_DELETE,
      Permission.STUDENTS_VIEW_TENANT,
      Permission.STUDENTS_CREATE,
      Permission.STUDENTS_UPDATE,
      Permission.STUDENTS_DELETE,
      Permission.STUDENTS_MANAGE_ATTENDANCE,
      Permission.REPORTS_VIEW_TENANT,
      Permission.REPORTS_EXPORT,
      Permission.NOTIFICATIONS_SEND_TENANT,
      Permission.MAINTENANCE_VIEW_TENANT,
      Permission.MAINTENANCE_CREATE,
      Permission.MAINTENANCE_SCHEDULE,
      Permission.EVALUATION_VIEW_TENANT,
      Permission.EVALUATION_ANALYZE,
    ],
    dataScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantScope: true,
    },
    allowedActions: {
      [ResourceType.SCHOOL]: [Action.READ, Action.UPDATE, Action.MANAGE],
      [ResourceType.USER]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.BUS]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.ASSIGN,
        Action.TRACK,
      ],
      [ResourceType.ROUTE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.ASSIGN,
      ],
      [ResourceType.STUDENT]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
      ],
      [ResourceType.REPORT]: [Action.READ, Action.EXPORT, Action.SCHEDULE],
      [ResourceType.NOTIFICATION]: [Action.CREATE, Action.READ, Action.MANAGE],
      [ResourceType.MAINTENANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.SCHEDULE,
      ],
      [ResourceType.EVALUATION]: [Action.CREATE, Action.READ, Action.ANALYZE],
    },
    hierarchyLevel: 2,
    canManageRoles: [
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
      UserRole.PARENT,
      UserRole.STUDENT,
    ],
    maxTenants: 1,
    sessionTimeout: 360, // 6 hours
  },

  [UserRole.SUPERVISOR]: {
    role: UserRole.SUPERVISOR,
    displayName: "Transport Supervisor",
    description: "Operational oversight of transport activities",
    permissions: [
      Permission.USERS_VIEW_TENANT,
      Permission.BUSES_VIEW_TENANT,
      Permission.BUSES_TRACK,
      Permission.ROUTES_VIEW_TENANT,
      Permission.STUDENTS_VIEW_TENANT,
      Permission.STUDENTS_MANAGE_ATTENDANCE,
      Permission.STUDENTS_VIEW_ATTENDANCE,
      Permission.REPORTS_VIEW_TENANT,
      Permission.NOTIFICATIONS_SEND_TENANT,
      Permission.MAINTENANCE_VIEW_TENANT,
      Permission.EVALUATION_CREATE,
    ],
    dataScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantScope: true,
    },
    allowedActions: {
      [ResourceType.USER]: [Action.READ],
      [ResourceType.BUS]: [Action.READ, Action.TRACK, Action.MONITOR],
      [ResourceType.ROUTE]: [Action.READ, Action.MONITOR],
      [ResourceType.STUDENT]: [Action.READ, Action.MANAGE],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
      ],
      [ResourceType.REPORT]: [Action.READ],
      [ResourceType.NOTIFICATION]: [Action.CREATE, Action.READ],
      [ResourceType.MAINTENANCE]: [Action.READ],
      [ResourceType.EVALUATION]: [Action.CREATE, Action.READ],
    },
    hierarchyLevel: 3,
    canManageRoles: [UserRole.DRIVER],
    maxTenants: 1,
    sessionTimeout: 240, // 4 hours
  },

  [UserRole.DRIVER]: {
    role: UserRole.DRIVER,
    displayName: "Bus Driver",
    description: "Operational access to assigned bus and route",
    permissions: [
      Permission.USERS_VIEW_OWN,
      Permission.USERS_UPDATE_OWN,
      Permission.BUSES_VIEW_ASSIGNED,
      Permission.BUSES_TRACK,
      Permission.ROUTES_VIEW_ASSIGNED,
      Permission.STUDENTS_VIEW_ATTENDANCE,
      Permission.STUDENTS_MANAGE_ATTENDANCE,
      Permission.MAINTENANCE_VIEW_ASSIGNED,
      Permission.MAINTENANCE_CREATE,
      Permission.EVALUATION_RESPOND,
    ],
    dataScopes: [
      DataScope.ASSIGNED,
      DataScope.PERSONAL,
      DataScope.BUS_BASED,
      DataScope.ROUTE_BASED,
    ],
    restrictions: {
      assignedScope: true,
      personalScope: true,
    },
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK, Action.MONITOR],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ],
      [ResourceType.ATTENDANCE]: [Action.CREATE, Action.READ, Action.UPDATE],
      [ResourceType.MAINTENANCE]: [Action.CREATE, Action.READ],
      [ResourceType.EVALUATION]: [Action.READ, Action.UPDATE],
      [ResourceType.EMERGENCY]: [Action.TRIGGER, Action.ALERT],
    },
    hierarchyLevel: 4,
    canManageRoles: [],
    maxTenants: 1,
    sessionTimeout: 480, // 8 hours (long shifts)
    timeRestrictions: {
      allowedHours: { start: "05:00", end: "20:00" },
    },
  },

  [UserRole.PARENT]: {
    role: UserRole.PARENT,
    displayName: "Parent/Guardian",
    description: "Access to children's information and services",
    permissions: [
      Permission.USERS_VIEW_OWN,
      Permission.USERS_UPDATE_OWN,
      Permission.BUSES_TRACK,
      Permission.ROUTES_VIEW_ASSIGNED,
      Permission.STUDENTS_VIEW_CHILDREN,
      Permission.STUDENTS_VIEW_ATTENDANCE,
      Permission.REPORTS_VIEW_PERSONAL,
      Permission.EVALUATION_CREATE,
      Permission.NOTIFICATIONS_VIEW_OWN,
    ],
    dataScopes: [DataScope.PERSONAL, DataScope.CHILDREN],
    restrictions: {
      childrenScope: true,
      personalScope: true,
    },
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ],
      [ResourceType.ATTENDANCE]: [Action.READ],
      [ResourceType.REPORT]: [Action.READ],
      [ResourceType.EVALUATION]: [Action.CREATE, Action.UPDATE],
      [ResourceType.NOTIFICATION]: [Action.READ],
      [ResourceType.EMERGENCY]: [Action.TRIGGER],
    },
    hierarchyLevel: 5,
    canManageRoles: [],
    sessionTimeout: 120, // 2 hours
  },

  [UserRole.STUDENT]: {
    role: UserRole.STUDENT,
    displayName: "Student",
    description: "Limited access to personal information",
    permissions: [
      Permission.USERS_VIEW_OWN,
      Permission.USERS_UPDATE_OWN,
      Permission.BUSES_TRACK,
      Permission.ROUTES_VIEW_ASSIGNED,
      Permission.STUDENTS_VIEW_OWN,
      Permission.STUDENTS_VIEW_ATTENDANCE,
      Permission.NOTIFICATIONS_VIEW_OWN,
    ],
    dataScopes: [DataScope.PERSONAL],
    restrictions: {
      personalScope: true,
    },
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ],
      [ResourceType.ATTENDANCE]: [Action.READ],
      [ResourceType.NOTIFICATION]: [Action.READ],
    },
    hierarchyLevel: 6,
    canManageRoles: [],
    sessionTimeout: 60, // 1 hour
    timeRestrictions: {
      allowedHours: { start: "06:00", end: "18:00" },
      allowedDays: [1, 2, 3, 4, 5], // Monday to Friday
    },
  },
};

// RBAC Audit Functions
export class RBACSecurityAudit {
  /**
   * Generate JSON-based permission structure for a role
   */
  static generateJSONPermissionStructure(
    role: UserRole,
  ): JSONPermissionStructure {
    const roleDefinition = ENHANCED_ROLE_DEFINITIONS[role];

    return {
      role: role,
      permissions: roleDefinition.permissions,
      scope: {
        level: this.determineScopeLevel(roleDefinition.dataScopes),
        restrictions: {
          school_id: roleDefinition.restrictions.tenantScope
            ? "strict"
            : "none",
          department_id: roleDefinition.restrictions.departmentScope
            ? "strict"
            : "none",
          route_id: roleDefinition.restrictions.routeScope ? "strict" : "none",
          bus_id: roleDefinition.restrictions.busScope ? "strict" : "none",
        },
      },
      actions: this.convertActionsToJSON(roleDefinition.allowedActions),
      metadata: {
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        version: "1.0.0",
        created_by: "system",
      },
    };
  }

  /**
   * Audit role permissions for security vulnerabilities
   */
  static auditRolePermissions(role: UserRole): {
    role: UserRole;
    securityLevel: "high" | "medium" | "low";
    vulnerabilities: string[];
    recommendations: string[];
    complianceScore: number;
  } {
    const roleDefinition = ENHANCED_ROLE_DEFINITIONS[role];
    const vulnerabilities: string[] = [];
    const recommendations: string[] = [];
    let securityScore = 100;

    // Check for overprivileged permissions
    if (
      roleDefinition.permissions.includes(Permission.SYSTEM_ADMIN) &&
      role !== UserRole.ADMIN
    ) {
      vulnerabilities.push("Non-admin role has system admin permissions");
      securityScore -= 30;
    }

    // Check for missing MFA requirement for privileged roles
    if (
      [UserRole.ADMIN, UserRole.SCHOOL_MANAGER].includes(role) &&
      !roleDefinition.requiresMFA
    ) {
      vulnerabilities.push("Privileged role missing MFA requirement");
      recommendations.push("Enable MFA for privileged roles");
      securityScore -= 20;
    }

    // Check session timeout
    if (roleDefinition.sessionTimeout && roleDefinition.sessionTimeout > 480) {
      vulnerabilities.push("Session timeout too long for security");
      recommendations.push("Reduce session timeout to maximum 8 hours");
      securityScore -= 10;
    }

    // Check data scope restrictions
    if (
      role !== UserRole.ADMIN &&
      roleDefinition.dataScopes.includes(DataScope.GLOBAL)
    ) {
      vulnerabilities.push("Non-admin role has global data access");
      securityScore -= 25;
    }

    const securityLevel =
      securityScore >= 80 ? "high" : securityScore >= 60 ? "medium" : "low";

    return {
      role,
      securityLevel,
      vulnerabilities,
      recommendations,
      complianceScore: securityScore,
    };
  }

  /**
   * Generate comprehensive RBAC audit report with real-time analysis
   */
  static generateAuditReport(): {
    timestamp: string;
    overallSecurityScore: number;
    roleAudits: ReturnType<typeof RBACSecurityAudit.auditRolePermissions>[];
    systemRecommendations: string[];
    complianceStatus: "compliant" | "needs_attention" | "critical";
    realTimeThreats: string[];
    actionableItems: {
      immediate: string[];
      shortTerm: string[];
      longTerm: string[];
    };
  } {
    const roleAudits = Object.values(UserRole).map((role) =>
      this.auditRolePermissions(role),
    );
    const overallScore =
      roleAudits.reduce((sum, audit) => sum + audit.complianceScore, 0) /
      roleAudits.length;

    const systemRecommendations = [
      "✅ Implement regular permission reviews (Monthly)",
      "✅ Enable audit logging for all privileged operations",
      "⚠️ Implement principle of least privilege",
      "⚠️ Regular security training for administrators",
      "🔴 CRITICAL: Implement automated permission monitoring",
      "🔴 CRITICAL: Add real-time threat detection",
      "🔴 CRITICAL: Implement session management with timeout",
    ];

    const realTimeThreats = this.detectRealTimeThreats();
    const actionableItems = this.generateActionableItems(
      roleAudits,
      overallScore,
    );

    const complianceStatus =
      overallScore >= 80
        ? "compliant"
        : overallScore >= 60
          ? "needs_attention"
          : "critical";

    return {
      timestamp: new Date().toISOString(),
      overallSecurityScore: overallScore,
      roleAudits,
      systemRecommendations,
      complianceStatus,
      realTimeThreats,
      actionableItems,
    };
  }

  /**
   * Detect real-time security threats
   */
  private static detectRealTimeThreats(): string[] {
    const threats: string[] = [];

    // Check for privilege escalation attempts
    if (this.hasPrivilegeEscalationAttempts()) {
      threats.push("🚨 Privilege escalation attempts detected");
    }

    // Check for unusual access patterns
    if (this.hasUnusualAccessPatterns()) {
      threats.push("⚠️ Unusual access patterns detected");
    }

    // Check for failed authentication attempts
    if (this.hasExcessiveFailedLogins()) {
      threats.push("🔴 Excessive failed login attempts");
    }

    return threats;
  }

  /**
   * Generate actionable security items
   */
  private static generateActionableItems(
    roleAudits: any[],
    overallScore: number,
  ): {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  } {
    const immediate: string[] = [];
    const shortTerm: string[] = [];
    const longTerm: string[] = [];

    if (overallScore < 60) {
      immediate.push(
        "🚨 URGENT: Review and fix critical security vulnerabilities",
      );
      immediate.push("🚨 URGENT: Implement emergency access controls");
    }

    if (overallScore < 80) {
      shortTerm.push("⚠️ Implement comprehensive audit logging");
      shortTerm.push("⚠️ Add multi-factor authentication for privileged users");
      shortTerm.push("⚠️ Review and update permission matrix");
    }

    longTerm.push("📋 Implement automated security scanning");
    longTerm.push("📋 Develop incident response procedures");
    longTerm.push("📋 Create security awareness training program");

    return { immediate, shortTerm, longTerm };
  }

  /**
   * Check for privilege escalation attempts
   */
  private static hasPrivilegeEscalationAttempts(): boolean {
    // In production, this would check audit logs
    return false;
  }

  /**
   * Check for unusual access patterns
   */
  private static hasUnusualAccessPatterns(): boolean {
    // In production, this would analyze access logs
    return false;
  }

  /**
   * Check for excessive failed logins
   */
  private static hasExcessiveFailedLogins(): boolean {
    // In production, this would check authentication logs
    return false;
  }

  /**
   * Validate database schema compliance with RBAC model
   */
  static validateDatabaseSchema(): {
    isCompliant: boolean;
    missingTables: string[];
    missingColumns: string[];
    recommendations: string[];
  } {
    const requiredTables = [
      "users",
      "tenants",
      "buses",
      "routes",
      "students",
      "attendance",
      "notifications",
      "bus_maintenance",
      "evaluations",
      "complaints",
    ];

    const requiredColumns = {
      users: ["id", "email", "role", "tenant_id", "is_active", "metadata"],
      tenants: ["id", "name", "is_active", "settings"],
      buses: ["id", "tenant_id", "driver_id", "is_active"],
      routes: ["id", "tenant_id", "bus_id", "is_active"],
      students: ["id", "tenant_id", "parent_id", "is_active"],
    };

    // This would be implemented with actual database introspection
    // For now, returning a mock validation
    return {
      isCompliant: true,
      missingTables: [],
      missingColumns: [],
      recommendations: [
        "Add audit trail tables for sensitive operations",
        "Implement row-level security policies",
        "Add indexes for performance optimization",
        "Implement data encryption for sensitive fields",
      ],
    };
  }

  private static determineScopeLevel(
    dataScopes: DataScope[],
  ): "global" | "tenant" | "personal" | "assigned" | "children" {
    if (dataScopes.includes(DataScope.GLOBAL)) return "global";
    if (dataScopes.includes(DataScope.TENANT)) return "tenant";
    if (dataScopes.includes(DataScope.CHILDREN)) return "children";
    if (dataScopes.includes(DataScope.ASSIGNED)) return "assigned";
    return "personal";
  }

  private static convertActionsToJSON(allowedActions: {
    [key in ResourceType]?: Action[];
  }): { [resource: string]: string[] } {
    const result: { [resource: string]: string[] } = {};
    Object.entries(allowedActions).forEach(([resource, actions]) => {
      result[resource] = actions || [];
    });
    return result;
  }
}

export default RBACSecurityAudit;
