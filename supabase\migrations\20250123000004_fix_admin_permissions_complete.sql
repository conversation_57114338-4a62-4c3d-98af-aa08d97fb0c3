-- Fix admin permissions and RLS policies for complete access

-- Drop existing policies that might be restrictive
DROP POLICY IF EXISTS "Users can view own tenant users" ON users;
DROP POLICY IF EXISTS "Users can view own tenant students" ON students;
DROP POLICY IF EXISTS "Users can view own tenant buses" ON buses;
DROP POLICY IF EXISTS "Users can view own tenant routes" ON routes;
DROP POLICY IF EXISTS "Users can view own tenant attendance" ON attendance;
DROP POLICY IF EXISTS "Users can view own tenant notifications" ON notifications;
DROP POLICY IF EXISTS "Users can view own tenant tenants" ON tenants;

-- Create comprehensive admin policies for users table
CREATE POLICY "Admin full access to users"
ON users FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant users"
ON users FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can update own tenant users"
ON users FOR UPDATE
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can insert own tenant users"
ON users FOR INSERT
WITH CHECK (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can delete own tenant users"
ON users FOR DELETE
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for students table
CREATE POLICY "Admin full access to students"
ON students FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant students"
ON students FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can manage own tenant students"
ON students FOR ALL
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for buses table
CREATE POLICY "Admin full access to buses"
ON buses FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant buses"
ON buses FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can manage own tenant buses"
ON buses FOR ALL
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for routes table
CREATE POLICY "Admin full access to routes"
ON routes FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant routes"
ON routes FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can manage own tenant routes"
ON routes FOR ALL
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for attendance table
CREATE POLICY "Admin full access to attendance"
ON attendance FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant attendance"
ON attendance FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can manage own tenant attendance"
ON attendance FOR ALL
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for notifications table
CREATE POLICY "Admin full access to notifications"
ON notifications FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant notifications"
ON notifications FOR SELECT
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR user_id = auth.uid()
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can manage own tenant notifications"
ON notifications FOR ALL
USING (
  tenant_id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create comprehensive admin policies for tenants table
CREATE POLICY "Admin full access to tenants"
ON tenants FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

CREATE POLICY "Users can view own tenant"
ON tenants FOR SELECT
USING (
  id = (
    SELECT tenant_id FROM users 
    WHERE id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM users u 
    WHERE u.id = auth.uid() 
    AND u.role = 'admin'
  )
);

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';