-- إصلاح سياسات إنشاء المستخدمين وحل مشاكل RLS
-- Fix user creation policies and resolve RLS issues

BEGIN;

-- 1. إزالة جميع السياسات المتضاربة لجدول users
DROP POLICY IF EXISTS "users_select_policy" ON users;
DROP POLICY IF EXISTS "users_insert_policy" ON users;
DROP POLICY IF EXISTS "users_update_policy" ON users;
DROP POLICY IF EXISTS "users_delete_policy" ON users;
DROP POLICY IF EXISTS "Enable read access for all users" ON users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;
DROP POLICY IF EXISTS "Admin can view all users" ON users;
DROP POLICY IF EXISTS "Admin can insert users" ON users;
DROP POLICY IF EXISTS "Admin can update all users" ON users;
DROP POLICY IF EXISTS "Admin can delete users" ON users;
DROP POLICY IF EXISTS "School managers can view users in their tenant" ON users;
DROP POLICY IF EXISTS "School managers can insert users in their tenant" ON users;
DROP POLICY IF EXISTS "School managers can update users in their tenant" ON users;

-- 2. تعطيل RLS مؤقتاً لإنشاء السياسات الجديدة
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- 3. إنشاء دالة مساعدة للتحقق من الصلاحيات
CREATE OR REPLACE FUNCTION auth.user_role()
RETURNS text
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    (SELECT role FROM users WHERE id = auth.uid()),
    'anonymous'
  );
$$;

-- 4. إنشاء دالة للتحقق من المستأجر
CREATE OR REPLACE FUNCTION auth.user_tenant_id()
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT tenant_id FROM users WHERE id = auth.uid();
$$;

-- 5. إنشاء سياسات جديدة ومبسطة
-- سياسة القراءة
CREATE POLICY "users_read_policy" ON users
  FOR SELECT
  USING (
    -- المستخدم يمكنه رؤية ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن يمكنه رؤية جميع المستخدمين
    auth.user_role() = 'admin'
    OR
    -- مدير المدرسة يمكنه رؤية مستخدمي مدرسته
    (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
    OR
    -- المشرف يمكنه رؤية مستخدمي مدرسته
    (auth.user_role() = 'supervisor' AND tenant_id = auth.user_tenant_id())
  );

-- سياسة الإدراج (للمستخدمين الجدد)
CREATE POLICY "users_insert_policy" ON users
  FOR INSERT
  WITH CHECK (
    -- السماح بإنشاء المستخدم إذا لم يكن هناك مستخدم مصادق عليه (للتسجيل الأول)
    auth.uid() IS NULL
    OR
    -- السماح للمستخدم بإنشاء ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن يمكنه إنشاء أي مستخدم
    auth.user_role() = 'admin'
    OR
    -- مدير المدرسة يمكنه إنشاء مستخدمين في مدرسته
    (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
  );

-- سياسة التحديث
CREATE POLICY "users_update_policy" ON users
  FOR UPDATE
  USING (
    -- المستخدم يمكنه تحديث ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن يمكنه تحديث أي مستخدم
    auth.user_role() = 'admin'
    OR
    -- مدير المدرسة يمكنه تحديث مستخدمي مدرسته
    (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
  );

-- سياسة الحذف
CREATE POLICY "users_delete_policy" ON users
  FOR DELETE
  USING (
    -- الأدمن فقط يمكنه حذف المستخدمين
    auth.user_role() = 'admin'
    OR
    -- مدير المدرسة يمكنه حذف مستخدمي مدرسته (عدا نفسه)
    (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id() AND id != auth.uid())
  );

-- 6. إعادة تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 7. إنشاء دالة لإنشاء مستخدم جديد بأمان
CREATE OR REPLACE FUNCTION public.create_user_profile(
  user_id uuid,
  user_email text,
  user_name text,
  user_role text DEFAULT 'student',
  user_tenant_id uuid DEFAULT NULL
)
RETURNS users
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user users;
BEGIN
  -- التحقق من وجود المستخدم
  SELECT * INTO new_user FROM users WHERE id = user_id;
  
  IF new_user.id IS NOT NULL THEN
    -- المستخدم موجود، إرجاع البيانات الحالية
    RETURN new_user;
  END IF;

  -- إنشاء مستخدم جديد
  INSERT INTO users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    user_id,
    user_email,
    user_name,
    user_role,
    user_tenant_id,
    true,
    NOW(),
    NOW()
  ) RETURNING * INTO new_user;

  RETURN new_user;
END;
$$;

-- 8. منح الصلاحيات للدالة
GRANT EXECUTE ON FUNCTION public.create_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_profile TO anon;

-- 9. إنشاء دالة للتحقق من إمكانية إنشاء المستخدم
CREATE OR REPLACE FUNCTION public.can_create_user(target_role text, target_tenant_id uuid DEFAULT NULL)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role text;
  current_tenant_id uuid;
BEGIN
  -- الحصول على دور المستخدم الحالي
  SELECT role, tenant_id INTO current_user_role, current_tenant_id
  FROM users WHERE id = auth.uid();

  -- إذا لم يكن هناك مستخدم مصادق عليه، السماح بالإنشاء (للتسجيل الأول)
  IF auth.uid() IS NULL THEN
    RETURN true;
  END IF;

  -- الأدمن يمكنه إنشاء أي مستخدم
  IF current_user_role = 'admin' THEN
    RETURN true;
  END IF;

  -- مدير المدرسة يمكنه إنشاء مستخدمين في مدرسته (عدا الأدمن)
  IF current_user_role = 'school_manager' AND target_role != 'admin' AND target_tenant_id = current_tenant_id THEN
    RETURN true;
  END IF;

  RETURN false;
END;
$$;

-- 10. إنشاء trigger لتسجيل الأحداث الأمنية
CREATE OR REPLACE FUNCTION public.log_user_creation()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO security_events (
    event_type,
    severity,
    description,
    user_id,
    tenant_id,
    metadata,
    created_at
  ) VALUES (
    'USER_CREATED',
    'INFO',
    'New user profile created',
    NEW.id,
    NEW.tenant_id,
    jsonb_build_object(
      'email', NEW.email,
      'role', NEW.role,
      'created_by', auth.uid()
    ),
    NOW()
  );
  
  RETURN NEW;
END;
$$;

-- إنشاء trigger
DROP TRIGGER IF EXISTS user_creation_log ON users;
CREATE TRIGGER user_creation_log
  AFTER INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION public.log_user_creation();

-- 11. تحديث دالة التحقق من سلامة النظام
CREATE OR REPLACE FUNCTION public.check_user_system_health()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  policy_count integer;
  function_count integer;
BEGIN
  -- عد السياسات النشطة
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'users' AND schemaname = 'public';

  -- عد الدوال المساعدة
  SELECT COUNT(*) INTO function_count
  FROM pg_proc p
  JOIN pg_namespace n ON p.pronamespace = n.oid
  WHERE n.nspname = 'public' 
  AND p.proname IN ('create_user_profile', 'can_create_user', 'log_user_creation');

  result := jsonb_build_object(
    'status', 'HEALTHY',
    'timestamp', NOW(),
    'policies_count', policy_count,
    'functions_count', function_count,
    'rls_enabled', (
      SELECT row_security 
      FROM pg_tables 
      WHERE tablename = 'users' AND schemaname = 'public'
    )
  );

  RETURN result;
END;
$$;

COMMIT;
