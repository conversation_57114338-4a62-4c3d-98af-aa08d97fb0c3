import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  User as UserIcon,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Loader2,
} from "lucide-react";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import { UserModal } from "../../components/users/UserModal";
import { UserDebugPanel } from "../../components/debug/UserDebugPanel";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { UserRole } from "../../types";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { PermissionGuard } from "../../components/auth/PermissionGuard";
import { Permission, ResourceType } from "../../lib/rbac";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

export const UsersPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { users, loading, error, refreshData } = useDatabase();

  // Force refresh data when component mounts
  React.useEffect(() => {
    console.log("UsersPage: Component mounted, refreshing data...");

    // Import and run database debug
    const runDebug = async () => {
      try {
        const { debugDatabase } = await import("../../utils/debugDatabase");
        await debugDatabase();

        await refreshData();
        console.log("UsersPage: Data refresh completed");
      } catch (error) {
        console.error("UsersPage: Error refreshing data:", error);
      }
    };

    runDebug();
  }, []);

  // Debug: Log users data
  React.useEffect(() => {
    console.log("UsersPage: Users from database:", users);
    console.log("UsersPage: Users count:", users.length);
    console.log("UsersPage: Current user role:", user?.role);
    console.log("UsersPage: Current user tenant_id:", user?.tenant_id);
    console.log("UsersPage: Loading state:", loading);
    console.log("UsersPage: Error state:", error);
  }, [users, user, loading, error]);

  const [searchQuery, setSearchQuery] = useState("");
  const [filterRole, setFilterRole] = useState<UserRole | "all">("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<
    Tables<"users"> | undefined
  >();
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [isBulkModalOpen, setIsBulkModalOpen] = useState(false);
  const [bulkAction, setBulkAction] = useState<"role" | "status" | null>(null);
  const isAdminOrSchoolManager =
    user?.role === "admin" || user?.role === "school_manager";

  const handleOpenModal = (user?: Tables<"users">) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedUser(undefined);
    setIsModalOpen(false);
  };

  const handleSubmit = async (
    data: Partial<Tables<"users">> & { password?: string },
  ) => {
    try {
      if (selectedUser) {
        // Update existing user
        const updateData = { ...data };
        delete updateData.password; // Remove password from update data

        const { error } = await supabase
          .from("users")
          .update(updateData)
          .eq("id", selectedUser.id);

        if (error) {
          throw new Error(
            `Supabase update error: ${error.message || ""} ${error.details || ""} ${error.code || ""}`
          );
        }

        // Update password if provided
        if (data.password && data.password.trim() !== "") {
          const { data: passwordResult, error: passwordError } =
            await supabase.functions.invoke(
              "supabase-functions-update-user-password",
              {
                body: {
                  userId: selectedUser.id,
                  password: data.password,
                },
              },
            );
          if (passwordError) {
            throw new Error(
              `Password update error: ${passwordError.message || ""} ${passwordError.details || ""} ${passwordError.code || ""}`
            );
          }
        }
      } else {
        // Create new user with auth
        if (!data.password || data.password.trim() === "") {
          throw new Error("Password is required for new users");
        }

        // Validate data before sending
        const userData = {
          email: data.email?.trim(),
          password: data.password?.trim(),
          name: data.name?.trim(),
          role: data.role,
          tenant_id: data.tenant_id || user?.tenant_id || null,
          phone: data.phone?.trim() || null,
          is_active: data.is_active ?? true,
        };

        console.log("Creating user with data:", {
          ...userData,
          password: !!userData.password,
        });

        // Additional validation
        if (
          !userData.email ||
          !userData.password ||
          !userData.name ||
          !userData.role
        ) {
          throw new Error(
            "Missing required fields: email, password, name, or role"
          );
        }

        let result, createError;
        try {
          const response = await supabase.functions.invoke(
            "supabase-functions-create-user",
            {
              body: userData,
            },
          );
          result = response.data;
          createError = response.error;
        } catch (err) {
          console.error("Function invocation failed:", err);
          createError = err;
        }

        console.log("Edge function response:", { result, createError });

        // Enhanced error handling to capture response body
        if (createError) {
          console.error("Full edge function error:", {
            message: createError.message,
            details: createError.details,
            hint: createError.hint,
            code: createError.code,
            context: createError.context,
          });

          // Try to get the actual response body if it's a Response object
          if (createError.context && createError.context instanceof Response) {
            try {
              const responseText = await createError.context.text();
              console.error("Response body:", responseText);

              // Try to parse as JSON
              try {
                const responseJson = JSON.parse(responseText);
                console.error("Parsed response:", responseJson);
                if (responseJson.error) {
                  throw new Error(responseJson.error);
                }
              } catch (parseError) {
                console.error("Could not parse response as JSON:", parseError);
                throw new Error(`Edge function error: ${responseText}`);
              }
            } catch (textError) {
              console.error("Could not read response text:", textError);
              throw new Error(createError.message || "Failed to create user");
            }
          }

          throw new Error(createError.message || "Failed to create user");
        }

        if (!result) {
          throw new Error("No response from server");
        }

        if (!result.success) {
          console.error("Edge function failed:", result);
          throw new Error(result.error || "Failed to create user");
        }

        console.log("User created successfully:", result);
      }

      // Force refresh data to ensure the UI is updated
      await refreshData();

      // Close the modal after successful submission
      handleCloseModal();
    } catch (error) {
      // طباعة الخطأ بشكل مفصل
      if (typeof error === "object") {
        console.error("Error saving user:", error, JSON.stringify(error));
      } else {
        console.error("Error saving user:", error);
      }
      throw error;
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm(t("users.deleteConfirmation"))) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase.from("users").delete().eq("id", id);

      if (error) throw error;

      await refreshData();
    } catch (error) {
      console.error("Error deleting user:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Use enhanced RBAC-based data filtering
  const { filterDataByRole } = useRBACEnhancedSecurity();
  const rbacFilteredUsers = filterDataByRole(users, "user", "id");

  // Apply additional filters
  const filteredUsers = rbacFilteredUsers
    .filter((u) => {
      // Filter by role
      if (filterRole === "all") return true;
      return u.role === filterRole;
    })
    .filter((u) => {
      // Search by name or email
      if (!searchQuery) return true;
      return (
        (u.name && u.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (u.email && u.email.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    });

  // Debug: Log filtered users count for admin
  React.useEffect(() => {
    if (user?.role === "admin") {
      console.log("UsersPage: Admin user - showing all users");
      console.log("UsersPage: Total users available:", users.length);
      console.log("UsersPage: Filtered users count:", filteredUsers.length);
    }
  }, [user?.role, users.length, filteredUsers.length]);

  // Debug: Log filtered users
  React.useEffect(() => {
    console.log("UsersPage: Filtered users:", filteredUsers.length);
    console.log("UsersPage: All users (raw):", users.length);
    if (users.length > 0) {
      console.log("UsersPage: Sample user data:", users[0]);
    }
  }, [filteredUsers, users]);

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } =
    usePagination(filteredUsers.length, 10);

  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-error-600 dark:text-error-400">
          {error.message}
        </div>
      </div>
    );
  }

  // دوال مساعدة لمنع تكرار الكود
  const handleSelectUser = (id: string, checked: boolean) => {
    setSelectedUserIds((prev) =>
      checked ? [...prev, id] : prev.filter((uid) => uid !== id)
    );
  };
  const handleSelectAll = (checked: boolean) => {
    setSelectedUserIds(checked ? paginatedUsers.map((u) => u.id) : []);
  };
  const handleBulkAction = (action: "role" | "status") => {
    setBulkAction(action);
    setIsBulkModalOpen(true);
  };
  const handleBulkSubmit = async (data: Partial<Tables<"users">>) => {
    try {
      const { error } = await supabase.from("users").update(data).in("id", selectedUserIds);
      if (error) {
        throw new Error(
          `Bulk update error: ${error.message || ""} ${error.details || ""} ${error.code || ""}`
        );
      }
      setSelectedUserIds([]);
      setIsBulkModalOpen(false);
      await refreshData();
    } catch (e) {
      if (typeof e === "object") {
        alert("Bulk update failed: " + JSON.stringify(e));
      } else {
        alert("Bulk update failed: " + String(e));
      }
    }
  };
  const handleBulkDelete = async () => {
    if (!window.confirm("هل أنت متأكد من حذف جميع المستخدمين المحددين؟")) return;
    try {
      await supabase.from("users").delete().in("id", selectedUserIds);
      setSelectedUserIds([]);
      await refreshData();
    } catch (e) {
      alert("Bulk delete failed");
    }
  };

  return (
    <ResponsiveLayout>
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t("nav.users")}
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t("common.view")}, {t("common.add")}, {t("common.edit")}{" "}
                {t("nav.users").toLowerCase()}
              </p>
            </div>

            <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => {
                  console.log("Manual refresh triggered");
                  refreshData();
                }}
                disabled={loading}
              >
                <Loader2 size={16} className={loading ? "animate-spin" : ""} />
                {loading ? "Refreshing..." : "Refresh"}
              </Button>
              <PermissionGuard permissions={[Permission.USERS_CREATE]}>
                <Button
                  className="flex items-center gap-2"
                  onClick={() => handleOpenModal()}
                >
                  <Plus size={16} />
                  {t("users.addUser")}
                </Button>
              </PermissionGuard>
              {/* أزرار جماعية */}
              {isAdminOrSchoolManager && selectedUserIds.length > 0 && (
                <>
                  <Button
                    className="flex items-center gap-2"
                    onClick={() => handleBulkAction("role")}
                  >
                    🛠️ {t("users.bulkRole")}
                  </Button>
                  <Button
                    className="flex items-center gap-2"
                    onClick={() => handleBulkAction("status")}
                  >
                    ⏯️ {t("users.bulkStatus")}
                  </Button>
                  <Button
                    className="flex items-center gap-2"
                    variant="destructive"
                    onClick={handleBulkDelete}
                  >
                    <Trash2 size={16} /> {t("users.bulkDelete")}
                  </Button>
                </>
              )}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="relative max-w-xs w-full">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder={`${t("common.search")} ${t("nav.users").toLowerCase()}`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Filter size={18} className="text-gray-400" />
                    </div>
                    <select
                      className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                      value={filterRole}
                      onChange={(e) =>
                        setFilterRole(e.target.value as UserRole | "all")
                      }
                    >
                      <option value="all">
                        {t("common.filter")}: {t("common.all")}
                      </option>
                      <option value={UserRole.ADMIN}>{t("users.admin")}</option>
                      <option value={UserRole.SCHOOL_MANAGER}>
                        {t("users.schoolManager")}
                      </option>
                      <option value={UserRole.SUPERVISOR}>
                        {t("users.supervisor")}
                      </option>
                      <option value={UserRole.DRIVER}>
                        {t("users.driver")}
                      </option>
                      <option value={UserRole.PARENT}>
                        {t("users.parent")}
                      </option>
                      <option value={UserRole.STUDENT}>
                        {t("users.student")}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    {isAdminOrSchoolManager && (
                      <th className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={
                            selectedUserIds.length === paginatedUsers.length &&
                            paginatedUsers.length > 0
                          }
                          onChange={(e) => handleSelectAll(e.target.checked)}
                        />
                      </th>
                    )}
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      {t("users.name")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      {t("users.role")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      {t("users.status")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      {t("common.actions")}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {paginatedUsers.length > 0 ? (
                    paginatedUsers.map((u) => (
                      <tr
                        key={u.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        {isAdminOrSchoolManager && (
                          <td className="px-4 py-4">
                            <input
                              type="checkbox"
                              checked={selectedUserIds.includes(u.id)}
                              onChange={(e) => handleSelectUser(u.id, e.target.checked)}
                            />
                          </td>
                        )}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              {u.avatar_url ? (
                                <img
                                  src={u.avatar_url}
                                  alt={u.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              ) : (
                                <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                                  <UserIcon size={20} />
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {u.name}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {u.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-800/20 dark:text-primary-400">
                            {u.role === "admin"
                              ? t("users.admin")
                              : u.role === "school_manager"
                                ? t("users.schoolManager")
                                : u.role === "supervisor"
                                  ? t("users.supervisor")
                                  : u.role === "driver"
                                    ? t("users.driver")
                                    : u.role === "parent"
                                      ? t("users.parent")
                                      : u.role === "student"
                                        ? t("users.student")
                                        : u.role}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col gap-1">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                u.is_active
                                  ? "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400"
                                  : "bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400"
                              }`}
                            >
                              {u.is_active ? "Active" : "Inactive"}
                            </span>
                            {u.tenant_id && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                School: {u.tenant_id.substring(0, 8)}...
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            {/* زر عرض التفاصيل */}
                            <button
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1"
                              title={t("common.view")}
                              onClick={() => handleOpenModal(u)}
                            >
                              <Eye size={18} />
                            </button>
                            {/* أزرار الإدارة تظهر فقط للأدوار المخولة */}
                            {isAdminOrSchoolManager && (
                              <>
                                {/* زر التعديل */}
                                <button
                                  className="text-warning-600 hover:text-warning-900 dark:text-warning-400 dark:hover:text-warning-300 p-1"
                                  title={t("common.edit")}
                                  onClick={() => handleOpenModal(u)}
                                >
                                  <Edit size={18} />
                                </button>
                                <button
                                  className="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300 p-1"
                                  title={t("common.delete")}
                                  onClick={() => handleDelete(u.id)}
                                  disabled={isDeleting}
                                >
                                  <Trash2 size={18} />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={5}
                        className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        {loading ? (
                          <div className="flex items-center justify-center">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading users...
                          </div>
                        ) : searchQuery || filterRole !== "all" ? (
                          t("common.noResultsFound")
                        ) : (
                          <div className="py-8">
                            <UserIcon
                              size={48}
                              className="mx-auto mb-4 text-gray-300"
                            />
                            <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              No users found
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Create your first user to get started
                            </p>
                          </div>
                        )}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {t("common.showing")} {startIndex + 1} - {endIndex}{" "}
                    {t("common.of")} {filteredUsers.length}{" "}
                    {t("nav.users").toLowerCase()}
                  </div>
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <UserModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        user={selectedUser ? { ...selectedUser, avatar_url: selectedUser.avatar_url ?? null } : undefined}
      />
      {/* نافذة التعديل الجماعي */}
      {isBulkModalOpen && (
        <UserModal
          isOpen={isBulkModalOpen}
          onClose={() => setIsBulkModalOpen(false)}
          onSubmit={handleBulkSubmit}
          user={undefined}
          bulkMode={bulkAction}
        />
      )}

      {/* Debug Panel - Remove in production */}
      <UserDebugPanel />
    </ResponsiveLayout>
  );
};
