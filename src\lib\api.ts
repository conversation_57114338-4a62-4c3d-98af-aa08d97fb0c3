import { supabase } from "./supabase";
import type { Database } from "./database.types";

// API call caching and debouncing
const apiCache = new Map<
  string,
  { data: any; timestamp: number; promise?: Promise<any> }
>();
const pendingRequests = new Map<string, Promise<any>>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const DEBOUNCE_DELAY = 300; // 300ms

// Debounce function
function debounce<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let timeoutId: NodeJS.Timeout;
  let lastPromise: Promise<ReturnType<T>>;

  return (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId);

      timeoutId = setTimeout(async () => {
        try {
          const result = await func(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  };
}

// Cache helper functions
function getCacheKey(operation: string, params: any): string {
  return `${operation}_${JSON.stringify(params)}`;
}

function getCachedData<T>(cacheKey: string): T | null {
  const cached = apiCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
}

function setCachedData<T>(cacheKey: string, data: T): void {
  apiCache.set(cacheKey, { data, timestamp: Date.now() });
}

// Prevent duplicate requests
function withRequestDeduplication<T>(
  cacheKey: string,
  requestFn: () => Promise<T>,
): Promise<T> {
  // Check if there's already a pending request
  const existingRequest = pendingRequests.get(cacheKey);
  if (existingRequest) {
    return existingRequest;
  }

  // Create new request
  const request = requestFn().finally(() => {
    pendingRequests.delete(cacheKey);
  });

  pendingRequests.set(cacheKey, request);
  return request;
}

export type Tables<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Row"];

export type Inserts<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Insert"];

export type Updates<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Update"];

// Evaluation functions
export async function createEvaluation(
  tenantId: string,
  userId: string,
  targetType: string,
  targetId: string,
  rating: number,
  comment?: string,
) {
  const { data, error } = await supabase
    .from("evaluations")
    .insert({
      tenant_id: tenantId,
      user_id: userId,
      target_type: targetType,
      target_id: targetId,
      rating,
      comment,
    })
    .select();

  if (error) throw error;

  // Check if rating is low and send notification
  if (rating <= 2) {
    try {
      const { notificationService } = await import(
        "../lib/notificationService"
      );
      await notificationService.sendLowRatingNotification(
        tenantId,
        targetType,
        targetId,
        rating,
      );
    } catch (notificationError) {
      console.error(
        "Error sending low rating notification:",
        notificationError,
      );
    }
  }

  return data[0];
}

export async function getEvaluations(
  tenantId: string,
  targetType?: string,
  targetId?: string,
) {
  let query = supabase
    .from("evaluations")
    .select(
      `
      *,
      user:users!user_id(
        id,
        name,
        avatar_url
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (targetType) {
    query = query.eq("target_type", targetType);
  }

  if (targetId) {
    query = query.eq("target_id", targetId);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

export async function getAverageRating(
  tenantId: string,
  targetType: string,
  targetId: string,
): Promise<number> {
  const { data, error } = await supabase
    .from("evaluations")
    .select("rating")
    .eq("tenant_id", tenantId)
    .eq("target_type", targetType)
    .eq("target_id", targetId);

  if (error) throw error;
  if (!data || data.length === 0) return 0;

  const sum = data.reduce((acc, evaluation) => acc + evaluation.rating, 0);
  return sum / data.length;
}

// Complaint functions
export async function createComplaint(
  tenantId: string,
  userId: string,
  type: string,
  targetId: string,
  title: string,
  description: string,
) {
  const { data, error } = await supabase
    .from("complaints")
    .insert({
      tenant_id: tenantId,
      user_id: userId,
      type,
      target_id: targetId,
      title,
      description,
      status: "pending",
    })
    .select();

  if (error) throw error;
  return data[0];
}

export async function getComplaints(
  tenantId: string,
  status?: string,
  userId?: string,
) {
  let query = supabase
    .from("complaints")
    .select(
      `
      *,
      user:users!user_id(
        id,
        name,
        avatar_url
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (status) {
    query = query.eq("status", status);
  }

  if (userId) {
    query = query.eq("user_id", userId);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

export async function updateComplaintStatus(
  complaintId: string,
  status: string,
  response?: string,
) {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString(),
  };

  if (response) {
    updateData.response = response;
  }

  if (status === "resolved") {
    updateData.resolved_at = new Date().toISOString();
  }

  const { data, error } = await supabase
    .from("complaints")
    .update(updateData)
    .eq("id", complaintId)
    .select();

  if (error) throw error;
  return data[0];
}

// Existing functions (keeping them for compatibility)
export async function getSchools() {
  const cacheKey = getCacheKey("getSchools", {});

  // Check cache first
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  return withRequestDeduplication(cacheKey, async () => {
    const { data, error } = await supabase
      .from("tenants")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) throw error;

    // Cache the result
    setCachedData(cacheKey, data);
    return data;
  });
}

export async function createSchool(school: Inserts<"tenants">) {
  const { data, error } = await supabase
    .from("tenants")
    .insert(school)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateSchool(id: string, updates: Updates<"tenants">) {
  const { data, error } = await supabase
    .from("tenants")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deleteSchool(id: string) {
  const { error } = await supabase.from("tenants").delete().eq("id", id);
  if (error) throw error;
}

export async function getBuses(tenantId: string) {
  const cacheKey = getCacheKey("getBuses", { tenantId });

  // Check cache first
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  return withRequestDeduplication(cacheKey, async () => {
    const { data, error } = await supabase
      .from("buses")
      .select(
        `
        *,
        driver:users!driver_id(
          id,
          name,
          email
        )
      `,
      )
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (error) throw error;

    // Cache the result
    setCachedData(cacheKey, data);
    return data;
  });
}

export async function createBus(bus: Inserts<"buses">) {
  const { data, error } = await supabase
    .from("buses")
    .insert(bus)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateBus(id: string, updates: Updates<"buses">) {
  const { data, error } = await supabase
    .from("buses")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deleteBus(id: string) {
  const { error } = await supabase.from("buses").delete().eq("id", id);
  if (error) throw error;
}

export async function getRoutes(tenantId: string) {
  const { data, error } = await supabase
    .from("routes")
    .select(
      `
      *,
      bus:buses!bus_id(
        id,
        plate_number,
        capacity
      ),
      route_stops(
        id,
        name,
        location,
        arrival_time,
        order
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}

export async function createRoute(route: Inserts<"routes">) {
  const { data, error } = await supabase
    .from("routes")
    .insert(route)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateRoute(id: string, updates: Updates<"routes">) {
  const { data, error } = await supabase
    .from("routes")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deleteRoute(id: string) {
  const { error } = await supabase.from("routes").delete().eq("id", id);
  if (error) throw error;
}

export async function getStudents(tenantId: string) {
  const { data, error } = await supabase
    .from("students")
    .select(
      `
      *,
      parent:users!parent_id(
        id,
        name,
        email
      ),
      route_stop:route_stops!route_stop_id(
        id,
        name,
        route:routes!route_id(
          id,
          name
        )
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}

export async function createStudent(student: Inserts<"students">) {
  const { data, error } = await supabase
    .from("students")
    .insert(student)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateStudent(id: string, updates: Updates<"students">) {
  const { data, error } = await supabase
    .from("students")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deleteStudent(id: string) {
  const { error } = await supabase.from("students").delete().eq("id", id);
  if (error) throw error;
}

export async function getUsers(tenantId: string) {
  const cacheKey = getCacheKey("getUsers", { tenantId });

  // Check cache first
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  return withRequestDeduplication(cacheKey, async () => {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (error) throw error;

    // Cache the result
    setCachedData(cacheKey, data);
    return data;
  });
}

export async function createUser(user: Inserts<"users">) {
  const { data, error } = await supabase
    .from("users")
    .insert(user)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updateUser(id: string, updates: Updates<"users">) {
  try {
    console.log(`API: Updating user ${id} with:`, updates);

    // Use the safe update function for better reliability
    const { data, error } = await supabase.rpc("update_user_safely", {
      user_id: id,
      new_tenant_id: updates.tenant_id || null,
      new_role: updates.role || null,
      new_name: updates.name || null,
      new_email: updates.email || null,
      new_phone: updates.phone || null,
      new_is_active: updates.is_active !== undefined ? updates.is_active : null,
    });

    if (error) {
      console.error("API: Update failed:", error);
      throw error;
    }

    if (!data) {
      throw new Error(`No user returned after update for ID ${id}`);
    }

    console.log("API: User updated successfully:", data);
    return data;
  } catch (error) {
    console.error("Error in updateUser:", error);
    throw error;
  }
}

export async function deleteUser(id: string) {
  const { error } = await supabase.from("users").delete().eq("id", id);
  if (error) throw error;
}

export async function getAttendance(
  tenantId: string,
  studentId?: string,
  busId?: string,
  date?: string,
) {
  let query = supabase
    .from("attendance")
    .select(
      `
      *,
      student:students!student_id(
        id,
        name,
        grade
      ),
      bus:buses!bus_id(
        id,
        plate_number
      ),
      recorded_by_user:users!recorded_by(
        id,
        name
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("recorded_at", { ascending: false });

  if (studentId) {
    query = query.eq("student_id", studentId);
  }

  if (busId) {
    query = query.eq("bus_id", busId);
  }

  if (date) {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    query = query
      .gte("recorded_at", startOfDay.toISOString())
      .lte("recorded_at", endOfDay.toISOString());
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

export async function createAttendance(attendance: Inserts<"attendance">) {
  const { data, error } = await supabase
    .from("attendance")
    .insert(attendance)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function getNotifications(
  userId: string,
  tenantId: string,
  unreadOnly = false,
) {
  let query = supabase
    .from("notifications")
    .select("*")
    .eq("user_id", userId)
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (unreadOnly) {
    query = query.eq("read", false);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

export async function markNotificationAsRead(notificationId: string) {
  const { data, error } = await supabase
    .from("notifications")
    .update({ read: true })
    .eq("id", notificationId)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function markAllNotificationsAsRead(
  userId: string,
  tenantId: string,
) {
  const { data, error } = await supabase
    .from("notifications")
    .update({ read: true })
    .eq("user_id", userId)
    .eq("tenant_id", tenantId)
    .eq("read", false)
    .select();

  if (error) throw error;
  return data;
}

export async function deleteNotification(notificationId: string) {
  const { error } = await supabase
    .from("notifications")
    .delete()
    .eq("id", notificationId);

  if (error) throw error;
}

export async function deleteNotifications(notificationIds: string[]) {
  const { error } = await supabase
    .from("notifications")
    .delete()
    .in("id", notificationIds);

  if (error) throw error;
}

// Geofence functions
export async function createGeofenceNotification(
  busId: string,
  stopId: string,
  type: "enter" | "exit",
  tenantId: string,
) {
  try {
    // Get bus and stop information
    const { data: bus } = await supabase
      .from("buses")
      .select("plate_number")
      .eq("id", busId)
      .single();

    const { data: stop } = await supabase
      .from("route_stops")
      .select("name")
      .eq("id", stopId)
      .single();

    const action = type === "enter" ? "arrived at" : "departed from";
    const title = `Bus ${action} stop`;
    const message = `Bus ${bus?.plate_number || busId} has ${action} ${stop?.name || "stop"}`;

    const { data, error } = await supabase
      .from("notifications")
      .insert({
        tenant_id: tenantId,
        title,
        message,
        type: "geofence",
        metadata: {
          type: "geofence",
          busId,
          stopId,
          action: type,
        },
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error creating geofence notification:", error);
    throw error;
  }
}

export function isWithinGeofence(
  currentLocation: { lat: number; lng: number },
  targetLocation: { lat: number; lng: number },
  radiusMeters: number,
): boolean {
  // Calculate distance using Haversine formula
  const R = 6371000; // Earth's radius in meters
  const dLat = ((targetLocation.lat - currentLocation.lat) * Math.PI) / 180;
  const dLng = ((targetLocation.lng - currentLocation.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((currentLocation.lat * Math.PI) / 180) *
      Math.cos((targetLocation.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return distance <= radiusMeters;
}

// Subscribe to real-time bus location updates
export function subscribeToBusLocation(
  busId: string,
  callback: (payload: any) => void,
) {
  const channel = supabase
    .channel(`bus-location-${busId}`)
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "buses",
        filter: `id=eq.${busId}`,
      },
      callback,
    )
    .subscribe();

  return channel;
}

// Driver performance functions
export async function getDriverPerformance(
  driverId: string | null,
  tenantId: string,
  startDate: string,
  endDate: string,
) {
  let query = supabase
    .from("driver_performance")
    .select(
      `
      *,
      driver:users!driver_id(
        id,
        name,
        email,
        avatar_url
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .gte("date", startDate)
    .lte("date", endDate)
    .order("date", { ascending: false });

  if (driverId) {
    query = query.eq("driver_id", driverId);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

// Route delay functions
export async function getRouteDelays(
  tenantId: string,
  routeId?: string,
  startDate?: string,
  endDate?: string,
) {
  let query = supabase
    .from("route_delays")
    .select(
      `
      *,
      route:routes!route_id(
        id,
        name,
        bus:buses!bus_id(
          id,
          plate_number
        )
      ),
      route_stop:route_stops!stop_id(
        id,
        name
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("delay_date", { ascending: false });

  if (routeId) {
    query = query.eq("route_id", routeId);
  }

  if (startDate) {
    query = query.gte("delay_date", startDate);
  }

  if (endDate) {
    query = query.lte("delay_date", endDate);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

export async function trackNotificationAnalytics(analyticsData: {
  type: string;
  notificationId: string;
  notificationData: any;
  timestamp: string;
  metadata?: any;
}) {
  try {
    const { error } = await supabase.from("notification_analytics").insert({
      notification_id: analyticsData.notificationId,
      event_type: analyticsData.type,
      event_data: analyticsData.notificationData,
      timestamp: analyticsData.timestamp,
      metadata: analyticsData.metadata,
    });

    if (error) {
      console.warn("Failed to track notification analytics:", error);
    }
  } catch (error) {
    console.warn("Error tracking notification analytics:", error);
  }
}

// Maintenance functions
export async function createMaintenanceRecord(
  busId: string,
  type: string,
  description: string,
  scheduledDate: string,
  tenantId: string,
  cost: number,
  notes?: string,
  technicianName?: string,
  nextMaintenanceDate?: string,
  spareParts?: { partId: string; quantityUsed: number }[],
) {
  const { data, error } = await supabase
    .from("bus_maintenance")
    .insert({
      bus_id: busId,
      tenant_id: tenantId,
      type,
      description,
      scheduled_date: scheduledDate,
      cost,
      notes,
      technician_name: technicianName,
      next_maintenance_date: nextMaintenanceDate,
      spare_parts: spareParts,
      status: "scheduled",
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function getSpareParts(tenantId: string) {
  const { data, error } = await supabase
    .from("spare_parts")
    .select("*")
    .eq("tenant_id", tenantId)
    .order("name", { ascending: true });

  if (error) throw error;
  return data;
}

export async function getBusUtilization(
  tenantId: string,
  startDate?: string,
  endDate?: string,
) {
  let query = supabase
    .from("attendance")
    .select(
      `
      bus_id,
      recorded_at,
      bus:buses!bus_id(
        id,
        plate_number,
        capacity
      )
    `,
    )
    .eq("tenant_id", tenantId);

  if (startDate) {
    query = query.gte("recorded_at", startDate);
  }

  if (endDate) {
    query = query.lte("recorded_at", endDate);
  }

  const { data, error } = await query;
  if (error) throw error;

  // Calculate utilization metrics
  const utilizationData = data?.reduce((acc: any, record: any) => {
    const busId = record.bus_id;
    if (!acc[busId]) {
      acc[busId] = {
        busId,
        plateNumber: record.bus?.plate_number,
        capacity: record.bus?.capacity,
        totalRides: 0,
        uniqueDays: new Set(),
      };
    }
    acc[busId].totalRides++;
    acc[busId].uniqueDays.add(new Date(record.recorded_at).toDateString());
    return acc;
  }, {});

  // Convert to array and calculate utilization percentage
  return Object.values(utilizationData || {}).map((bus: any) => ({
    ...bus,
    activeDays: bus.uniqueDays.size,
    averageRidesPerDay: bus.totalRides / bus.uniqueDays.size,
    utilizationPercentage: Math.round(
      (bus.totalRides / (bus.capacity * bus.uniqueDays.size)) * 100,
    ),
    uniqueDays: undefined, // Remove Set object from response
  }));
}

// Additional functions needed by DatabaseContext
export async function getAllTenants() {
  // Use the RPC function to bypass RLS for admin users
  const { data, error } = await supabase.rpc("get_all_tenants");

  if (error) throw error;
  return data;
}

export async function getAllUsers() {
  const cacheKey = getCacheKey("getAllUsers", {});

  // Check cache first
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    console.log("API: Using cached users data");
    return cachedData;
  }

  return withRequestDeduplication(cacheKey, async () => {
    console.log("API: Fetching all users via RPC function...");

    try {
      // Use the safe RPC function that bypasses RLS recursion
      const { data, error } = await supabase.rpc("get_all_users");

      console.log("API: RPC query result:", {
        data: data,
        error: error,
        dataLength: data?.length,
      });

      if (error) {
        console.error("API: Error fetching all users via RPC:", error);
        throw error;
      }

      console.log(
        `API: Successfully fetched ${data?.length || 0} users via RPC`,
      );

      // Log each user for debugging (only first 5 to avoid spam)
      if (data && data.length > 0) {
        console.log("API: Sample users details (first 5):");
        data.slice(0, 5).forEach((user, index) => {
          console.log(`User ${index + 1}:`, {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            tenant_id: user.tenant_id,
            is_active: user.is_active,
            created_at: user.created_at,
          });
        });
      } else {
        console.warn("API: No users found via RPC function!");
      }

      const result = data || [];

      // Cache the result
      setCachedData(cacheKey, result);
      return result;
    } catch (err) {
      console.error("API: Exception in getAllUsers:", err);
      throw err;
    }
  });
}

export async function getTenant(tenantId: string) {
  const { data, error } = await supabase
    .from("tenants")
    .select("*")
    .eq("id", tenantId)
    .single();

  if (error) throw error;
  return data;
}

export async function getUsersByTenant(tenantId: string) {
  const { data, error } = await supabase
    .from("users")
    .select("*")
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}

export async function getBusesByTenant(tenantId: string) {
  const { data, error } = await supabase
    .from("buses")
    .select(
      `
      *,
      driver:users!driver_id(
        id,
        name,
        email
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}

export async function getRoutesByTenant(tenantId: string) {
  try {
    console.log(`API: Fetching routes for tenant ${tenantId}...`);

    // Use basic query without complex joins to avoid RLS recursion
    const { data: routesData, error: routesError } = await supabase
      .from("routes")
      .select(
        "id, name, is_active, bus_id, tenant_id, created_at, updated_at, schedule",
      )
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (routesError) {
      console.error("API: Error fetching routes for tenant:", routesError);
      throw routesError;
    }

    if (!routesData) {
      console.log("API: No routes found for tenant");
      return [];
    }

    console.log(
      `API: Found ${routesData.length} routes for tenant ${tenantId}`,
    );

    // Return basic route data without complex joins to avoid recursion
    // The UI can fetch additional details separately if needed
    return routesData.map((route) => ({
      ...route,
      bus: null, // Will be populated by UI if needed
      route_stops: [], // Will be populated by UI if needed
    }));
  } catch (error) {
    console.error("Error in getRoutesByTenant:", error);
    // Return empty array as fallback
    return [];
  }
}

export async function getStudentsByTenant(tenantId: string) {
  const { data, error } = await supabase
    .from("students")
    .select(
      `
      *,
      parent:users!parent_id(
        id,
        name,
        email
      ),
      route_stop:route_stops!route_stop_id(
        id,
        name,
        route:routes!route_id(
          id,
          name
        )
      )
    `,
    )
    .eq("tenant_id", tenantId)
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}

// System-wide functions for super admin access
export async function getAllBuses() {
  // Use the RPC function to bypass RLS for admin users
  const { data, error } = await supabase.rpc("get_all_buses");

  if (error) throw error;

  // Fetch additional data for each bus
  if (data && data.length > 0) {
    const busesWithDetails = await Promise.all(
      data.map(async (bus) => {
        // Get driver details if available
        let driver = null;
        if (bus.driver_id) {
          const { data: driverData } = await supabase
            .from("users")
            .select("id, name, email")
            .eq("id", bus.driver_id)
            .single();
          driver = driverData;
        }

        // Get tenant details
        const { data: tenantData } = await supabase
          .from("tenants")
          .select("id, name")
          .eq("id", bus.tenant_id)
          .single();

        return {
          ...bus,
          driver,
          tenant: tenantData,
        };
      }),
    );

    return busesWithDetails;
  }

  return data;
}

export async function getAllRoutes() {
  try {
    console.log("API: Fetching all routes via RPC function...");

    // Use RPC function to bypass RLS for admin users
    const { data, error } = await supabase.rpc("get_all_routes");

    if (error) {
      console.error("API: RPC get_all_routes failed:", error);
      // Fallback to basic query without joins
      const { data: fallbackData, error: fallbackError } = await supabase
        .from("routes")
        .select(
          "id, name, is_active, bus_id, tenant_id, created_at, updated_at, schedule",
        )
        .order("created_at", { ascending: false });

      if (fallbackError) throw fallbackError;
      return fallbackData || [];
    }

    console.log(
      `API: Successfully fetched ${data?.length || 0} routes via RPC`,
    );
    return data || [];
  } catch (error) {
    console.error("Error in getAllRoutes:", error);
    // Return empty array as final fallback
    return [];
  }
}

export async function getAllStudents() {
  // Use the RPC function to bypass RLS for admin users
  const { data, error } = await supabase.rpc("get_all_students");

  if (error) throw error;

  // Fetch additional data for each student
  if (data && data.length > 0) {
    const studentsWithDetails = await Promise.all(
      data.map(async (student) => {
        // Get parent details if available
        let parent = null;
        if (student.parent_id) {
          const { data: parentData } = await supabase
            .from("users")
            .select("id, name, email")
            .eq("id", student.parent_id)
            .single();
          parent = parentData;
        }

        // Get tenant details
        const { data: tenantData } = await supabase
          .from("tenants")
          .select("id, name")
          .eq("id", student.tenant_id)
          .single();

        // Get route stop details if available
        let route_stop = null;
        if (student.route_stop_id) {
          const { data: stopData } = await supabase
            .from("route_stops")
            .select("id, name, route_id")
            .eq("id", student.route_stop_id)
            .single();

          if (stopData) {
            // Get route details
            const { data: routeData } = await supabase
              .from("routes")
              .select("id, name")
              .eq("id", stopData.route_id)
              .single();

            route_stop = {
              ...stopData,
              route: routeData,
            };
          }
        }

        return {
          ...student,
          parent,
          tenant: tenantData,
          route_stop,
        };
      }),
    );

    return studentsWithDetails;
  }

  return data;
}

// Bus tracking history functions
export async function createBusLocationHistory(
  busId: string,
  location: { lat: number; lng: number },
  tenantId: string,
  metadata?: any,
) {
  const { data, error } = await supabase
    .from("bus_location_history")
    .insert({
      bus_id: busId,
      location: `POINT(${location.lng} ${location.lat})`,
      tenant_id: tenantId,
      timestamp: new Date().toISOString(),
      metadata,
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Update bus location and create history record
export async function updateBusLocation(
  busId: string,
  location: { lat: number; lng: number },
  tenantId: string,
  metadata?: any,
) {
  try {
    // Update current bus location
    const { error: busError } = await supabase
      .from("buses")
      .update({
        last_location: `POINT(${location.lng} ${location.lat})`,
        last_updated: new Date().toISOString(),
      })
      .eq("id", busId);

    if (busError) throw busError;

    // Create history record
    const historyRecord = await createBusLocationHistory(
      busId,
      location,
      tenantId,
      metadata,
    );

    return historyRecord;
  } catch (error) {
    console.error("Error updating bus location:", error);
    throw error;
  }
}

export async function getBusLocationHistory(
  busId: string,
  startDate?: string,
  endDate?: string,
) {
  let query = supabase
    .from("bus_location_history")
    .select(
      `
      *,
      bus:buses!bus_id(
        id,
        plate_number
      )
    `,
    )
    .eq("bus_id", busId)
    .order("timestamp", { ascending: false });

  if (startDate) {
    query = query.gte("timestamp", startDate);
  }

  if (endDate) {
    query = query.lte("timestamp", endDate);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

// Bus delay detection and notifications
export async function detectBusDelays(tenantId: string) {
  try {
    // Get all active routes with their schedules
    const { data: routes } = await supabase
      .from("routes")
      .select(
        `
        *,
        bus:buses!bus_id(
          id,
          plate_number,
          last_location,
          last_updated
        ),
        route_stops(
          id,
          name,
          arrival_time,
          order,
          location
        )
      `,
      )
      .eq("tenant_id", tenantId)
      .eq("is_active", true);

    if (!routes) return [];

    const delays = [];
    const now = new Date();

    for (const route of routes) {
      if (!route.bus || !route.route_stops) continue;

      // Check each stop for potential delays
      for (const stop of route.route_stops) {
        if (!stop.arrival_time) continue;

        const [hours, minutes] = stop.arrival_time.split(":").map(Number);
        const scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0);

        // If current time is more than 15 minutes past scheduled time
        const delayThreshold = 15 * 60 * 1000; // 15 minutes in milliseconds
        const timeDiff = now.getTime() - scheduledTime.getTime();

        if (timeDiff > delayThreshold) {
          delays.push({
            routeId: route.id,
            routeName: route.name,
            busId: route.bus.id,
            busPlateNumber: route.bus.plate_number,
            stopId: stop.id,
            stopName: stop.name,
            scheduledTime: stop.arrival_time,
            delayMinutes: Math.floor(timeDiff / (60 * 1000)),
            tenantId,
          });
        }
      }
    }

    return delays;
  } catch (error) {
    console.error("Error detecting bus delays:", error);
    throw error;
  }
}

export async function sendDelayNotifications(delays: any[]) {
  for (const delay of delays) {
    try {
      // Create notification for delay
      await supabase.from("notifications").insert({
        tenant_id: delay.tenantId,
        title: "Bus Delay Alert",
        message: `Bus ${delay.busPlateNumber} on route ${delay.routeName} is running ${delay.delayMinutes} minutes late at stop ${delay.stopName}`,
        type: "delay",
        priority: "high",
        metadata: {
          type: "bus_delay",
          routeId: delay.routeId,
          busId: delay.busId,
          stopId: delay.stopId,
          delayMinutes: delay.delayMinutes,
        },
      });

      // Log the delay for tracking
      await supabase.from("route_delays").insert({
        route_id: delay.routeId,
        stop_id: delay.stopId,
        delay_minutes: delay.delayMinutes,
        delay_date: new Date().toISOString().split("T")[0],
        tenant_id: delay.tenantId,
        reason: "Automatic detection",
      });
    } catch (error) {
      console.error("Error sending delay notification:", error);
    }
  }
}

// Create template sharing table if needed
export async function createTemplateShare(
  templateId: string,
  sharedWithEmail: string,
  sharedBy: string,
  tenantId: string,
  message?: string,
) {
  const { data, error } = await supabase
    .from("template_shares")
    .insert({
      template_id: templateId,
      shared_with_email: sharedWithEmail,
      shared_by: sharedBy,
      tenant_id: tenantId,
      message,
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Attendance export functions
export async function exportAttendanceToCSV(
  tenantId: string,
  startDate?: string,
  endDate?: string,
  studentId?: string,
) {
  try {
    let query = supabase
      .from("attendance")
      .select(
        `
        *,
        student:students!student_id(
          id,
          name,
          grade
        ),
        bus:buses!bus_id(
          id,
          plate_number
        ),
        recorded_by_user:users!recorded_by(
          id,
          name
        )
      `,
      )
      .eq("tenant_id", tenantId)
      .order("recorded_at", { ascending: false });

    if (studentId) {
      query = query.eq("student_id", studentId);
    }

    if (startDate) {
      query = query.gte("recorded_at", startDate);
    }

    if (endDate) {
      query = query.lte("recorded_at", endDate);
    }

    const { data, error } = await query;
    if (error) throw error;

    // Convert to CSV format
    if (!data || data.length === 0) {
      return "No attendance data found for the specified criteria.";
    }

    const headers = [
      "Date",
      "Time",
      "Student Name",
      "Grade",
      "Bus",
      "Type",
      "Recorded By",
      "Location",
    ];

    const csvRows = [headers.join(",")];

    data.forEach((record) => {
      const date = new Date(record.recorded_at);
      const row = [
        date.toLocaleDateString(),
        date.toLocaleTimeString(),
        record.student?.name || "Unknown",
        record.student?.grade || "Unknown",
        record.bus?.plate_number || "Unknown",
        record.type,
        record.recorded_by_user?.name || "System",
        record.location ? "Available" : "Not recorded",
      ];
      csvRows.push(row.join(","));
    });

    return csvRows.join("\n");
  } catch (error) {
    console.error("Error exporting attendance to CSV:", error);
    throw error;
  }
}

// Geofencing functions
export async function checkGeofenceViolations(
  busId: string,
  currentLocation: { lat: number; lng: number },
  tenantId: string,
) {
  try {
    // Get the bus's assigned route and stops
    const { data: bus } = await supabase
      .from("buses")
      .select(
        `
        *,
        routes!bus_id(
          id,
          name,
          route_stops(
            id,
            name,
            location
          )
        )
      `,
      )
      .eq("id", busId)
      .single();

    if (!bus || !bus.routes) {
      return { violation: false, message: "No route assigned to bus" };
    }

    const route = Array.isArray(bus.routes) ? bus.routes[0] : bus.routes;
    const allowedRadius = 500; // 500 meters radius around stops

    // Check if bus is within allowed radius of any stop
    let withinAllowedArea = false;
    let nearestStop = null;
    let minDistance = Infinity;

    for (const stop of route.route_stops || []) {
      if (!stop.location) continue;

      // Parse PostGIS point format
      const locationMatch = stop.location.toString().match(/POINT\(([^)]+)\)/);
      if (!locationMatch) continue;

      const [lng, lat] = locationMatch[1].split(" ").map(Number);
      const distance = calculateDistance(currentLocation, { lat, lng });

      if (distance < minDistance) {
        minDistance = distance;
        nearestStop = stop;
      }

      if (distance <= allowedRadius) {
        withinAllowedArea = true;
        break;
      }
    }

    if (!withinAllowedArea) {
      // Create geofence violation alert
      await supabase.from("geofence_alerts").insert({
        bus_id: busId,
        tenant_id: tenantId,
        alert_type: "route_deviation",
        location: `POINT(${currentLocation.lng} ${currentLocation.lat})`,
        stop_id: nearestStop?.id,
        timestamp: new Date().toISOString(),
        metadata: {
          distance_from_nearest_stop: Math.round(minDistance),
          nearest_stop_name: nearestStop?.name,
        },
      });

      return {
        violation: true,
        message: `Bus is ${Math.round(minDistance)}m away from nearest authorized stop (${nearestStop?.name})`,
        distance: minDistance,
        nearestStop: nearestStop?.name,
      };
    }

    return { violation: false, message: "Bus is within authorized area" };
  } catch (error) {
    console.error("Error checking geofence violations:", error);
    throw error;
  }
}

function calculateDistance(
  point1: { lat: number; lng: number },
  point2: { lat: number; lng: number },
): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = ((point2.lat - point1.lat) * Math.PI) / 180;
  const dLng = ((point2.lng - point1.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((point1.lat * Math.PI) / 180) *
      Math.cos((point2.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Driver performance functions
export async function updateDriverPerformance(
  driverId: string,
  tenantId: string,
  metrics: {
    onTimeArrivals?: number;
    totalTrips?: number;
    safetyScore?: number;
    parentRating?: number;
    fuelEfficiency?: number;
  },
) {
  const today = new Date().toISOString().split("T")[0];

  const { data, error } = await supabase
    .from("driver_performance")
    .upsert(
      {
        driver_id: driverId,
        tenant_id: tenantId,
        date: today,
        ...metrics,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "driver_id,date",
      },
    )
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function getDriverPerformanceMetrics(
  driverId: string,
  tenantId: string,
  startDate?: string,
  endDate?: string,
) {
  let query = supabase
    .from("driver_performance")
    .select(
      `
      *,
      driver:users!driver_id(
        id,
        name,
        email,
        avatar_url
      )
    `,
    )
    .eq("driver_id", driverId)
    .eq("tenant_id", tenantId)
    .order("date", { ascending: false });

  if (startDate) {
    query = query.gte("date", startDate);
  }

  if (endDate) {
    query = query.lte("date", endDate);
  }

  const { data, error } = await query;
  if (error) throw error;
  return data;
}

// Parent notification preferences
export async function getParentNotificationPreferences(
  userId: string,
  tenantId: string,
) {
  const { data, error } = await supabase
    .from("notification_user_preferences")
    .select("*")
    .eq("user_id", userId)
    .eq("tenant_id", tenantId)
    .single();

  if (error && error.code !== "PGRST116") throw error;
  return data;
}

export async function updateParentNotificationPreferences(
  userId: string,
  tenantId: string,
  preferences: {
    notification_types?: any;
    delivery_channels?: any;
    schedule_settings?: any;
    sound_settings?: any;
    grouping_settings?: any;
  },
) {
  const { data, error } = await supabase
    .from("notification_user_preferences")
    .upsert(
      {
        user_id: userId,
        tenant_id: tenantId,
        ...preferences,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "user_id,tenant_id",
      },
    )
    .select()
    .single();

  if (error) throw error;
  return data;
}
