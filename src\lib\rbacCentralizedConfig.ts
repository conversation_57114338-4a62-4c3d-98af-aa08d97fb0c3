/**
 * Centralized RBAC Configuration
 * Single source of truth for all permission mappings and access control rules
 */

import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "./rbac";

/**
 * Route Permission Mapping
 * Maps application routes to required permissions
 */
export const ROUTE_PERMISSIONS: Record<
  string,
  {
    permissions: Permission[];
    requireAll?: boolean;
    fallbackRoute?: string;
    description: string;
  }
> = {
  "/dashboard": {
    permissions: [], // Available to all authenticated users
    description: "Main dashboard - accessible to all authenticated users",
  },
  "/dashboard/schools": {
    permissions: [Permission.VIEW_ALL_SCHOOLS, Permission.SCHOOLS_VIEW_ALL],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "School management - admin only",
  },
  "/dashboard/users": {
    permissions: [
      Permission.VIEW_ALL_USERS,
      Permission.VIEW_TENANT_USERS,
      Permission.USERS_VIEW_ALL,
      Permission.USERS_VIEW_TENANT,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "User management - admin and school managers",
  },
  "/dashboard/buses": {
    permissions: [
      Permission.VIEW_ALL_BUSES,
      Permission.VIEW_TENANT_BUSES,
      Permission.VIEW_ASSIGNED_BUSES,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Bus management - varies by role",
  },
  "/dashboard/routes": {
    permissions: [
      Permission.VIEW_ALL_ROUTES,
      Permission.VIEW_TENANT_ROUTES,
      Permission.VIEW_ASSIGNED_ROUTE,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Route management - varies by role",
  },
  "/dashboard/students": {
    permissions: [
      Permission.VIEW_ALL_STUDENTS,
      Permission.VIEW_TENANT_STUDENTS,
      Permission.VIEW_OWN_CHILDREN,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Student management - varies by role",
  },
  "/dashboard/children": {
    permissions: [Permission.VIEW_OWN_CHILDREN],
    requireAll: true,
    fallbackRoute: "/dashboard",
    description: "Parent view of their children",
  },
  "/dashboard/attendance": {
    permissions: [Permission.MANAGE_ATTENDANCE, Permission.VIEW_ATTENDANCE],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Attendance management",
  },
  "/dashboard/driver-attendance": {
    permissions: [Permission.MANAGE_ATTENDANCE],
    requireAll: true,
    fallbackRoute: "/dashboard",
    description: "Driver attendance interface",
  },
  "/dashboard/my-route": {
    permissions: [Permission.VIEW_ASSIGNED_ROUTE],
    requireAll: true,
    fallbackRoute: "/dashboard",
    description: "Driver route view",
  },
  "/dashboard/tracking": {
    permissions: [Permission.TRACK_BUS],
    requireAll: true,
    fallbackRoute: "/dashboard",
    description: "Bus tracking",
  },
  "/dashboard/reports": {
    permissions: [
      Permission.VIEW_SYSTEM_REPORTS,
      Permission.VIEW_TENANT_REPORTS,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Reports and analytics",
  },
  "/dashboard/notifications": {
    permissions: [
      Permission.VIEW_ALL_NOTIFICATIONS,
      Permission.SEND_NOTIFICATIONS,
    ],
    requireAll: false,
    fallbackRoute: "/dashboard",
    description: "Notification management",
  },
  "/dashboard/evaluation": {
    permissions: [], // Available to most roles
    description: "Evaluation system",
  },
  "/dashboard/profile": {
    permissions: [], // Available to all authenticated users
    description: "User profile management",
  },
  "/dashboard/settings": {
    permissions: [], // Available to all authenticated users
    description: "User settings",
  },
};

/**
 * Component Permission Mapping
 * Maps UI components to required permissions
 */
export const COMPONENT_PERMISSIONS: Record<
  string,
  {
    permissions: Permission[];
    roles?: UserRole[];
    dataScopes?: DataScope[];
    description: string;
  }
> = {
  "SchoolModal.create": {
    permissions: [Permission.SCHOOLS_CREATE],
    roles: [UserRole.ADMIN],
    description: "Create new school",
  },
  "SchoolModal.edit": {
    permissions: [Permission.SCHOOLS_UPDATE],
    roles: [UserRole.ADMIN],
    description: "Edit school details",
  },
  "UserModal.create": {
    permissions: [Permission.USERS_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new user",
  },
  "UserModal.edit": {
    permissions: [
      Permission.USERS_UPDATE_ALL,
      Permission.USERS_UPDATE_TENANT,
      Permission.USERS_UPDATE_OWN,
    ],
    description: "Edit user details",
  },
  "BusModal.create": {
    permissions: [Permission.BUSES_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new bus",
  },
  "StudentModal.create": {
    permissions: [Permission.STUDENTS_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new student",
  },
  AttendanceForm: {
    permissions: [Permission.MANAGE_ATTENDANCE],
    roles: [
      UserRole.DRIVER,
      UserRole.SUPERVISOR,
      UserRole.SCHOOL_MANAGER,
      UserRole.ADMIN,
    ],
    description: "Record attendance",
  },
  ReportExport: {
    permissions: [Permission.EXPORT_REPORTS],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Export reports",
  },
  NotificationTemplates: {
    permissions: [Permission.NOTIFICATIONS_MANAGE_TEMPLATES],
    roles: [UserRole.ADMIN],
    description: "Manage notification templates",
  },
};

/**
 * Data Access Patterns
 * Defines how different roles access data
 */
export const DATA_ACCESS_PATTERNS: Record<
  UserRole,
  {
    defaultScope: DataScope;
    allowedScopes: DataScope[];
    restrictions: {
      tenantBound: boolean;
      personalOnly: boolean;
      assignedOnly: boolean;
      childrenOnly: boolean;
    };
  }
> = {
  [UserRole.ADMIN]: {
    defaultScope: DataScope.GLOBAL,
    allowedScopes: [
      DataScope.GLOBAL,
      DataScope.TENANT,
      DataScope.PERSONAL,
      DataScope.ASSIGNED,
      DataScope.CHILDREN,
    ],
    restrictions: {
      tenantBound: false,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
    },
  },
  [UserRole.SCHOOL_MANAGER]: {
    defaultScope: DataScope.TENANT,
    allowedScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
    },
  },
  [UserRole.SUPERVISOR]: {
    defaultScope: DataScope.TENANT,
    allowedScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
    },
  },
  [UserRole.DRIVER]: {
    defaultScope: DataScope.ASSIGNED,
    allowedScopes: [DataScope.ASSIGNED, DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: true,
      childrenOnly: false,
    },
  },
  [UserRole.PARENT]: {
    defaultScope: DataScope.CHILDREN,
    allowedScopes: [DataScope.CHILDREN, DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: true,
    },
  },
  [UserRole.STUDENT]: {
    defaultScope: DataScope.PERSONAL,
    allowedScopes: [DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: true,
      assignedOnly: false,
      childrenOnly: false,
    },
  },
};

/**
 * Action Permission Matrix
 * Maps actions to required permissions for each resource type
 */
export const ACTION_PERMISSION_MATRIX: Record<
  ResourceType,
  Record<Action, Permission[]>
> = {
  [ResourceType.SCHOOL]: {
    [Action.READ]: [
      Permission.VIEW_ALL_SCHOOLS,
      Permission.SCHOOLS_VIEW_ALL,
      Permission.SCHOOLS_VIEW_OWN,
    ],
    [Action.CREATE]: [Permission.SCHOOLS_CREATE],
    [Action.UPDATE]: [Permission.SCHOOLS_UPDATE],
    [Action.DELETE]: [Permission.SCHOOLS_DELETE],
    [Action.MANAGE]: [
      Permission.MANAGE_SCHOOLS,
      Permission.SCHOOLS_MANAGE_SETTINGS,
    ],
  },
  [ResourceType.USER]: {
    [Action.READ]: [
      Permission.VIEW_ALL_USERS,
      Permission.VIEW_TENANT_USERS,
      Permission.VIEW_OWN_PROFILE,
    ],
    [Action.CREATE]: [Permission.USERS_CREATE],
    [Action.UPDATE]: [
      Permission.USERS_UPDATE_ALL,
      Permission.USERS_UPDATE_TENANT,
      Permission.USERS_UPDATE_OWN,
    ],
    [Action.DELETE]: [
      Permission.USERS_DELETE_ALL,
      Permission.USERS_DELETE_TENANT,
    ],
    [Action.MANAGE]: [
      Permission.MANAGE_ALL_USERS,
      Permission.MANAGE_TENANT_USERS,
    ],
  },
  [ResourceType.BUS]: {
    [Action.READ]: [
      Permission.VIEW_ALL_BUSES,
      Permission.VIEW_TENANT_BUSES,
      Permission.VIEW_ASSIGNED_BUSES,
    ],
    [Action.CREATE]: [Permission.BUSES_CREATE],
    [Action.UPDATE]: [Permission.BUSES_UPDATE],
    [Action.DELETE]: [Permission.BUSES_DELETE],
    [Action.MANAGE]: [Permission.MANAGE_BUSES],
    [Action.TRACK]: [Permission.TRACK_BUS],
  },
  [ResourceType.STUDENT]: {
    [Action.READ]: [
      Permission.VIEW_ALL_STUDENTS,
      Permission.VIEW_TENANT_STUDENTS,
      Permission.VIEW_OWN_CHILDREN,
    ],
    [Action.CREATE]: [Permission.STUDENTS_CREATE],
    [Action.UPDATE]: [Permission.STUDENTS_UPDATE],
    [Action.DELETE]: [Permission.STUDENTS_DELETE],
    [Action.MANAGE]: [Permission.MANAGE_STUDENTS],
  },
  [ResourceType.ROUTE]: {
    [Action.READ]: [
      Permission.VIEW_ALL_ROUTES,
      Permission.VIEW_TENANT_ROUTES,
      Permission.VIEW_ASSIGNED_ROUTE,
    ],
    [Action.CREATE]: [Permission.ROUTES_CREATE],
    [Action.UPDATE]: [Permission.ROUTES_UPDATE],
    [Action.DELETE]: [Permission.ROUTES_DELETE],
    [Action.MANAGE]: [Permission.MANAGE_ROUTES],
  },
  [ResourceType.ATTENDANCE]: {
    [Action.READ]: [Permission.VIEW_ATTENDANCE],
    [Action.CREATE]: [Permission.MANAGE_ATTENDANCE],
    [Action.UPDATE]: [Permission.MANAGE_ATTENDANCE],
    [Action.MANAGE]: [Permission.MANAGE_ATTENDANCE],
  },
  [ResourceType.REPORT]: {
    [Action.READ]: [
      Permission.VIEW_SYSTEM_REPORTS,
      Permission.VIEW_TENANT_REPORTS,
    ],
    [Action.EXPORT]: [Permission.EXPORT_REPORTS],
    [Action.SCHEDULE]: [Permission.SCHEDULE_REPORTS],
  },
  [ResourceType.NOTIFICATION]: {
    [Action.READ]: [Permission.VIEW_ALL_NOTIFICATIONS],
    [Action.CREATE]: [Permission.SEND_NOTIFICATIONS],
    [Action.MANAGE]: [Permission.MANAGE_NOTIFICATION_SETTINGS],
  },
};

/**
 * Navigation Menu Configuration
 * Defines navigation items and their permission requirements
 */
export const NAVIGATION_CONFIG = {
  dashboard: {
    permissions: [],
    roles: "all",
    icon: "LayoutDashboard",
    route: "/dashboard",
  },
  schools: {
    permissions: [Permission.VIEW_ALL_SCHOOLS],
    roles: [UserRole.ADMIN],
    icon: "School",
    route: "/dashboard/schools",
  },
  users: {
    permissions: [Permission.VIEW_ALL_USERS, Permission.VIEW_TENANT_USERS],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    icon: "Users",
    route: "/dashboard/users",
  },
  buses: {
    permissions: [
      Permission.VIEW_ALL_BUSES,
      Permission.VIEW_TENANT_BUSES,
      Permission.VIEW_ASSIGNED_BUSES,
    ],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
    ],
    icon: "Bus",
    route: "/dashboard/buses",
  },
  routes: {
    permissions: [
      Permission.VIEW_ALL_ROUTES,
      Permission.VIEW_TENANT_ROUTES,
      Permission.VIEW_ASSIGNED_ROUTE,
    ],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
    ],
    icon: "Route",
    route: "/dashboard/routes",
  },
  students: {
    permissions: [
      Permission.VIEW_ALL_STUDENTS,
      Permission.VIEW_TENANT_STUDENTS,
      Permission.VIEW_OWN_CHILDREN,
    ],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.PARENT,
    ],
    icon: "Users",
    route: "/dashboard/students",
  },
  attendance: {
    permissions: [Permission.MANAGE_ATTENDANCE, Permission.VIEW_ATTENDANCE],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
    ],
    icon: "ClipboardCheck",
    route: "/dashboard/attendance",
  },
  tracking: {
    permissions: [Permission.TRACK_BUS],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.DRIVER,
      UserRole.PARENT,
    ],
    icon: "MapPin",
    route: "/dashboard/tracking",
  },
  reports: {
    permissions: [
      Permission.VIEW_SYSTEM_REPORTS,
      Permission.VIEW_TENANT_REPORTS,
    ],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    icon: "FileBarChart",
    route: "/dashboard/reports",
  },
  notifications: {
    permissions: [
      Permission.VIEW_ALL_NOTIFICATIONS,
      Permission.SEND_NOTIFICATIONS,
    ],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER, UserRole.SUPERVISOR],
    icon: "Bell",
    route: "/dashboard/notifications",
  },
  evaluation: {
    permissions: [],
    roles: [
      UserRole.ADMIN,
      UserRole.SCHOOL_MANAGER,
      UserRole.SUPERVISOR,
      UserRole.PARENT,
    ],
    icon: "Star",
    route: "/dashboard/evaluation",
  },
  profile: {
    permissions: [],
    roles: "all",
    icon: "User",
    route: "/dashboard/profile",
  },
  settings: {
    permissions: [],
    roles: "all",
    icon: "Settings",
    route: "/dashboard/settings",
  },
};

/**
 * Error Messages for Permission Denials
 */
export const PERMISSION_ERROR_MESSAGES: Record<string, string> = {
  ROUTE_ACCESS_DENIED: "You do not have permission to access this page.",
  COMPONENT_ACCESS_DENIED: "You do not have permission to perform this action.",
  DATA_ACCESS_DENIED: "You do not have permission to view this data.",
  FEATURE_DISABLED: "This feature is not available for your role.",
  TENANT_MISMATCH: "You can only access data from your organization.",
  INSUFFICIENT_PERMISSIONS:
    "Your current role does not have sufficient permissions.",
  SESSION_EXPIRED: "Your session has expired. Please log in again.",
  RATE_LIMIT_EXCEEDED: "Too many requests. Please try again later.",
};

export default {
  ROUTE_PERMISSIONS,
  COMPONENT_PERMISSIONS,
  DATA_ACCESS_PATTERNS,
  ACTION_PERMISSION_MATRIX,
  NAVIGATION_CONFIG,
  PERMISSION_ERROR_MESSAGES,
};
