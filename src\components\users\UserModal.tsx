import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import { Button } from "../ui/Button";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";
import type { UserRole } from "../../types";

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Tables<"users">>) => Promise<void>;
  user?: Tables<"users">;
  bulkMode?: "role" | "status" | null;
}

export const UserModal: React.FC<UserModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  user,
  bulkMode = null,
}) => {
  const { t } = useTranslation();
  const { tenant } = useDatabase();
  const { user: currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<
    Partial<Tables<"users">> & { password?: string }
  >({
    name: "",
    email: "",
    role: "school_manager",
    phone: "",
    password: "",
    is_active: true,
    tenant_id: tenant?.id || currentUser?.tenant_id,
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone || "",
        password: "", // Don't populate password for editing
        is_active: user.is_active,
        tenant_id: user.tenant_id,
      });
    } else if (bulkMode) {
      // في التعديل الجماعي، ابدأ بقيم فارغة
      setFormData({
        role: "school_manager",
        is_active: true,
      });
    } else {
      setFormData({
        name: "",
        email: "",
        role: "school_manager",
        phone: "",
        password: "",
        is_active: true,
        tenant_id: tenant?.id || currentUser?.tenant_id,
      });
    }
  }, [user, tenant, bulkMode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (bulkMode === "role") {
        if (!formData.role) {
          alert(t("users.roleRequired") || "Role is required");
          return;
        }
      } else if (bulkMode === "status") {
        if (typeof formData.is_active !== "boolean") {
          alert(t("users.statusRequired") || "Status is required");
          return;
        }
      } else {
        // Validate required fields (فردي)
        if (!formData.name || formData.name.trim() === "") {
          alert(t("users.nameRequired") || "Name is required");
          return;
        }
        if (!formData.email || formData.email.trim() === "") {
          alert(t("users.emailRequired") || "Email is required");
          return;
        }
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email.trim())) {
          alert("Please enter a valid email address");
          return;
        }
        // Validate password for new users
        if (!user && (!formData.password || formData.password.trim() === "")) {
          alert(t("users.passwordRequired") || "Password is required");
          return;
        }
        // Validate password length
        if (!user && formData.password && formData.password.length < 6) {
          alert("Password must be at least 6 characters long");
          return;
        }
      }

      await onSubmit(formData);
      onClose();
    } catch (error) {
      // تحسين تسجيل الأخطاء
      console.error("Error submitting user:", error);
      if (error instanceof Error) {
        alert(t("common.error") + ": " + error.message);
      } else if (typeof error === "object") {
        alert(t("common.error") + ": " + JSON.stringify(error));
      } else {
        alert(t("common.error") + ": " + String(error));
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {bulkMode === "role"
              ? t("users.bulkRole")
              : bulkMode === "status"
              ? t("users.bulkStatus")
              : user
              ? t("users.editUser")
              : t("users.addUser")}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            {/* تعديل جماعي: تغيير الدور */}
            {bulkMode === "role" && (
              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("users.role")}
                </label>
                <select
                  id="role"
                  value={formData.role}
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as UserRole })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
                >
                  <option value="school_manager">{t("users.schoolManager")}</option>
                  <option value="supervisor">{t("users.supervisor")}</option>
                  <option value="driver">{t("users.driver")}</option>
                  <option value="parent">{t("users.parent")}</option>
                  <option value="student">{t("users.student")}</option>
                </select>
              </div>
            )}
            {/* تعديل جماعي: تفعيل/إيقاف */}
            {bulkMode === "status" && (
              <div>
                <label htmlFor="is_active" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("users.status")}
                </label>
                <select
                  id="is_active"
                  value={formData.is_active ? "active" : "inactive"}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.value === "active" })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
                >
                  <option value="active">{t("users.active")}</option>
                  <option value="inactive">{t("users.inactive")}</option>
                </select>
              </div>
            )}
            {/* الوضع الفردي (إضافة/تعديل مستخدم) */}
            {!bulkMode && (
              <>
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                {t("users.name")}
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("users.email")}
              </label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              />
            </div>

            <div>
              <label
                htmlFor="role"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("users.role")}
              </label>
              <select
                id="role"
                value={formData.role}
                onChange={(e) =>
                  setFormData({ ...formData, role: e.target.value as UserRole })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              >
                <option value="school_manager">
                  {t("users.schoolManager")}
                </option>
                <option value="supervisor">{t("users.supervisor")}</option>
                <option value="driver">{t("users.driver")}</option>
                <option value="parent">{t("users.parent")}</option>
                <option value="student">{t("users.student")}</option>
              </select>
            </div>

            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("users.phone")}
              </label>
              <input
                type="tel"
                id="phone"
                value={formData.phone ?? ""}
                onChange={(e) =>
                  setFormData({ ...formData, phone: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {user ? t("users.newPassword") : t("users.password")}{" "}
                {!user && "*"}
              </label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={(e) =>
                  setFormData({ ...formData, password: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required={!user}
                placeholder={user ? t("users.leaveEmptyToKeepCurrent") : ""}
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) =>
                  setFormData({ ...formData, is_active: e.target.checked })
                }
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_active"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                {t("users.active")}
              </label>
            </div>
            </> /* نهاية الوضع الفردي */
            )}

          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t("common.saving") : t("common.save")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
