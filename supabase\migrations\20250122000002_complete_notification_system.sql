-- Create scheduled_notifications table
CREATE TABLE IF NOT EXISTS scheduled_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('geofence', 'attendance', 'maintenance', 'announcements', 'emergency', 'route_changes')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high')),
  target_roles TEXT[] DEFAULT '{}',
  scheduled_for TIMESTAMPTZ NOT NULL,
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_pattern JSONB,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'cancelled', 'failed')),
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create geofence_alerts table
CREATE TABLE IF NOT EXISTS geofence_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
  stop_id UUID NOT NULL REFERENCES route_stops(id) ON DELETE CASCADE,
  alert_type TEXT NOT NULL CHECK (alert_type IN ('enter', 'exit')),
  location GEOMETRY(POINT, 4326),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create out_of_route_alerts table
CREATE TABLE IF NOT EXISTS out_of_route_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
  location GEOMETRY(POINT, 4326),
  severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high')),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'dismissed')),
  resolved_by UUID REFERENCES users(id),
  resolved_at TIMESTAMPTZ,
  notes TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notification_delivery_log table
CREATE TABLE IF NOT EXISTS notification_delivery_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  delivery_method TEXT NOT NULL CHECK (delivery_method IN ('push', 'email', 'sms', 'in_app')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
  attempt_count INTEGER DEFAULT 1,
  last_attempt_at TIMESTAMPTZ DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notification_user_preferences table
CREATE TABLE IF NOT EXISTS notification_user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  notification_types JSONB DEFAULT '{}',
  delivery_channels JSONB DEFAULT '{}',
  sound_settings JSONB DEFAULT '{}',
  schedule_settings JSONB DEFAULT '{}',
  grouping_settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);

-- Create notification_rate_limits table
CREATE TABLE IF NOT EXISTS notification_rate_limits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL,
  time_window INTERVAL NOT NULL,
  max_notifications INTEGER NOT NULL,
  current_count INTEGER DEFAULT 0,
  window_start TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_tenant_status ON scheduled_notifications(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_for ON scheduled_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_geofence_alerts_bus_timestamp ON geofence_alerts(bus_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_geofence_alerts_tenant_timestamp ON geofence_alerts(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_out_of_route_alerts_bus_status ON out_of_route_alerts(bus_id, status);
CREATE INDEX IF NOT EXISTS idx_out_of_route_alerts_tenant_timestamp ON out_of_route_alerts(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification ON notification_delivery_log(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_user_status ON notification_delivery_log(user_id, status);
CREATE INDEX IF NOT EXISTS idx_notification_user_preferences_user ON notification_user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_rate_limits_tenant_type ON notification_rate_limits(tenant_id, notification_type);

-- Add spatial index for location-based queries
CREATE INDEX IF NOT EXISTS idx_geofence_alerts_location ON geofence_alerts USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_out_of_route_alerts_location ON out_of_route_alerts USING GIST(location);

-- Enable realtime for new tables
ALTER PUBLICATION supabase_realtime ADD TABLE scheduled_notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE geofence_alerts;
ALTER PUBLICATION supabase_realtime ADD TABLE out_of_route_alerts;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_delivery_log;

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_scheduled_notifications_updated_at
    BEFORE UPDATE ON scheduled_notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_user_preferences_updated_at
    BEFORE UPDATE ON notification_user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_rate_limits_updated_at
    BEFORE UPDATE ON notification_rate_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to process scheduled notifications
CREATE OR REPLACE FUNCTION process_scheduled_notifications()
RETURNS void AS $$
DECLARE
    notification_record RECORD;
    target_users UUID[];
    user_id UUID;
BEGIN
    -- Get all pending notifications that should be sent now
    FOR notification_record IN
        SELECT * FROM scheduled_notifications
        WHERE status = 'pending'
        AND scheduled_for <= NOW()
    LOOP
        BEGIN
            -- Get target users based on roles
            IF array_length(notification_record.target_roles, 1) > 0 THEN
                SELECT array_agg(id) INTO target_users
                FROM users
                WHERE tenant_id = notification_record.tenant_id
                AND role = ANY(notification_record.target_roles)
                AND is_active = true;
            ELSE
                -- If no specific roles, send to all active users
                SELECT array_agg(id) INTO target_users
                FROM users
                WHERE tenant_id = notification_record.tenant_id
                AND is_active = true;
            END IF;

            -- Create individual notifications for each user
            IF target_users IS NOT NULL THEN
                FOREACH user_id IN ARRAY target_users
                LOOP
                    INSERT INTO notifications (
                        user_id,
                        tenant_id,
                        title,
                        message,
                        type,
                        priority,
                        metadata
                    ) VALUES (
                        user_id,
                        notification_record.tenant_id,
                        notification_record.title,
                        notification_record.message,
                        notification_record.type,
                        notification_record.priority,
                        notification_record.metadata || jsonb_build_object('scheduled_notification_id', notification_record.id)
                    );
                END LOOP;
            END IF;

            -- Update scheduled notification status
            UPDATE scheduled_notifications
            SET status = 'sent',
                sent_at = NOW()
            WHERE id = notification_record.id;

            -- Handle recurring notifications
            IF notification_record.is_recurring AND notification_record.recurring_pattern IS NOT NULL THEN
                DECLARE
                    next_scheduled_time TIMESTAMPTZ;
                    pattern JSONB := notification_record.recurring_pattern;
BEGIN
                    -- Calculate next scheduled time based on pattern
                    CASE pattern->>'frequency'
                        WHEN 'daily' THEN
                            next_scheduled_time := notification_record.scheduled_for + INTERVAL '1 day';
                        WHEN 'weekly' THEN
                            next_scheduled_time := notification_record.scheduled_for + INTERVAL '1 week';
                        WHEN 'monthly' THEN
                            next_scheduled_time := notification_record.scheduled_for + INTERVAL '1 month';
                        ELSE
                            next_scheduled_time := NULL;
                    END CASE;

                    -- Create next occurrence if calculated
                    IF next_scheduled_time IS NOT NULL THEN
                        INSERT INTO scheduled_notifications (
                            tenant_id,
                            created_by,
                            title,
                            message,
                            type,
                            priority,
                            target_roles,
                            scheduled_for,
                            is_recurring,
                            recurring_pattern,
                            metadata
                        ) VALUES (
                            notification_record.tenant_id,
                            notification_record.created_by,
                            notification_record.title,
                            notification_record.message,
                            notification_record.type,
                            notification_record.priority,
                            notification_record.target_roles,
                            next_scheduled_time,
                            notification_record.is_recurring,
                            notification_record.recurring_pattern,
                            notification_record.metadata
                        );
                    END IF;
                END;
            END IF;

        EXCEPTION
            WHEN OTHERS THEN
                -- Update notification with error status
                UPDATE scheduled_notifications
                SET status = 'failed',
                    error_message = SQLERRM
                WHERE id = notification_record.id;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- Delete notifications older than 90 days
    DELETE FROM notifications
    WHERE created_at < NOW() - INTERVAL '90 days';

    -- Delete old geofence alerts (older than 30 days)
    DELETE FROM geofence_alerts
    WHERE created_at < NOW() - INTERVAL '30 days';

    -- Delete old out of route alerts that are resolved (older than 30 days)
    DELETE FROM out_of_route_alerts
    WHERE status = 'resolved'
    AND resolved_at < NOW() - INTERVAL '30 days';

    -- Delete old delivery logs (older than 30 days)
    DELETE FROM notification_delivery_log
    WHERE created_at < NOW() - INTERVAL '30 days';

    -- Delete old sent scheduled notifications (older than 30 days)
    DELETE FROM scheduled_notifications
    WHERE status = 'sent'
    AND sent_at < NOW() - INTERVAL '30 days'
    AND is_recurring = false;
END;
$$ LANGUAGE plpgsql;
