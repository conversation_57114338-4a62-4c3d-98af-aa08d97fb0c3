import { UserRole } from "../types";

// Enhanced RBAC Permissions - Comprehensive and Granular
export enum Permission {
  // System Administration - Global Level
  SYSTEM_ADMIN = "system:admin",
  SYSTEM_SETTINGS = "system:settings",
  SYSTEM_AUDIT = "system:audit",
  SYSTEM_BACKUP = "system:backup",
  SYSTEM_ANALYTICS = "system:analytics",
  SYSTEM_BILLING = "system:billing",

  // Legacy permissions for backward compatibility
  VIEW_ALL_SCHOOLS = "schools:view_all",
  MANAGE_SCHOOLS = "schools:manage",
  VIEW_SCHOOL_SETTINGS = "schools:view_settings",
  MANAGE_SCHOOL_SETTINGS = "schools:manage_settings",

  VIEW_ALL_USERS = "users:view_all",
  VIEW_TENANT_USERS = "users:view_tenant",
  VIEW_OWN_PROFILE = "users:view_own",
  MANAGE_ALL_USERS = "users:manage_all",
  <PERSON>NAGE_TENANT_USERS = "users:manage_tenant",
  MANAGE_OWN_PROFILE = "users:manage_own",
  ASSIGN_USER_ROLES = "users:assign_roles",

  VIEW_ALL_BUSES = "buses:view_all",
  VIEW_TENANT_BUSES = "buses:view_tenant",
  VIEW_ASSIGNED_BUSES = "buses:view_assigned",
  MANAGE_BUSES = "buses:manage",
  ASSIGN_BUS_DRIVERS = "buses:assign_drivers",
  TRACK_BUS = "buses:track",
  MANAGE_BUS_MAINTENANCE = "buses:manage_maintenance",

  VIEW_ALL_STUDENTS = "students:view_all",
  VIEW_TENANT_STUDENTS = "students:view_tenant",
  VIEW_OWN_CHILDREN = "students:view_children",
  MANAGE_STUDENTS = "students:manage",
  MANAGE_ATTENDANCE = "students:manage_attendance",
  VIEW_ATTENDANCE = "students:view_attendance",

  VIEW_ALL_ROUTES = "routes:view_all",
  VIEW_TENANT_ROUTES = "routes:view_tenant",
  VIEW_ASSIGNED_ROUTE = "routes:view_assigned",
  MANAGE_ROUTES = "routes:manage",
  OPTIMIZE_ROUTES = "routes:optimize",

  VIEW_SYSTEM_REPORTS = "reports:view_system",
  VIEW_TENANT_REPORTS = "reports:view_tenant",
  EXPORT_REPORTS = "reports:export",
  SCHEDULE_REPORTS = "reports:schedule",

  VIEW_ALL_NOTIFICATIONS = "notifications:view_all",
  SEND_NOTIFICATIONS = "notifications:send",
  MANAGE_NOTIFICATION_SETTINGS = "notifications:manage_settings",

  VIEW_MAINTENANCE = "maintenance:view",
  MANAGE_MAINTENANCE = "maintenance:manage",
  SCHEDULE_MAINTENANCE = "maintenance:schedule",

  VIEW_EVALUATIONS = "evaluations:view",
  CREATE_EVALUATION = "evaluations:create",
  RESPOND_TO_EVALUATION = "evaluations:respond",
  ANALYZE_EVALUATIONS = "evaluations:analyze",

  // School Management - Tenant Level
  SCHOOLS_VIEW_ALL = "schools:view_all",
  SCHOOLS_VIEW_OWN = "schools:view_own",
  SCHOOLS_CREATE = "schools:create",
  SCHOOLS_UPDATE = "schools:update",
  SCHOOLS_DELETE = "schools:delete",
  SCHOOLS_MANAGE_SETTINGS = "schools:manage_settings",
  SCHOOLS_MANAGE_BRANDING = "schools:manage_branding",

  // User Management - Multi-level
  USERS_VIEW_ALL = "users:view_all",
  USERS_VIEW_TENANT = "users:view_tenant",
  USERS_VIEW_OWN = "users:view_own",
  USERS_CREATE = "users:create",
  USERS_UPDATE_ALL = "users:update_all",
  USERS_UPDATE_TENANT = "users:update_tenant",
  USERS_UPDATE_OWN = "users:update_own",
  USERS_DELETE_ALL = "users:delete_all",
  USERS_DELETE_TENANT = "users:delete_tenant",
  USERS_ASSIGN_ROLES = "users:assign_roles",
  USERS_MANAGE_PERMISSIONS = "users:manage_permissions",

  // Bus Management
  BUSES_VIEW_ALL = "buses:view_all",
  BUSES_VIEW_TENANT = "buses:view_tenant",
  BUSES_VIEW_ASSIGNED = "buses:view_assigned",
  BUSES_CREATE = "buses:create",
  BUSES_UPDATE = "buses:update",
  BUSES_DELETE = "buses:delete",
  BUSES_ASSIGN_DRIVERS = "buses:assign_drivers",
  BUSES_TRACK = "buses:track",
  BUSES_MANAGE_MAINTENANCE = "buses:manage_maintenance",

  // Student Management
  STUDENTS_VIEW_ALL = "students:view_all",
  STUDENTS_VIEW_TENANT = "students:view_tenant",
  STUDENTS_VIEW_CHILDREN = "students:view_children",
  STUDENTS_VIEW_OWN = "students:view_own",
  STUDENTS_CREATE = "students:create",
  STUDENTS_UPDATE = "students:update",
  STUDENTS_DELETE = "students:delete",
  STUDENTS_MANAGE_ATTENDANCE = "students:manage_attendance",
  STUDENTS_VIEW_ATTENDANCE = "students:view_attendance",
  STUDENTS_ASSIGN_ROUTES = "students:assign_routes",

  // Route Management
  ROUTES_VIEW_ALL = "routes:view_all",
  ROUTES_VIEW_TENANT = "routes:view_tenant",
  ROUTES_VIEW_ASSIGNED = "routes:view_assigned",
  ROUTES_CREATE = "routes:create",
  ROUTES_UPDATE = "routes:update",
  ROUTES_DELETE = "routes:delete",
  ROUTES_OPTIMIZE = "routes:optimize",
  ROUTES_ASSIGN_BUSES = "routes:assign_buses",

  // Reports and Analytics
  REPORTS_VIEW_SYSTEM = "reports:view_system",
  REPORTS_VIEW_TENANT = "reports:view_tenant",
  REPORTS_VIEW_PERSONAL = "reports:view_personal",
  REPORTS_CREATE = "reports:create",
  REPORTS_EXPORT = "reports:export",
  REPORTS_SCHEDULE = "reports:schedule",
  REPORTS_ANALYTICS = "reports:analytics",

  // Notification Management
  NOTIFICATIONS_VIEW_ALL = "notifications:view_all",
  NOTIFICATIONS_VIEW_TENANT = "notifications:view_tenant",
  NOTIFICATIONS_VIEW_OWN = "notifications:view_own",
  NOTIFICATIONS_SEND_SYSTEM = "notifications:send_system",
  NOTIFICATIONS_SEND_TENANT = "notifications:send_tenant",
  NOTIFICATIONS_MANAGE_TEMPLATES = "notifications:manage_templates",
  NOTIFICATIONS_MANAGE_SETTINGS = "notifications:manage_settings",

  // Maintenance Management
  MAINTENANCE_VIEW_ALL = "maintenance:view_all",
  MAINTENANCE_VIEW_TENANT = "maintenance:view_tenant",
  MAINTENANCE_VIEW_ASSIGNED = "maintenance:view_assigned",
  MAINTENANCE_CREATE = "maintenance:create",
  MAINTENANCE_UPDATE = "maintenance:update",
  MAINTENANCE_DELETE = "maintenance:delete",
  MAINTENANCE_SCHEDULE = "maintenance:schedule",
  MAINTENANCE_APPROVE = "maintenance:approve",

  // Evaluation and Feedback
  EVALUATION_VIEW_ALL = "evaluation:view_all",
  EVALUATION_VIEW_TENANT = "evaluation:view_tenant",
  EVALUATION_VIEW_OWN = "evaluation:view_own",
  EVALUATION_CREATE = "evaluation:create",
  EVALUATION_RESPOND = "evaluation:respond",
  EVALUATION_ANALYZE = "evaluation:analyze",
  EVALUATION_MODERATE = "evaluation:moderate",

  // Financial Management
  BILLING_VIEW_ALL = "billing:view_all",
  BILLING_VIEW_TENANT = "billing:view_tenant",
  BILLING_MANAGE = "billing:manage",
  BILLING_PROCESS_PAYMENTS = "billing:process_payments",

  // Emergency and Safety
  EMERGENCY_TRIGGER = "emergency:trigger",
  EMERGENCY_RESPOND = "emergency:respond",
  EMERGENCY_MANAGE = "emergency:manage",
  SAFETY_MONITOR = "safety:monitor",
  SAFETY_ALERTS = "safety:alerts",
}

// Enhanced Data Scopes for Multi-Tenant Architecture
export enum DataScope {
  GLOBAL = "global", // System-wide data access (Admin only)
  TENANT = "tenant", // School-specific data access
  PERSONAL = "personal", // User's own data only
  ASSIGNED = "assigned", // Data assigned to user (drivers, routes)
  CHILDREN = "children", // Parent's children data
  DEPARTMENT = "department", // Department-level access within school
  ROUTE_BASED = "route_based", // Route-specific data access
  BUS_BASED = "bus_based", // Bus-specific data access
  CLASS_BASED = "class_based", // Class/Grade-specific data access
}

// Enhanced Resource Types for Comprehensive System Coverage
export enum ResourceType {
  SYSTEM = "system",
  SCHOOL = "school",
  USER = "user",
  BUS = "bus",
  ROUTE = "route",
  STUDENT = "student",
  ATTENDANCE = "attendance",
  NOTIFICATION = "notification",
  REPORT = "report",
  MAINTENANCE = "maintenance",
  EVALUATION = "evaluation",
  BILLING = "billing",
  EMERGENCY = "emergency",
  SAFETY = "safety",
  ANALYTICS = "analytics",
  AUDIT = "audit",
  INTEGRATION = "integration",
  GEOFENCE = "geofence",
  SCHEDULE = "schedule",
  COMMUNICATION = "communication",
}

// Enhanced Actions for Granular Permission Control
export enum Action {
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  MANAGE = "manage",
  ASSIGN = "assign",
  UNASSIGN = "unassign",
  TRACK = "track",
  EXPORT = "export",
  IMPORT = "import",
  SCHEDULE = "schedule",
  ANALYZE = "analyze",
  APPROVE = "approve",
  REJECT = "reject",
  SUSPEND = "suspend",
  ACTIVATE = "activate",
  MONITOR = "monitor",
  ALERT = "alert",
  BACKUP = "backup",
  RESTORE = "restore",
  AUDIT = "audit",
  CONFIGURE = "configure",
}

// تعريف السياسات المحدثة والمبسطة لكل دور
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // صلاحيات النظام العامة - الأدمن لديه كافة الصلاحيات
    Permission.SYSTEM_ADMIN,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_AUDIT,
    Permission.SYSTEM_BACKUP,
    Permission.SYSTEM_ANALYTICS,
    Permission.SYSTEM_BILLING,

    // صلاحيات المدارس الكاملة
    Permission.VIEW_ALL_SCHOOLS,
    Permission.MANAGE_SCHOOLS,
    Permission.VIEW_SCHOOL_SETTINGS,
    Permission.MANAGE_SCHOOL_SETTINGS,

    // صلاحيات المستخدمين الكاملة
    Permission.VIEW_ALL_USERS,
    Permission.VIEW_TENANT_USERS,
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_ALL_USERS,
    Permission.MANAGE_TENANT_USERS,
    Permission.MANAGE_OWN_PROFILE,
    Permission.ASSIGN_USER_ROLES,

    // صلاحيات الحافلات الكاملة
    Permission.VIEW_ALL_BUSES,
    Permission.VIEW_TENANT_BUSES,
    Permission.VIEW_ASSIGNED_BUSES,
    Permission.MANAGE_BUSES,
    Permission.ASSIGN_BUS_DRIVERS,
    Permission.TRACK_BUS,
    Permission.MANAGE_BUS_MAINTENANCE,

    // صلاحيات الطلاب الكاملة
    Permission.VIEW_ALL_STUDENTS,
    Permission.VIEW_TENANT_STUDENTS,
    Permission.VIEW_OWN_CHILDREN,
    Permission.MANAGE_STUDENTS,
    Permission.MANAGE_ATTENDANCE,
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات الكاملة
    Permission.VIEW_ALL_ROUTES,
    Permission.VIEW_TENANT_ROUTES,
    Permission.VIEW_ASSIGNED_ROUTE,
    Permission.MANAGE_ROUTES,
    Permission.OPTIMIZE_ROUTES,

    // صلاحيات التقارير الكاملة
    Permission.VIEW_SYSTEM_REPORTS,
    Permission.VIEW_TENANT_REPORTS,
    Permission.EXPORT_REPORTS,
    Permission.SCHEDULE_REPORTS,

    // صلاحيات الإشعارات الكاملة
    Permission.VIEW_ALL_NOTIFICATIONS,
    Permission.SEND_NOTIFICATIONS,
    Permission.MANAGE_NOTIFICATION_SETTINGS,

    // صلاحيات الصيانة الكاملة
    Permission.VIEW_MAINTENANCE,
    Permission.MANAGE_MAINTENANCE,
    Permission.SCHEDULE_MAINTENANCE,

    // صلاحيات التقييم الكاملة
    Permission.VIEW_EVALUATIONS,
    Permission.CREATE_EVALUATION,
    Permission.RESPOND_TO_EVALUATION,
    Permission.ANALYZE_EVALUATIONS,
  ],

  [UserRole.SCHOOL_MANAGER]: [
    // صلاحيات إدارة المدرسة
    Permission.VIEW_SCHOOL_SETTINGS,
    Permission.MANAGE_SCHOOL_SETTINGS,

    // صلاحيات المستخدمين على مستوى المدرسة - محدثة
    Permission.VIEW_TENANT_USERS,
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_TENANT_USERS,
    Permission.MANAGE_OWN_PROFILE,
    Permission.ASSIGN_USER_ROLES,

    // صلاحيات الحافلات على مستوى المدرسة - محدثة
    Permission.VIEW_TENANT_BUSES,
    Permission.VIEW_ASSIGNED_BUSES,
    Permission.MANAGE_BUSES,
    Permission.ASSIGN_BUS_DRIVERS,
    Permission.TRACK_BUS,
    Permission.MANAGE_BUS_MAINTENANCE,

    // صلاحيات الطلاب على مستوى المدرسة - محدثة
    Permission.VIEW_TENANT_STUDENTS,
    Permission.VIEW_OWN_CHILDREN,
    Permission.MANAGE_STUDENTS,
    Permission.MANAGE_ATTENDANCE,
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات على مستوى المدرسة - محدثة
    Permission.VIEW_TENANT_ROUTES,
    Permission.VIEW_ASSIGNED_ROUTE,
    Permission.MANAGE_ROUTES,
    Permission.OPTIMIZE_ROUTES,

    // صلاحيات التقارير على مستوى المدرسة
    Permission.VIEW_TENANT_REPORTS,
    Permission.EXPORT_REPORTS,
    Permission.SCHEDULE_REPORTS,

    // صلاحيات الإشعارات على مستوى المدرسة
    Permission.VIEW_ALL_NOTIFICATIONS,
    Permission.SEND_NOTIFICATIONS,
    Permission.MANAGE_NOTIFICATION_SETTINGS,

    // صلاحيات الصيانة
    Permission.VIEW_MAINTENANCE,
    Permission.MANAGE_MAINTENANCE,
    Permission.SCHEDULE_MAINTENANCE,

    // صلاحيات التقييم
    Permission.VIEW_EVALUATIONS,
    Permission.CREATE_EVALUATION,
    Permission.RESPOND_TO_EVALUATION,
    Permission.ANALYZE_EVALUATIONS,
  ],

  [UserRole.SUPERVISOR]: [
    // صلاحيات المستخدمين المحدودة
    Permission.VIEW_TENANT_USERS,
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_OWN_PROFILE,

    // صلاحيات الحافلات المحدودة
    Permission.VIEW_TENANT_BUSES,
    Permission.TRACK_BUS,

    // صلاحيات الطلاب
    Permission.VIEW_TENANT_STUDENTS,
    Permission.MANAGE_ATTENDANCE,
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات
    Permission.VIEW_TENANT_ROUTES,

    // صلاحيات التقارير المحدودة
    Permission.VIEW_TENANT_REPORTS,

    // صلاحيات الإشعارات المحدودة
    Permission.SEND_NOTIFICATIONS,

    // صلاحيات الصيانة المحدودة
    Permission.VIEW_MAINTENANCE,

    // صلاحيات التقييم
    Permission.VIEW_EVALUATIONS,
    Permission.CREATE_EVALUATION,
    Permission.RESPOND_TO_EVALUATION,
  ],

  [UserRole.DRIVER]: [
    // صلاحيات المستخدمين الشخصية
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_OWN_PROFILE,

    // صلاحيات الحافلات المخصصة
    Permission.VIEW_ASSIGNED_BUSES,
    Permission.TRACK_BUS,

    // صلاحيات الطلاب المحدودة
    Permission.MANAGE_ATTENDANCE,
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات المخصصة
    Permission.VIEW_ASSIGNED_ROUTE,

    // صلاحيات الصيانة المحدودة
    Permission.VIEW_MAINTENANCE,
    Permission.MANAGE_MAINTENANCE,

    // صلاحيات التقييم
    Permission.RESPOND_TO_EVALUATION,
  ],

  [UserRole.PARENT]: [
    // صلاحيات المستخدمين الشخصية
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_OWN_PROFILE,

    // صلاحيات الحافلات المحدودة
    Permission.TRACK_BUS,

    // صلاحيات الطلاب (الأطفال)
    Permission.VIEW_OWN_CHILDREN,
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات المحدودة
    Permission.VIEW_ASSIGNED_ROUTE,

    // صلاحيات التقييم
    Permission.CREATE_EVALUATION,
    Permission.RESPOND_TO_EVALUATION,
  ],

  [UserRole.STUDENT]: [
    // صلاحيات المستخدمين الشخصية
    Permission.VIEW_OWN_PROFILE,
    Permission.MANAGE_OWN_PROFILE,

    // صلاحيات الحافلات المحدودة
    Permission.TRACK_BUS,

    // صلاحيات الحضور الشخصية
    Permission.VIEW_ATTENDANCE,

    // صلاحيات المسارات المحدودة
    Permission.VIEW_ASSIGNED_ROUTE,
  ],
};

// تعريف نطاق البيانات لكل دور
export const ROLE_DATA_SCOPE: Record<UserRole, DataScope[]> = {
  [UserRole.ADMIN]: [
    DataScope.GLOBAL,
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
    DataScope.CHILDREN,
  ],
  [UserRole.SCHOOL_MANAGER]: [
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
    DataScope.CHILDREN,
  ],
  [UserRole.SUPERVISOR]: [
    DataScope.TENANT,
    DataScope.PERSONAL,
    DataScope.ASSIGNED,
  ],
  [UserRole.DRIVER]: [DataScope.ASSIGNED, DataScope.PERSONAL],
  [UserRole.PARENT]: [DataScope.PERSONAL, DataScope.CHILDREN],
  [UserRole.STUDENT]: [DataScope.PERSONAL],
};

// التسلسل الهرمي للأدوار
export const ROLE_HIERARCHY: Record<UserRole, UserRole[]> = {
  [UserRole.ADMIN]: [
    UserRole.SCHOOL_MANAGER,
    UserRole.SUPERVISOR,
    UserRole.DRIVER,
    UserRole.PARENT,
    UserRole.STUDENT,
  ],
  [UserRole.SCHOOL_MANAGER]: [
    UserRole.SUPERVISOR,
    UserRole.DRIVER,
    UserRole.PARENT,
    UserRole.STUDENT,
  ],
  [UserRole.SUPERVISOR]: [UserRole.DRIVER, UserRole.PARENT, UserRole.STUDENT],
  [UserRole.DRIVER]: [],
  [UserRole.PARENT]: [],
  [UserRole.STUDENT]: [],
};

// نموذج السياسة المحدث
export interface Policy {
  id: string;
  role: UserRole;
  resource: ResourceType;
  action: Action;
  scope: DataScope;
  conditions?: PolicyCondition[];
  effect: "allow" | "deny";
  priority: number;
}

// شروط السياسة
export interface PolicyCondition {
  field: string;
  operator:
    | "equals"
    | "not_equals"
    | "in"
    | "not_in"
    | "contains"
    | "starts_with"
    | "ends_with";
  value: any;
}

// سياق التحقق من الصلاحيات
export interface PermissionContext {
  userId?: string;
  tenantId?: string;
  resourceOwnerId?: string;
  resourceTenantId?: string;
  resourceData?: any;
  timestamp?: Date;
  ipAddress?: string;
  userAgent?: string;
}

// فئة إدارة الصلاحيات المحدثة
export class RBACManager {
  /**
   * فحص ما إذا كان المستخدم لديه صلاحية معينة
   */
  static hasPermission(userRole: UserRole, permission: Permission): boolean {
    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
    return rolePermissions.includes(permission);
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لنطاق بيانات معين
   */
  static hasDataScope(userRole: UserRole, scope: DataScope): boolean {
    const roleScopes = ROLE_DATA_SCOPE[userRole] || [];
    return roleScopes.includes(scope);
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه تنفيذ عملية على مورد معين
   */
  static canPerformAction(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
    context?: PermissionContext,
  ): boolean {
    // فحص الصلاحيات الأساسية أولاً
    const hasBasicPermission = this.checkBasicPermission(
      userRole,
      resource,
      action,
    );
    if (!hasBasicPermission) {
      return false;
    }

    // فحص السياق والشروط الإضافية
    return this.checkContextualPermission(userRole, resource, action, context);
  }

  /**
   * فحص الصلاحيات الأساسية
   */
  private static checkBasicPermission(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
  ): boolean {
    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];

    // تحويل المورد والعملية إلى صلاحية
    const permission = this.getPermissionFromResourceAction(
      resource,
      action,
      userRole,
    );

    return permission ? rolePermissions.includes(permission) : false;
  }

  /**
   * فحص الصلاحيات السياقية المحدث
   */
  private static checkContextualPermission(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
    context?: PermissionContext,
  ): boolean {
    if (!context) return true;

    switch (userRole) {
      case UserRole.ADMIN:
        // الأدمن له صلاحية كاملة لجميع الموارد والعمليات بدون قيود
        return true;

      case UserRole.SCHOOL_MANAGER:
        // مدير المدرسة يمكنه الوصول لبيانات مدرسته فقط
        if (!this.checkTenantAccess(context)) {
          return false;
        }
        // إضافة فحص إضافي للبيانات المخصصة
        if (allowedScopes.includes(DataScope.ASSIGNED)) {
          return this.checkPersonalOrAssignedAccess(context, resource);
        }
        return true;

      case UserRole.SUPERVISOR:
        // المشرف يمكنه الوصول لبيانات مدرسته فقط
        return this.checkTenantAccess(context);

      case UserRole.DRIVER:
        // السائق يمكنه الوصول لبياناته الشخصية أو المخصصة له فقط
        return this.checkPersonalOrAssignedAccess(context, resource);

      case UserRole.PARENT:
        // ولي الأمر يمكنه الوصول لبياناته الشخصية أو بيانات أطفاله
        return this.checkParentAccess(context, resource);

      case UserRole.STUDENT:
        // الطالب يمكنه الوصول لبياناته الشخصية فقط
        return this.checkPersonalAccess(context);

      default:
        return false;
    }
  }

  /**
   * فحص الوصول على مستوى المدرسة
   */
  private static checkTenantAccess(context: PermissionContext): boolean {
    if (context.resourceTenantId && context.tenantId) {
      return context.resourceTenantId === context.tenantId;
    }
    return true;
  }

  /**
   * فحص الوصول الشخصي
   */
  private static checkPersonalAccess(context: PermissionContext): boolean {
    if (context.resourceOwnerId && context.userId) {
      return context.resourceOwnerId === context.userId;
    }
    return true;
  }

  /**
   * فحص الوصول الشخصي أو المخصص
   */
  private static checkPersonalOrAssignedAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    // فحص الوصول الشخصي
    if (this.checkPersonalAccess(context)) {
      return true;
    }

    // فحص الوصول للبيانات المخصصة
    return this.checkAssignedAccess(context, resource);
  }

  /**
   * فحص الوصول للبيانات المخصصة
   */
  private static checkAssignedAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    if (!context.resourceData || !context.userId) {
      return false;
    }

    switch (resource) {
      case ResourceType.BUS:
        return context.resourceData.driver_id === context.userId;
      case ResourceType.ROUTE:
        return context.resourceData.bus?.driver_id === context.userId;
      case ResourceType.STUDENT:
        return (
          context.resourceData.route_stop?.route?.bus?.driver_id ===
          context.userId
        );
      default:
        return false;
    }
  }

  /**
   * فحص وصول ولي الأمر
   */
  private static checkParentAccess(
    context: PermissionContext,
    resource: ResourceType,
  ): boolean {
    // فحص الوصول الشخصي
    if (this.checkPersonalAccess(context)) {
      return true;
    }

    // فحص الوصول لبيانات الأطفال
    if (resource === ResourceType.STUDENT && context.resourceData) {
      return context.resourceData.parent_id === context.userId;
    }

    return false;
  }

  /**
   * تحويل المورد والعملية إلى صلاحية محدث
   */
  private static getPermissionFromResourceAction(
    resource: ResourceType,
    action: Action,
    userRole: UserRole,
  ): Permission | null {
    const mapping: Record<string, Permission> = {
      // صلاحيات المدارس
      [`${ResourceType.SCHOOL}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.SCHOOLS_VIEW_ALL,
      [`${ResourceType.SCHOOL}_${Action.CREATE}`]: Permission.SCHOOLS_CREATE,
      [`${ResourceType.SCHOOL}_${Action.UPDATE}`]: Permission.SCHOOLS_UPDATE,
      [`${ResourceType.SCHOOL}_${Action.DELETE}`]: Permission.SCHOOLS_DELETE,
      [`${ResourceType.SCHOOL}_${Action.MANAGE}`]:
        Permission.SCHOOLS_MANAGE_SETTINGS,

      // صلاحيات المستخدمين
      [`${ResourceType.USER}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.USERS_VIEW_ALL,
      [`${ResourceType.USER}_${Action.READ}_${UserRole.SCHOOL_MANAGER}`]:
        Permission.VIEW_TENANT_USERS,
      [`${ResourceType.USER}_${Action.READ}_${UserRole.SUPERVISOR}`]:
        Permission.VIEW_TENANT_USERS,
      [`${ResourceType.USER}_${Action.READ}`]: Permission.USERS_VIEW_OWN,
      [`${ResourceType.USER}_${Action.CREATE}`]: Permission.USERS_CREATE,
      [`${ResourceType.USER}_${Action.UPDATE}_${UserRole.ADMIN}`]:
        Permission.USERS_UPDATE_ALL,
      [`${ResourceType.USER}_${Action.UPDATE}_${UserRole.SCHOOL_MANAGER}`]:
        Permission.MANAGE_TENANT_USERS,
      [`${ResourceType.USER}_${Action.UPDATE}`]: Permission.USERS_UPDATE_OWN,
      [`${ResourceType.USER}_${Action.DELETE}_${UserRole.ADMIN}`]:
        Permission.USERS_DELETE_ALL,
      [`${ResourceType.USER}_${Action.DELETE}`]: Permission.USERS_DELETE_TENANT,

      // صلاحيات الحافلات
      [`${ResourceType.BUS}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.BUSES_VIEW_ALL,
      [`${ResourceType.BUS}_${Action.READ}_${UserRole.SCHOOL_MANAGER}`]:
        Permission.VIEW_TENANT_BUSES,
      [`${ResourceType.BUS}_${Action.READ}_${UserRole.SUPERVISOR}`]:
        Permission.VIEW_TENANT_BUSES,
      [`${ResourceType.BUS}_${Action.READ}_${UserRole.DRIVER}`]:
        Permission.VIEW_ASSIGNED_BUSES,
      [`${ResourceType.BUS}_${Action.CREATE}`]: Permission.BUSES_CREATE,
      [`${ResourceType.BUS}_${Action.UPDATE}`]: Permission.MANAGE_BUSES,
      [`${ResourceType.BUS}_${Action.DELETE}`]: Permission.MANAGE_BUSES,
      [`${ResourceType.BUS}_${Action.TRACK}`]: Permission.TRACK_BUS,

      // صلاحيات الطلاب
      [`${ResourceType.STUDENT}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.STUDENTS_VIEW_ALL,
      [`${ResourceType.STUDENT}_${Action.READ}_${UserRole.SCHOOL_MANAGER}`]:
        Permission.VIEW_TENANT_STUDENTS,
      [`${ResourceType.STUDENT}_${Action.READ}_${UserRole.SUPERVISOR}`]:
        Permission.VIEW_TENANT_STUDENTS,
      [`${ResourceType.STUDENT}_${Action.READ}_${UserRole.PARENT}`]:
        Permission.VIEW_OWN_CHILDREN,
      [`${ResourceType.STUDENT}_${Action.CREATE}`]: Permission.MANAGE_STUDENTS,
      [`${ResourceType.STUDENT}_${Action.UPDATE}`]: Permission.MANAGE_STUDENTS,
      [`${ResourceType.STUDENT}_${Action.DELETE}`]: Permission.MANAGE_STUDENTS,

      // صلاحيات المسارات
      [`${ResourceType.ROUTE}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.ROUTES_VIEW_ALL,
      [`${ResourceType.ROUTE}_${Action.READ}_${UserRole.SCHOOL_MANAGER}`]:
        Permission.VIEW_TENANT_ROUTES,
      [`${ResourceType.ROUTE}_${Action.READ}_${UserRole.SUPERVISOR}`]:
        Permission.VIEW_TENANT_ROUTES,
      [`${ResourceType.ROUTE}_${Action.READ}_${UserRole.DRIVER}`]:
        Permission.VIEW_ASSIGNED_ROUTE,
      [`${ResourceType.ROUTE}_${Action.CREATE}`]: Permission.MANAGE_ROUTES,
      [`${ResourceType.ROUTE}_${Action.UPDATE}`]: Permission.MANAGE_ROUTES,
      [`${ResourceType.ROUTE}_${Action.DELETE}`]: Permission.MANAGE_ROUTES,

      // صلاحيات التقارير
      [`${ResourceType.REPORT}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.VIEW_SYSTEM_REPORTS,
      [`${ResourceType.REPORT}_${Action.READ}`]: Permission.VIEW_TENANT_REPORTS,
      [`${ResourceType.REPORT}_${Action.EXPORT}`]: Permission.EXPORT_REPORTS,

      // صلاحيات الإشعارات
      [`${ResourceType.NOTIFICATION}_${Action.READ}_${UserRole.ADMIN}`]:
        Permission.VIEW_ALL_NOTIFICATIONS,
      [`${ResourceType.NOTIFICATION}_${Action.CREATE}_${UserRole.ADMIN}`]:
        Permission.SEND_NOTIFICATIONS,
      [`${ResourceType.NOTIFICATION}_${Action.CREATE}`]:
        Permission.SEND_NOTIFICATIONS,
    };

    const key = `${resource}_${action}_${userRole}`;
    const fallbackKey = `${resource}_${action}`;

    return mapping[key] || mapping[fallbackKey] || null;
  }

  /**
   * الحصول على جميع الصلاحيات لدور معين
   */
  static getRolePermissions(userRole: UserRole): Permission[] {
    return ROLE_PERMISSIONS[userRole] || [];
  }

  /**
   * الحصول على نطاقات البيانات لدور معين
   */
  static getRoleDataScopes(userRole: UserRole): DataScope[] {
    return ROLE_DATA_SCOPE[userRole] || [];
  }

  /**
   * فحص ما إذا كان الدور يمكنه إدارة دور آخر مع التحقق من الأمان
   */
  static canManageRole(
    managerRole: UserRole,
    targetRole: UserRole,
    context?: PermissionContext,
  ): boolean {
    // منع أي شخص غير الأدمن من إنشاء مستخدمين أدمن
    if (targetRole === UserRole.ADMIN && managerRole !== UserRole.ADMIN) {
      console.warn(
        `Security Alert: ${managerRole} attempted to create admin user`,
      );
      return false;
    }

    const managedRoles = ROLE_HIERARCHY[managerRole] || [];
    const canManage = managedRoles.includes(targetRole);

    // التحقق الإضافي من نطاق المدرسة لمديري المدارس
    if (managerRole === UserRole.SCHOOL_MANAGER && context && canManage) {
      return this.checkTenantAccess(context);
    }

    return canManage;
  }

  /**
   * إنشاء سياق التحقق من الصلاحيات
   */
  static createPermissionContext(
    userId?: string,
    tenantId?: string,
    resourceOwnerId?: string,
    resourceTenantId?: string,
    resourceData?: any,
  ): PermissionContext {
    return {
      userId,
      tenantId,
      resourceOwnerId,
      resourceTenantId,
      resourceData,
      timestamp: new Date(),
    };
  }

  /**
   * تسجيل عملية التحقق من الصلاحيات للمراجعة مع كشف الأنشطة المشبوهة
   */
  static logPermissionCheck(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
    context: PermissionContext,
    result: boolean,
  ): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      userRole,
      resource,
      action,
      context: {
        userId: context.userId,
        tenantId: context.tenantId,
        resourceOwnerId: context.resourceOwnerId,
        resourceTenantId: context.resourceTenantId,
      },
      result,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      severity: result ? "INFO" : "WARNING",
    };

    // تسجيل العملية
    if (result) {
      console.log("✅ Permission Granted:", logEntry);
    } else {
      console.warn("❌ Permission Denied:", logEntry);

      // كشف الأنشطة المشبوهة
      this.detectSuspiciousActivity(logEntry);
    }

    // في التطبيق الحقيقي، يجب حفظ هذا السجل في قاعدة البيانات
    // await this.saveAuditLog(logEntry);
  }

  /**
   * كشف الأنشطة المشبوهة
   */
  private static detectSuspiciousActivity(logEntry: any): void {
    const suspiciousPatterns = [
      // محاولة الوصول لموارد النظام من قبل غير الأدمن
      logEntry.resource === ResourceType.SYSTEM &&
        logEntry.userRole !== UserRole.ADMIN,
      // محاولة إدارة المستخدمين من قبل السائقين أو الطلاب
      logEntry.resource === ResourceType.USER &&
        [UserRole.DRIVER, UserRole.STUDENT, UserRole.PARENT].includes(
          logEntry.userRole,
        ),
      // محاولة الوصول لبيانات مدرسة أخرى
      logEntry.context.tenantId &&
        logEntry.context.resourceTenantId &&
        logEntry.context.tenantId !== logEntry.context.resourceTenantId &&
        logEntry.userRole !== UserRole.ADMIN,
    ];

    if (suspiciousPatterns.some((pattern) => pattern)) {
      console.error("🚨 SECURITY ALERT - Suspicious Activity Detected:", {
        userId: logEntry.context.userId,
        userRole: logEntry.userRole,
        attemptedResource: logEntry.resource,
        attemptedAction: logEntry.action,
        timestamp: logEntry.timestamp,
        ipAddress: logEntry.ipAddress,
      });

      // في التطبيق الحقيقي، يجب إرسال تنبيه فوري للأمان
      // await this.sendSecurityAlert(logEntry);
    }
  }

  /**
   * فحص شامل للصلاحيات مع التسجيل والتحقق الأمني المحسن
   */
  static checkPermissionWithLogging(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
    context?: PermissionContext,
  ): boolean {
    const startTime = performance.now();

    // التحقق من الصلاحيات الأساسية
    const result = this.canPerformAction(userRole, resource, action, context);

    // التحقق من القيود الزمنية
    const timeBasedResult = this.checkTimeBasedAccess(userRole, action);

    // التحقق من حدود المعدل
    const rateLimitResult = context
      ? this.checkRateLimit(context.userId, action)
      : true;

    const finalResult = result && timeBasedResult && rateLimitResult;

    if (context) {
      const enhancedContext = {
        ...context,
        checkDuration: performance.now() - startTime,
        timeBasedCheck: timeBasedResult,
        rateLimitCheck: rateLimitResult,
      };

      this.logPermissionCheck(
        userRole,
        resource,
        action,
        enhancedContext,
        finalResult,
      );
    }

    return finalResult;
  }

  /**
   * التحقق من القيود الزمنية
   */
  private static checkTimeBasedAccess(
    userRole: UserRole,
    action: Action,
  ): boolean {
    // مثال: السائقون يمكنهم تسجيل الحضور فقط خلال ساعات العمل
    if (userRole === UserRole.DRIVER && action === Action.CREATE) {
      const now = new Date();
      const hour = now.getHours();
      const day = now.getDay();

      // من الاثنين إلى الجمعة، من 6 صباحاً إلى 6 مساءً
      if (day >= 1 && day <= 5 && hour >= 6 && hour <= 18) {
        return true;
      }

      console.warn(
        `Time-based access denied for ${userRole} at ${now.toISOString()}`,
      );
      return false;
    }

    return true;
  }

  /**
   * التحقق من حدود المعدل
   */
  private static rateLimitStore = new Map<
    string,
    { count: number; resetTime: number }
  >();

  private static checkRateLimit(userId?: string, action?: Action): boolean {
    if (!userId || !action) return true;

    const key = `${userId}:${action}`;
    const now = Date.now();
    const windowMs = 60 * 1000; // نافذة دقيقة واحدة
    const maxRequests = this.getRateLimitForAction(action);

    const current = this.rateLimitStore.get(key);

    if (!current || now > current.resetTime) {
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (current.count >= maxRequests) {
      console.warn(
        `Rate limit exceeded for user ${userId} on action ${action}`,
      );
      return false;
    }

    current.count++;
    return true;
  }

  /**
   * الحصول على حد المعدل لكل عملية
   */
  private static getRateLimitForAction(action: Action): number {
    const limits: Record<Action, number> = {
      [Action.CREATE]: 10,
      [Action.UPDATE]: 20,
      [Action.DELETE]: 5,
      [Action.READ]: 100,
      [Action.EXPORT]: 3,
      [Action.MANAGE]: 15,
      [Action.ASSIGN]: 10,
      [Action.TRACK]: 50,
      [Action.SCHEDULE]: 5,
      [Action.ANALYZE]: 10,
      [Action.APPROVE]: 10,
      [Action.REJECT]: 10,
      [Action.SUSPEND]: 3,
      [Action.ACTIVATE]: 10,
      [Action.MONITOR]: 30,
      [Action.ALERT]: 20,
      [Action.BACKUP]: 1,
      [Action.RESTORE]: 1,
      [Action.AUDIT]: 10,
      [Action.CONFIGURE]: 5,
      [Action.UNASSIGN]: 10,
      [Action.IMPORT]: 3,
    };

    return limits[action] || 10;
  }
}

export default RBACManager;
