import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  School,
  Bus,
  MapPin,
  Users,
  FileBarChart,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Bell,
  Star,
  ClipboardCheck,
  Route,
} from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { useTheme } from "../../contexts/ThemeContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { ResourceType } from "../../lib/rbac";
import { UserRole } from "../../types";
import { Button } from "../ui/Button";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { Permission, DataScope } from "../../lib/rbac";
import { PermissionGuard, RoleGuard } from "../auth/PermissionGuard";

interface SidebarProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isOpen = false,
  onToggle,
}) => {
  const [isMobileOpen, setIsMobileOpen] = useState(isOpen);

  // Sync with external control if provided
  useEffect(() => {
    if (onToggle) {
      setIsMobileOpen(isOpen);
    }
  }, [isOpen, onToggle]);

  const handleToggle = () => {
    const newState = !isMobileOpen;
    setIsMobileOpen(newState);
    if (onToggle) onToggle();
  };
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const { user, tenant, logout } = useAuth();
  const { direction } = useTheme();
  const { users, tenants, buses, students, routes } = useDatabase();
  const { canAccessRoute, filterDataByRole } = useRBACEnhancedSecurity();

  // Update direction when language changes
  useEffect(() => {
    if (i18n.language === "ar") {
      document.documentElement.setAttribute("dir", "rtl");
    } else {
      document.documentElement.setAttribute("dir", "ltr");
    }
  }, [i18n.language]);

  // Use centralized navigation configuration with enhanced RBAC
  const getNavItems = () => {
    // Get RBAC-filtered data for badges
    const filteredBuses = filterDataByRole(buses, ResourceType.BUS, "driver_id");
    const filteredStudents = filterDataByRole(students, ResourceType.STUDENT, "parent_id");
    const filteredRoutes = filterDataByRole(routes, ResourceType.ROUTE);

    const isAdmin = user?.role === UserRole.ADMIN;

    const navigationItems = [
      {
        key: "dashboard",
        to: "/dashboard",
        icon: <LayoutDashboard size={20} />,
        label: t("nav.dashboard"),
        route: "/dashboard",
      },
      {
        key: "schools",
        to: "/dashboard/schools",
        icon: <School size={20} />,
        label: t("nav.schools"),
        route: "/dashboard/schools",
        badge: isAdmin && tenants ? tenants.length : undefined,
      },
      {
        key: "users",
        to: "/dashboard/users",
        icon: <Users size={20} />,
        label: isAdmin ? t("nav.allUsers") : t("nav.users"),
        route: "/dashboard/users",
        badge: filterDataByRole(users, ResourceType.USER).length,
      },
      {
        key: "buses",
        to: "/dashboard/buses",
        icon: <Bus size={20} />,
        label: isAdmin ? t("nav.allBuses") : t("nav.buses"),
        route: "/dashboard/buses",
        badge: filteredBuses.filter((b) => b.is_active).length,
      },
      {
        key: "routes",
        to:
          user?.role === "driver" ? "/dashboard/my-route" : "/dashboard/routes",
        icon: <Route size={20} />,
        label:
          user?.role === "driver"
            ? t("routes.manageRoutes")
            : isAdmin
              ? t("nav.allRoutes")
              : t("nav.routes"),
        route:
          user?.role === "driver" ? "/dashboard/my-route" : "/dashboard/routes",
        badge:
          user?.role !== "driver"
            ? filteredRoutes.filter((r) => r.is_active).length
            : undefined,
      },
      {
        key: "students",
        to:
          user?.role === "parent"
            ? "/dashboard/children"
            : "/dashboard/students",
        icon: <Users size={20} />,
        label:
          user?.role === "parent"
            ? t("students.manageStudents")
            : isAdmin
              ? t("nav.allStudents")
              : t("nav.students"),
        route:
          user?.role === "parent"
            ? "/dashboard/children"
            : "/dashboard/students",
        badge:
          user?.role !== "parent"
            ? filteredStudents.filter((s) => s.is_active).length
            : undefined,
      },
      {
        key: "attendance",
        to:
          user?.role === "driver"
            ? "/dashboard/driver-attendance"
            : "/dashboard/attendance",
        icon: <ClipboardCheck size={20} />,
        label: t("students.attendance"),
        route:
          user?.role === "driver"
            ? "/dashboard/driver-attendance"
            : "/dashboard/attendance",
      },
      {
        key: "tracking",
        to: "/dashboard/tracking",
        icon: <MapPin size={20} />,
        label: t("tracking.liveTracking"),
        route: "/dashboard/tracking",
        featureFlag: tenant?.settings?.features?.gpsTracking !== false,
      },
      {
        key: "reports",
        to: "/dashboard/reports",
        icon: <FileBarChart size={20} />,
        label: isAdmin ? t("nav.systemReports") : t("nav.reports"),
        route: "/dashboard/reports",
        featureFlag: tenant?.settings?.features?.reports !== false,
      },
      {
        key: "notifications",
        to: "/dashboard/notifications",
        icon: <Bell size={20} />,
        label: isAdmin ? t("nav.systemNotifications") : t("nav.notifications"),
        route: "/dashboard/notifications",
        featureFlag: tenant?.settings?.features?.notifications !== false,
      },
      {
        key: "evaluation",
        to: "/dashboard/evaluation",
        icon: <Star size={20} />,
        label: t("evaluation.title"),
        route: "/dashboard/evaluation",
        excludeRoles: ["student", "driver"],
      },
      {
        key: "settings",
        to: "/dashboard/settings",
        icon: <Settings size={20} />,
        label: t("nav.settings"),
        route: "/dashboard/settings",
      },
      {
        key: "profile",
        to: "/dashboard/profile",
        icon: <User size={20} />,
        label: t("nav.profile"),
        route: "/dashboard/profile",
      },
    ];

    return navigationItems.filter((item) => {
      // Check feature flags
      if (item.featureFlag === false) return false;

      // Check excluded roles
      if (
        item.excludeRoles &&
        user?.role &&
        item.excludeRoles.includes(user.role)
      )
        return false;

      // Use centralized RBAC route access checking
      if (item.route) {
        const accessResult = canAccessRoute(item.route);
        return accessResult.allowed;
      }

      return true;
    });
  };

  const navItems = getNavItems();
  const isRTL = i18n.language === "ar" || direction === "rtl";

  const tenantLogo = tenant?.settings?.branding?.logo || tenant?.logo_url;
  const tenantName = tenant?.settings?.branding?.schoolName || tenant?.name || "School";
  const tenantTagline = tenant?.settings?.branding?.tagline;

  return (
    <>
      {/* Mobile toggle button - position based on language direction */}
      <Button
        onClick={handleToggle}
        className={`md:hidden fixed top-4 z-50 shadow-lg ${
          isRTL ? "right-4" : "left-4"
        }`}
        variant="default"
        size="sm"
        aria-label="Toggle sidebar"
      >
        {isMobileOpen ? <X size={20} /> : <Menu size={20} />}
      </Button>

      {/* Overlay for mobile - only visible when sidebar is open */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={handleToggle}
          aria-hidden="true"
        />
      )}

      {/* Sidebar - position based on language direction */}
      <aside
        className={`w-64 bg-white dark:bg-gray-800 h-screen fixed top-0 z-40 overflow-y-auto transition-transform duration-300 ease-in-out ${
          isRTL
            ? `right-0 border-l border-gray-200 dark:border-gray-700 ${
                isMobileOpen
                  ? "translate-x-0"
                  : "translate-x-full md:translate-x-0"
              }`
            : `left-0 border-r border-gray-200 dark:border-gray-700 ${
                isMobileOpen
                  ? "translate-x-0"
                  : "-translate-x-full md:translate-x-0"
              }`
        }`}
      >
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <Link to="/" className="flex items-center gap-2 flex-1">
            {tenantLogo ? (
              <img
                src={tenantLogo}
                alt={tenantName}
                className="h-8 w-8 rounded object-cover"
              />
            ) : (
              <div className="h-8 w-8 bg-primary-500 text-white rounded flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M8 6v6"></path>
                  <path d="M16 6v6"></path>
                  <path d="M2 12h20"></path>
                  <path d="M18 18h2a2 2 0 0 0 2-2v-6a8 8 0 0 0-16 0v6a2 2 0 0 0 2 2h2"></path>
                  <path d="M9 19h6"></path>
                  <path d="M9 21v-2"></path>
                  <path d="M15 21v-2"></path>
                </svg>
              </div>
            )}
            <div className="flex flex-col">
              <span className="text-lg font-bold text-gray-900 dark:text-white tracking-tight leading-tight">
                {tenantName}
              </span>
              {tenantTagline && (
                <span className="text-xs text-gray-500 dark:text-gray-400 leading-tight">
                  {tenantTagline}
                </span>
              )}
            </div>
          </Link>
          <Button
            onClick={handleToggle}
            className="md:hidden"
            variant="ghost"
            size="sm"
            aria-label="Close sidebar"
          >
            <X size={20} />
          </Button>
        </div>

        <nav className="mt-4 px-2 space-y-1">
          {navItems.map((item) => (
            <Link
              key={item.to}
              to={item.to}
              onClick={() => {
                if (window.innerWidth < 768) {
                  handleToggle();
                }
              }}
              className={`flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                location.pathname === item.to ||
                location.pathname.startsWith(`${item.to}/`)
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-300 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              <div className="flex items-center">
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </div>
              {item.badge !== undefined && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary-600 rounded-full">
                  {item.badge}
                </span>
              )}
            </Link>
          ))}
        </nav>

        <div className="px-2 mt-auto pb-4 absolute bottom-0 w-full border-t border-gray-200 dark:border-gray-700 pt-4">
          <Button
            onClick={logout}
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            leftIcon={<LogOut size={20} />}
          >
            {t("auth.signOut")}
          </Button>
        </div>
      </aside>

      {/* Content spacer for desktop view */}
      <div className="hidden md:block w-64"></div>
    </>
  );
};
