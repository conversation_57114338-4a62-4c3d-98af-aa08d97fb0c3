import React, { useRef, useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Marker, Popup, Source, Layer } from "react-map-gl";
import { Bus, Navigation, AlertTriangle } from "lucide-react";
import { MapboxMap } from "./MapboxMap";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import { subscribeToBusLocation } from "../../lib/api";
import type { Tables } from "../../lib/api";
import type { RealtimeChannel } from "@supabase/supabase-js";

interface LiveMapProps {
  selectedBusId?: string;
  onBusSelect?: (bus: Tables<"buses">) => void;
  showRoutes?: boolean;
  showGeofences?: boolean;
  showTraffic?: boolean;
  showSpeedInfo?: boolean;
  showETA?: boolean;
  className?: string;
}

interface BusMetrics {
  speed: number;
  heading: number;
  lastUpdate: Date;
  isMoving: boolean;
  distanceTraveled: number;
  eta?: { [stopId: string]: number };
}

interface GeofenceAlert {
  busId: string;
  stopId: string;
  type: "enter" | "exit";
  timestamp: Date;
}

export const LiveMap: React.FC<LiveMapProps> = ({
  selectedBusId,
  onBusSelect,
  showRoutes = false,
  showGeofences = false,
  showTraffic = false,
  showSpeedInfo = true,
  showETA = true,
  className = "",
}) => {
  const { t } = useTranslation();
  const { buses, routes } = useDatabase();
  const [popupInfo, setPopupInfo] = useState<Tables<"buses"> | null>(null);
  const [realtimeBuses, setRealtimeBuses] = useState<Tables<"buses">[]>([]);
  const [subscriptions, setSubscriptions] = useState<RealtimeChannel[]>([]);
  const [geofenceAlerts, setGeofenceAlerts] = useState<GeofenceAlert[]>([]);
  const [routeData, setRouteData] = useState<any>(null);
  const [busMetrics, setBusMetrics] = useState<Map<string, BusMetrics>>(
    new Map(),
  );
  const [outOfRouteAlerts, setOutOfRouteAlerts] = useState<Set<string>>(
    new Set(),
  );
  const [previousLocations, setPreviousLocations] = useState<
    Map<string, { lat: number; lng: number; timestamp: Date }>
  >(new Map());
  const [busGeofenceStates, setBusGeofenceStates] = useState<
    Map<string, Set<string>>
  >(new Map());

  const [viewport, setViewport] = useState({
    latitude: 30.0444,
    longitude: 31.2357,
    zoom: 11,
  });

  // Update bus location from device GPS
  const updateBusLocationFromGPS = useCallback(async (bus: Tables<"buses">) => {
    if (!navigator.geolocation) return;

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;

        try {
          await supabase
            .from("buses")
            .update({
              last_location: `POINT(${longitude} ${latitude})`,
              last_updated: new Date().toISOString(),
            })
            .eq("id", bus.id);
        } catch (error) {
          console.error("Error updating GPS location:", error);
        }
      },
      (error) => console.error("GPS Error:", error),
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 },
    );
  }, []);

  // Simulate real-time location updates for demo
  const simulateLocationUpdate = useCallback(
    async (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const currentLat = location.coordinates[1];
      const currentLng = location.coordinates[0];

      // Simulate small movement (±0.0001 degrees ≈ ±11 meters)
      const newLat = currentLat + (Math.random() - 0.5) * 0.0002;
      const newLng = currentLng + (Math.random() - 0.5) * 0.0002;

      try {
        // Update bus location in database
        await supabase
          .from("buses")
          .update({
            last_location: `POINT(${newLng} ${newLat})`,
            last_updated: new Date().toISOString(),
          })
          .eq("id", bus.id);

        // Update local state
        const updatedBus = {
          ...bus,
          last_location: {
            type: "Point",
            coordinates: [newLng, newLat],
          },
          last_updated: new Date().toISOString(),
        };

        setRealtimeBuses((prev) =>
          prev.map((b) => (b.id === bus.id ? updatedBus : b)),
        );

        // Update metrics
        updateBusMetrics(updatedBus);

        // Check geofencing
        if (showGeofences) {
          checkGeofencing(updatedBus);
        }

        // Check out of route
        checkOutOfRoute(updatedBus);
      } catch (error) {
        console.error("Error updating bus location:", error);
      }
    },
    [showGeofences],
  );

  // Initialize real-time subscriptions
  useEffect(() => {
    setRealtimeBuses(buses);

    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    // Set up real-time subscriptions for each bus
    const newSubscriptions = buses.map((bus) => {
      return subscribeToBusLocation(bus.id, (payload) => {
        if (payload.eventType === "UPDATE") {
          const updatedBus = payload.new as Tables<"buses">;
          setRealtimeBuses((prev) =>
            prev.map((b) => (b.id === updatedBus.id ? updatedBus : b)),
          );

          // Update bus metrics
          updateBusMetrics(updatedBus);

          // Check for geofencing if enabled
          if (showGeofences) {
            checkGeofencing(updatedBus);
          }

          // Check if bus is out of route
          checkOutOfRoute(updatedBus);
        }
      });
    });

    setSubscriptions(newSubscriptions);

    // Set up interval for real-time updates simulation
    const updateInterval = setInterval(() => {
      buses.forEach((bus) => {
        if (bus.last_location) {
          // Simulate real-time location updates
          simulateLocationUpdate(bus);
        }
      });
    }, 5000); // Update every 5 seconds

    return () => {
      newSubscriptions.forEach((sub) => sub.unsubscribe());
      clearInterval(updateInterval);
    };
  }, [buses, showGeofences, simulateLocationUpdate]);

  // Update bus metrics when location changes
  const updateBusMetrics = useCallback(
    (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const currentLat = location.coordinates[1];
      const currentLng = location.coordinates[0];
      const currentTime = new Date();

      const previousData = previousLocations.get(bus.id);

      let speed = 0;
      let heading = 0;
      let distanceTraveled = 0;
      let isMoving = false;

      if (previousData) {
        const timeDiff =
          (currentTime.getTime() - previousData.timestamp.getTime()) / 1000; // seconds
        const distance = calculateDistance(
          previousData.lat,
          previousData.lng,
          currentLat,
          currentLng,
        );

        if (timeDiff > 0) {
          speed = (distance / timeDiff) * 3.6; // Convert m/s to km/h
          isMoving = speed > 1; // Consider moving if speed > 1 km/h

          // Calculate heading (bearing)
          heading = calculateBearing(
            previousData.lat,
            previousData.lng,
            currentLat,
            currentLng,
          );
        }

        const existingMetrics = busMetrics.get(bus.id);
        distanceTraveled = (existingMetrics?.distanceTraveled || 0) + distance;
      }

      // Calculate ETA for each stop if route exists
      const route = routes.find((r) => r.bus_id === bus.id);
      let eta: { [stopId: string]: number } = {};

      if (route?.stops && speed > 0) {
        route.stops.forEach((stop) => {
          const stopLocation = stop.location as any;
          const distanceToStop = calculateDistance(
            currentLat,
            currentLng,
            stopLocation.coordinates[1],
            stopLocation.coordinates[0],
          );
          // Estimate ETA in minutes (assuming current speed)
          eta[stop.id] = Math.round((distanceToStop / 1000 / speed) * 60);
        });
      }

      setBusMetrics(
        (prev) =>
          new Map(
            prev.set(bus.id, {
              speed,
              heading,
              lastUpdate: currentTime,
              isMoving,
              distanceTraveled,
              eta,
            }),
          ),
      );

      setPreviousLocations(
        (prev) =>
          new Map(
            prev.set(bus.id, {
              lat: currentLat,
              lng: currentLng,
              timestamp: currentTime,
            }),
          ),
      );
    },
    [previousLocations, busMetrics, routes],
  );

  // Calculate bearing between two points
  const calculateBearing = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number => {
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const lat1Rad = (lat1 * Math.PI) / 180;
    const lat2Rad = (lat2 * Math.PI) / 180;

    const y = Math.sin(dLng) * Math.cos(lat2Rad);
    const x =
      Math.cos(lat1Rad) * Math.sin(lat2Rad) -
      Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

    let bearing = (Math.atan2(y, x) * 180) / Math.PI;
    return (bearing + 360) % 360;
  };

  // Enhanced geofencing with entry/exit detection
  const checkGeofencing = useCallback(
    (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const busLat = location.coordinates[1];
      const busLng = location.coordinates[0];

      // Find the route for this bus
      const route = routes.find((r) => r.bus_id === bus.id);
      if (!route?.stops) return;

      const currentGeofences = busGeofenceStates.get(bus.id) || new Set();
      const newGeofences = new Set<string>();

      // Check if bus is within geofence of any stop (100m radius)
      route.stops.forEach((stop) => {
        const stopLocation = stop.location as any;
        const stopLat = stopLocation.coordinates[1];
        const stopLng = stopLocation.coordinates[0];

        const distance = calculateDistance(busLat, busLng, stopLat, stopLng);
        const isWithinGeofence = distance <= 100; // 100 meters

        if (isWithinGeofence) {
          newGeofences.add(stop.id);

          // Check if this is a new entry
          if (!currentGeofences.has(stop.id)) {
            const alert: GeofenceAlert = {
              busId: bus.id,
              stopId: stop.id,
              type: "enter",
              timestamp: new Date(),
            };

            setGeofenceAlerts((prev) => [...prev, alert]);
            triggerGeofenceNotification(bus, stop, "enter");
          }
        }
      });

      // Check for exits
      currentGeofences.forEach((stopId) => {
        if (!newGeofences.has(stopId)) {
          const stop = route.stops?.find((s) => s.id === stopId);
          if (stop) {
            const alert: GeofenceAlert = {
              busId: bus.id,
              stopId: stop.id,
              type: "exit",
              timestamp: new Date(),
            };

            setGeofenceAlerts((prev) => [...prev, alert]);
            triggerGeofenceNotification(bus, stop, "exit");
          }
        }
      });

      // Update geofence states
      setBusGeofenceStates((prev) => new Map(prev.set(bus.id, newGeofences)));
    },
    [routes, busGeofenceStates],
  );

  // Check if bus is out of its designated route
  const checkOutOfRoute = useCallback(
    (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const busLat = location.coordinates[1];
      const busLng = location.coordinates[0];

      const route = routes.find((r) => r.bus_id === bus.id);
      if (!route?.stops || route.stops.length < 2) return;

      // Check if bus is within reasonable distance of the route
      const routeBuffer = 500; // 500 meters buffer
      let isOnRoute = false;

      // Check if bus is near any route segment
      for (let i = 0; i < route.stops.length - 1; i++) {
        const stop1 = route.stops[i].location as any;
        const stop2 = route.stops[i + 1].location as any;

        const distanceToSegment = distanceToLineSegment(
          { lat: busLat, lng: busLng },
          { lat: stop1.coordinates[1], lng: stop1.coordinates[0] },
          { lat: stop2.coordinates[1], lng: stop2.coordinates[0] },
        );

        if (distanceToSegment <= routeBuffer) {
          isOnRoute = true;
          break;
        }
      }

      // Also check if bus is near any stop
      if (!isOnRoute) {
        for (const stop of route.stops) {
          const stopLocation = stop.location as any;
          const distanceToStop = calculateDistance(
            busLat,
            busLng,
            stopLocation.coordinates[1],
            stopLocation.coordinates[0],
          );

          if (distanceToStop <= routeBuffer) {
            isOnRoute = true;
            break;
          }
        }
      }

      setOutOfRouteAlerts((prev) => {
        const newAlerts = new Set(prev);
        if (!isOnRoute) {
          if (!newAlerts.has(bus.id)) {
            // Trigger out-of-route alert
            triggerOutOfRouteAlert(bus);
          }
          newAlerts.add(bus.id);
        } else {
          newAlerts.delete(bus.id);
        }
        return newAlerts;
      });
    },
    [routes],
  );

  // Calculate distance from point to line segment
  const distanceToLineSegment = (
    point: { lat: number; lng: number },
    lineStart: { lat: number; lng: number },
    lineEnd: { lat: number; lng: number },
  ): number => {
    const A = point.lat - lineStart.lat;
    const B = point.lng - lineStart.lng;
    const C = lineEnd.lat - lineStart.lat;
    const D = lineEnd.lng - lineStart.lng;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;

    if (lenSq !== 0) {
      param = dot / lenSq;
    }

    let xx, yy;

    if (param < 0) {
      xx = lineStart.lat;
      yy = lineStart.lng;
    } else if (param > 1) {
      xx = lineEnd.lat;
      yy = lineEnd.lng;
    } else {
      xx = lineStart.lat + param * C;
      yy = lineStart.lng + param * D;
    }

    return calculateDistance(point.lat, point.lng, xx, yy);
  };

  // Trigger out-of-route alert
  const triggerOutOfRouteAlert = async (bus: Tables<"buses">) => {
    try {
      // Create notification in database
      await supabase.from("notifications").insert({
        title: t("notifications.outOfRoute"),
        message: `${bus.plate_number} ${t("notifications.outOfRouteMessage")}`,
        user_id: bus.driver_id || "",
        tenant_id: bus.tenant_id,
        type: "alert",
        priority: "high",
        metadata: {
          type: "out_of_route",
          busId: bus.id,
          plateNumber: bus.plate_number,
        },
      });

      // Send push notification to driver and supervisors
      const { data: supervisors } = await supabase
        .from("users")
        .select("id")
        .eq("role", "admin")
        .eq("tenant_id", bus.tenant_id);

      const userIds = [
        bus.driver_id,
        ...(supervisors?.map((s) => s.id) || []),
      ].filter(Boolean);

      if (userIds.length > 0) {
        await supabase.functions.invoke(
          "supabase-functions-send-push-notification",
          {
            body: {
              userIds,
              payload: {
                title: "⚠️ تنبيه: خروج عن المسار",
                body: `الحافلة ${bus.plate_number} خرجت عن المسار المحدد`,
                icon: "/bus-icon.svg",
                tag: `out-of-route-${bus.id}`,
                data: {
                  type: "out_of_route",
                  busId: bus.id,
                  url: `/tracking?bus=${bus.id}`,
                },
                requireInteraction: true,
              },
            },
          },
        );
      }
    } catch (error) {
      console.error("Error creating out-of-route alert:", error);
    }
  };

  // Calculate distance between two coordinates
  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Enhanced geofence notification with push notifications
  const triggerGeofenceNotification = async (
    bus: Tables<"buses">,
    stop: Tables<"route_stops">,
    type: "enter" | "exit",
  ) => {
    try {
      const title = type === "enter" ? "Bus Arrived" : "Bus Departed";
      const message = `${bus.plate_number} ${type === "enter" ? "arrived at" : "left"} ${stop.name}`;

      // Create notification in database
      await supabase.from("notifications").insert({
        title,
        message,
        user_id: bus.driver_id || "",
        tenant_id: bus.tenant_id,
        type: "geofence",
        priority: "medium",
        metadata: {
          type: "geofence",
          busId: bus.id,
          stopId: stop.id,
          action: type,
          plateNumber: bus.plate_number,
          stopName: stop.name,
        },
      });

      // Get students at this stop and notify their parents
      const { data: students } = await supabase
        .from("students")
        .select("parent_id, name")
        .eq("route_stop_id", stop.id);

      if (students && students.length > 0) {
        const parentIds = students
          .filter((student) => student.parent_id)
          .map((student) => student.parent_id!);

        if (parentIds.length > 0) {
          // Send push notification to parents
          await supabase.functions.invoke(
            "supabase-functions-send-push-notification",
            {
              body: {
                userIds: parentIds,
                payload: {
                  title,
                  body: message,
                  icon: "/bus-icon.svg",
                  tag: `geofence-${bus.id}-${stop.id}`,
                  data: {
                    type: "geofence",
                    busId: bus.id,
                    stopId: stop.id,
                    action: type,
                    url: `/tracking?bus=${bus.id}`,
                  },
                  requireInteraction: true,
                },
              },
            },
          );

          // Create notifications for parents
          const parentNotifications = parentIds.map((parentId) => ({
            user_id: parentId,
            title,
            message,
            tenant_id: bus.tenant_id,
            type: "geofence",
            priority: "medium",
            metadata: {
              type: "geofence",
              busId: bus.id,
              stopId: stop.id,
              action: type,
            },
          }));

          await supabase.from("notifications").insert(parentNotifications);
        }
      }
    } catch (error) {
      console.error("Error creating geofence notification:", error);
    }
  };

  // Generate route path data for visualization
  useEffect(() => {
    if (showRoutes && selectedBusId) {
      const bus = realtimeBuses.find((b) => b.id === selectedBusId);
      const route = routes.find((r) => r.bus_id === selectedBusId);

      if (route?.stops && route.stops.length > 1) {
        const coordinates = route.stops
          .sort((a, b) => a.order - b.order)
          .map((stop) => {
            const location = stop.location as any;
            return [location.coordinates[0], location.coordinates[1]];
          });

        setRouteData({
          type: "Feature",
          properties: {},
          geometry: {
            type: "LineString",
            coordinates,
          },
        });
      }
    } else {
      setRouteData(null);
    }
  }, [showRoutes, selectedBusId, realtimeBuses, routes]);

  // Update viewport when bus is selected
  useEffect(() => {
    if (selectedBusId) {
      const bus = realtimeBuses.find((b) => b.id === selectedBusId);
      if (bus?.last_location) {
        const location = bus.last_location as any;
        setViewport({
          latitude: location.coordinates[1],
          longitude: location.coordinates[0],
          zoom: 14,
        });
      }
    }
  }, [selectedBusId, realtimeBuses]);

  const handleBusClick = useCallback(
    (bus: Tables<"buses">) => {
      setPopupInfo(bus);
      if (onBusSelect) onBusSelect(bus);
    },
    [onBusSelect],
  );

  return (
    <MapboxMap
      initialViewState={viewport}
      onViewStateChange={setViewport}
      className={className}
      showNavigation={true}
      mapStyle={
        showTraffic
          ? "mapbox://styles/mapbox/traffic-day-v2"
          : "mapbox://styles/mapbox/streets-v12"
      }
    >
      {/* Route visualization */}
      {showRoutes && routeData && (
        <Source id="route" type="geojson" data={routeData}>
          <Layer
            id="route-line"
            type="line"
            paint={{
              "line-color": "#3B82F6",
              "line-width": 4,
              "line-opacity": 0.8,
            }}
          />
        </Source>
      )}

      {/* Bus markers */}
      {realtimeBuses.map((bus) => {
        if (!bus.last_location) return null;
        const location = bus.last_location as any;
        const isSelected = selectedBusId === bus.id;
        const metrics = busMetrics.get(bus.id);
        const isMoving = metrics?.isMoving || false;
        const isOutOfRoute = outOfRouteAlerts.has(bus.id);
        const speed = metrics?.speed || 0;
        const heading = metrics?.heading || 0;

        return (
          <Marker
            key={bus.id}
            latitude={location.coordinates[1]}
            longitude={location.coordinates[0]}
            onClick={(e) => {
              e.originalEvent.stopPropagation();
              handleBusClick(bus);
            }}
          >
            <div
              className={`relative cursor-pointer transition-all duration-200 ${
                isSelected ? "scale-110" : ""
              }`}
              style={{
                transform: `rotate(${heading}deg)`,
                transformOrigin: "center",
              }}
            >
              <div
                className={`p-2 rounded-full shadow-lg border-2 ${
                  isSelected
                    ? "bg-primary-500 text-white"
                    : "bg-white text-primary-500 hover:bg-primary-50"
                } ${isMoving ? "border-green-400" : "border-gray-300"} ${
                  isOutOfRoute ? "border-red-500 bg-red-100" : ""
                }`}
              >
                {isMoving ? <Navigation size={20} /> : <Bus size={20} />}
              </div>

              {/* Speed indicator */}
              {showSpeedInfo && isMoving && (
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                  {speed.toFixed(0)} km/h
                </div>
              )}

              {/* Real-time indicator */}
              {isMoving && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse" />
              )}

              {/* Out of route alert */}
              {isOutOfRoute && (
                <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                  <AlertTriangle size={10} className="text-white" />
                </div>
              )}

              {/* Geofence alert */}
              {geofenceAlerts.some((alert) => alert.busId === bus.id) && (
                <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                  <AlertTriangle size={10} className="text-yellow-800" />
                </div>
              )}
            </div>
          </Marker>
        );
      })}

      {/* Route stops */}
      {showRoutes &&
        selectedBusId &&
        routes
          .find((r) => r.bus_id === selectedBusId)
          ?.stops?.map((stop) => {
            const location = stop.location as any;
            return (
              <Marker
                key={stop.id}
                latitude={location.coordinates[1]}
                longitude={location.coordinates[0]}
              >
                <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg" />
              </Marker>
            );
          })}

      {/* Geofence circles */}
      {showGeofences &&
        selectedBusId &&
        routes
          .find((r) => r.bus_id === selectedBusId)
          ?.stops?.map((stop) => {
            const location = stop.location as any;
            return (
              <Source
                key={`geofence-${stop.id}`}
                id={`geofence-${stop.id}`}
                type="geojson"
                data={{
                  type: "Feature",
                  properties: {},
                  geometry: {
                    type: "Point",
                    coordinates: [
                      location.coordinates[0],
                      location.coordinates[1],
                    ],
                  },
                }}
              >
                <Layer
                  id={`geofence-circle-${stop.id}`}
                  type="circle"
                  paint={{
                    "circle-radius": {
                      stops: [
                        [0, 0],
                        [20, 50],
                      ],
                      base: 2,
                    },
                    "circle-color": "#3B82F6",
                    "circle-opacity": 0.1,
                    "circle-stroke-color": "#3B82F6",
                    "circle-stroke-width": 2,
                    "circle-stroke-opacity": 0.3,
                  }}
                />
              </Source>
            );
          })}

      {/* Bus info popup */}
      {popupInfo && (
        <Popup
          anchor="bottom"
          latitude={(popupInfo.last_location as any).coordinates[1]}
          longitude={(popupInfo.last_location as any).coordinates[0]}
          onClose={() => setPopupInfo(null)}
          closeOnClick={false}
          className="z-10"
        >
          <div className="p-3 min-w-[200px]">
            <h3 className="font-medium text-gray-900 mb-2">
              {popupInfo.plate_number}
            </h3>
            <div className="space-y-1 text-sm">
              <p className="text-gray-600">
                <span className="font-medium">{t("nav.routes")}:</span>{" "}
                {routes.find((r) => r.bus_id === popupInfo.id)?.name ||
                  t("buses.noRoute")}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">{t("buses.capacity")}:</span>{" "}
                {popupInfo.capacity}
              </p>

              {/* Bus metrics */}
              {busMetrics.get(popupInfo.id) && (
                <>
                  <p className="text-gray-600">
                    <span className="font-medium">{t("tracking.speed")}:</span>{" "}
                    {busMetrics.get(popupInfo.id)!.speed.toFixed(1)} km/h
                  </p>
                  <p className="text-gray-600">
                    <span className="font-medium">{t("tracking.status")}:</span>{" "}
                    <span
                      className={`font-medium ${
                        busMetrics.get(popupInfo.id)!.isMoving
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {busMetrics.get(popupInfo.id)!.isMoving
                        ? t("tracking.moving")
                        : t("tracking.stopped")}
                    </span>
                  </p>
                  <p className="text-gray-600">
                    <span className="font-medium">
                      {t("tracking.distanceTraveled")}:
                    </span>{" "}
                    {(
                      busMetrics.get(popupInfo.id)!.distanceTraveled / 1000
                    ).toFixed(2)}{" "}
                    km
                  </p>
                </>
              )}

              {/* Out of route warning */}
              {outOfRouteAlerts.has(popupInfo.id) && (
                <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-xs">
                  <AlertTriangle size={12} className="inline mr-1" />
                  {t("tracking.outOfRoute")}
                </div>
              )}

              {/* ETA information */}
              {showETA && busMetrics.get(popupInfo.id)?.eta && (
                <div className="mt-2">
                  <p className="text-xs font-medium text-gray-700 mb-1">
                    {t("tracking.nextStops")}:
                  </p>
                  {Object.entries(busMetrics.get(popupInfo.id)!.eta!)
                    .slice(0, 3)
                    .map(([stopId, eta]) => {
                      const stop = routes
                        .find((r) => r.bus_id === popupInfo.id)
                        ?.stops?.find((s) => s.id === stopId);
                      return stop ? (
                        <p key={stopId} className="text-xs text-gray-600">
                          {stop.name}: {eta} min
                        </p>
                      ) : null;
                    })}
                </div>
              )}
              <p className="text-gray-500 text-xs">
                {t("tracking.lastUpdated")}:{" "}
                {popupInfo.last_updated
                  ? new Date(popupInfo.last_updated).toLocaleString()
                  : t("common.never")}
              </p>

              {/* Real-time status */}
              <div className="flex items-center space-x-2 mt-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    popupInfo.last_updated &&
                    Date.now() - new Date(popupInfo.last_updated).getTime() <
                      300000
                      ? "bg-green-400 animate-pulse"
                      : "bg-gray-400"
                  }`}
                />
                <span className="text-xs text-gray-500">
                  {popupInfo.last_updated &&
                  Date.now() - new Date(popupInfo.last_updated).getTime() <
                    300000
                    ? t("tracking.live")
                    : t("tracking.offline")}
                </span>
              </div>
            </div>
          </div>
        </Popup>
      )}
    </MapboxMap>
  );
};
