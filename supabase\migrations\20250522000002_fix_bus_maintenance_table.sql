-- Create bus_maintenance table if it doesn't exist
CREATE TABLE IF NOT EXISTS bus_maintenance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('routine', 'repair', 'inspection')),
  description TEXT NOT NULL,
  cost DECIMAL(10,2),
  scheduled_date DATE NOT NULL,
  completed_date DATE,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'overdue')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_bus_id ON bus_maintenance(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_tenant_id ON bus_maintenance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_scheduled_date ON bus_maintenance(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_status ON bus_maintenance(status);

-- Enable realtime
ALTER PUBLICATION supabase_realtime ADD TABLE bus_maintenance;