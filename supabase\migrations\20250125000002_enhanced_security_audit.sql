-- Enhanced Security Audit Migration
-- Generated on: 2025-01-25
-- Purpose: Implement comprehensive security monitoring and audit system

-- Create enhanced security events table
CREATE TABLE IF NOT EXISTS security_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  description text NOT NULL,
  user_id uuid REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  ip_address inet,
  user_agent text,
  metadata jsonb DEFAULT '{}',
  timestamp timestamptz DEFAULT now(),
  resolved boolean DEFAULT false,
  resolved_at timestamptz,
  resolved_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now()
);

-- Create audit logs table with enhanced fields
CREATE TABLE IF NOT EXISTS audit_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  operation text NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
  old_data jsonb,
  new_data jsonb,
  user_id uuid REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  session_id text,
  ip_address inet,
  user_agent text,
  risk_level text DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  timestamp timestamptz DEFAULT now(),
  metadata jsonb DEFAULT '{}'
);

-- Create user sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  session_token text UNIQUE NOT NULL,
  ip_address inet,
  user_agent text,
  device_fingerprint text,
  location_data jsonb,
  is_active boolean DEFAULT true,
  last_activity timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  created_at timestamptz DEFAULT now(),
  terminated_at timestamptz,
  termination_reason text
);

-- Create permission violations table
CREATE TABLE IF NOT EXISTS permission_violations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  attempted_action text NOT NULL,
  resource_type text NOT NULL,
  resource_id text,
  violation_type text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  ip_address inet,
  user_agent text,
  timestamp timestamptz DEFAULT now(),
  metadata jsonb DEFAULT '{}'
);

-- Create rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  ip_address inet,
  endpoint text NOT NULL,
  request_count integer DEFAULT 1,
  window_start timestamptz DEFAULT now(),
  window_end timestamptz NOT NULL,
  blocked boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Add security-related columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login timestamptz;
ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts integer DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until timestamptz;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at timestamptz DEFAULT now();
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_enabled boolean DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_secret text;
ALTER TABLE users ADD COLUMN IF NOT EXISTS security_questions jsonb;
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_history jsonb DEFAULT '[]';
ALTER TABLE users ADD COLUMN IF NOT EXISTS risk_score integer DEFAULT 0;

-- Create comprehensive indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_tenant_id ON security_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);

CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_operation ON audit_logs(operation);
CREATE INDEX IF NOT EXISTS idx_audit_logs_risk_level ON audit_logs(risk_level);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);

CREATE INDEX IF NOT EXISTS idx_permission_violations_user_id ON permission_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_permission_violations_timestamp ON permission_violations(timestamp);
CREATE INDEX IF NOT EXISTS idx_permission_violations_severity ON permission_violations(severity);

CREATE INDEX IF NOT EXISTS idx_rate_limits_user_id ON rate_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_rate_limits_ip ON rate_limits(ip_address);
CREATE INDEX IF NOT EXISTS idx_rate_limits_endpoint ON rate_limits(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window ON rate_limits(window_start, window_end);

-- Enhanced audit trigger function with risk assessment
CREATE OR REPLACE FUNCTION enhanced_audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  risk_level text := 'low';
  user_role text;
  sensitive_tables text[] := ARRAY['users', 'tenants', 'buses', 'students', 'attendance'];
BEGIN
  -- Determine risk level based on table and operation
  IF TG_TABLE_NAME = ANY(sensitive_tables) THEN
    IF TG_OP = 'DELETE' THEN
      risk_level := 'high';
    ELSIF TG_OP = 'UPDATE' THEN
      risk_level := 'medium';
    END IF;
  END IF;

  -- Get user role for additional context
  IF auth.uid() IS NOT NULL THEN
    SELECT role INTO user_role FROM users WHERE id = auth.uid();
  END IF;

  -- Insert audit log
  INSERT INTO audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    tenant_id,
    risk_level,
    metadata
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    auth.uid(),
    COALESCE(
      CASE WHEN TG_OP = 'DELETE' THEN OLD.tenant_id ELSE NEW.tenant_id END,
      (auth.jwt() ->> 'tenant_id')::uuid
    ),
    risk_level,
    jsonb_build_object(
      'user_role', user_role,
      'table_name', TG_TABLE_NAME,
      'operation', TG_OP
    )
  );
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply enhanced audit triggers to all sensitive tables
DROP TRIGGER IF EXISTS audit_users_trigger ON users;
CREATE TRIGGER audit_users_trigger
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

DROP TRIGGER IF EXISTS audit_tenants_trigger ON tenants;
CREATE TRIGGER audit_tenants_trigger
  AFTER INSERT OR UPDATE OR DELETE ON tenants
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

DROP TRIGGER IF EXISTS audit_buses_trigger ON buses;
CREATE TRIGGER audit_buses_trigger
  AFTER INSERT OR UPDATE OR DELETE ON buses
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

DROP TRIGGER IF EXISTS audit_students_trigger ON students;
CREATE TRIGGER audit_students_trigger
  AFTER INSERT OR UPDATE OR DELETE ON students
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

DROP TRIGGER IF EXISTS audit_attendance_trigger ON attendance;
CREATE TRIGGER audit_attendance_trigger
  AFTER INSERT OR UPDATE OR DELETE ON attendance
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
  p_event_type text,
  p_severity text,
  p_description text,
  p_metadata jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  event_id uuid;
BEGIN
  INSERT INTO security_events (
    event_type,
    severity,
    description,
    user_id,
    tenant_id,
    metadata
  ) VALUES (
    p_event_type,
    p_severity,
    p_description,
    auth.uid(),
    (auth.jwt() ->> 'tenant_id')::uuid,
    p_metadata
  ) RETURNING id INTO event_id;
  
  RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log permission violations
CREATE OR REPLACE FUNCTION log_permission_violation(
  p_attempted_action text,
  p_resource_type text,
  p_resource_id text DEFAULT NULL,
  p_violation_type text DEFAULT 'unauthorized_access',
  p_severity text DEFAULT 'medium'
)
RETURNS uuid AS $$
DECLARE
  violation_id uuid;
BEGIN
  INSERT INTO permission_violations (
    user_id,
    tenant_id,
    attempted_action,
    resource_type,
    resource_id,
    violation_type,
    severity
  ) VALUES (
    auth.uid(),
    (auth.jwt() ->> 'tenant_id')::uuid,
    p_attempted_action,
    p_resource_type,
    p_resource_id,
    p_violation_type,
    p_severity
  ) RETURNING id INTO violation_id;
  
  RETURN violation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old audit logs and security events
CREATE OR REPLACE FUNCTION cleanup_security_data()
RETURNS integer AS $$
DECLARE
  deleted_count integer := 0;
  temp_count integer;
BEGIN
  -- Clean up audit logs older than 1 year
  DELETE FROM audit_logs 
  WHERE timestamp < now() - interval '1 year'
  AND risk_level = 'low';
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;
  
  -- Clean up resolved security events older than 6 months
  DELETE FROM security_events 
  WHERE timestamp < now() - interval '6 months'
  AND resolved = true
  AND severity IN ('low', 'medium');
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;
  
  -- Clean up expired sessions
  DELETE FROM user_sessions 
  WHERE expires_at < now() OR (last_activity < now() - interval '30 days');
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;
  
  -- Clean up old rate limit records
  DELETE FROM rate_limits 
  WHERE window_end < now() - interval '1 day';
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to detect suspicious activity
CREATE OR REPLACE FUNCTION detect_suspicious_activity()
RETURNS TABLE(
  user_id uuid,
  activity_type text,
  risk_score integer,
  details jsonb
) AS $$
BEGIN
  RETURN QUERY
  -- Detect users with multiple failed login attempts
  SELECT 
    u.id,
    'excessive_failed_logins'::text,
    (u.failed_login_attempts * 10)::integer,
    jsonb_build_object(
      'failed_attempts', u.failed_login_attempts,
      'last_attempt', u.updated_at
    )
  FROM users u
  WHERE u.failed_login_attempts >= 5
  AND u.updated_at > now() - interval '1 hour'
  
  UNION ALL
  
  -- Detect users with unusual permission violations
  SELECT 
    pv.user_id,
    'permission_violations'::text,
    (COUNT(*) * 15)::integer,
    jsonb_build_object(
      'violation_count', COUNT(*),
      'latest_violation', MAX(pv.timestamp)
    )
  FROM permission_violations pv
  WHERE pv.timestamp > now() - interval '1 hour'
  GROUP BY pv.user_id
  HAVING COUNT(*) >= 3;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable RLS on new security tables
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_violations ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;

-- RLS policies for security tables
CREATE POLICY "Admin access to security events" ON security_events
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Users can view their own security events" ON security_events
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin access to audit logs" ON audit_logs
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Users can view their own sessions" ON user_sessions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin access to permission violations" ON permission_violations
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Users can view their own violations" ON permission_violations
  FOR SELECT USING (user_id = auth.uid());

-- Grant necessary permissions
GRANT SELECT, INSERT ON security_events TO authenticated;
GRANT SELECT, INSERT ON audit_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON user_sessions TO authenticated;
GRANT SELECT, INSERT ON permission_violations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON rate_limits TO authenticated;

COMMIT;
