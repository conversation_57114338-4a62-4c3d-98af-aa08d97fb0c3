/**
 * Enhanced Security Middleware
 * Implements server-side security controls and fixes audit issues
 */

import { User, UserRole } from "../types";
import {
  Permission,
  DataScope,
  ResourceType,
  Action,
  PermissionContext,
} from "./rbac";
import {
  OPTIMIZED_ROLE_PERMISSIONS,
  OPTIMIZED_ROLE_DATA_SCOPE,
} from "./rbacOptimized";

// Enhanced Security Configuration
interface SecurityConfig {
  enableRateLimit: boolean;
  enableAuditLogging: boolean;
  enableSessionValidation: boolean;
  enableIPRestrictions: boolean;
  enableGeofencing: boolean;
  maxSessionsPerUser: number;
  sessionTimeoutMinutes: number;
  rateLimitWindow: number; // minutes
  rateLimitMaxRequests: number;
}

// Security Event Types
enum SecurityEventType {
  AUTHENTICATION_SUCCESS = "auth_success",
  AUTHENTICATION_FAILURE = "auth_failure",
  PERMISSION_GRANTED = "permission_granted",
  PERMISSION_DENIED = "permission_denied",
  RATE_LIMIT_EXCEEDED = "rate_limit_exceeded",
  SUSPICIOUS_ACTIVITY = "suspicious_activity",
  SESSION_CREATED = "session_created",
  SESSION_EXPIRED = "session_expired",
  IP_RESTRICTION_VIOLATION = "ip_restriction_violation",
  GEOFENCE_VIOLATION = "geofence_violation",
}

// Security Event Severity
enum SecuritySeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

// Enhanced Security Context
interface EnhancedSecurityContext {
  userId?: string;
  tenantId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  location?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  };
  timestamp: Date;
  resourceId?: string;
  resourceOwnerId?: string;
  resourceTenantId?: string;
  resourceData?: any;
}

// Rate Limiting Store (In production, use Redis)
class RateLimitStore {
  private static store = new Map<
    string,
    { count: number; resetTime: number }
  >();

  static check(
    key: string,
    maxRequests: number,
    windowMs: number,
  ): {
    allowed: boolean;
    remainingRequests: number;
    resetTime: Date;
  } {
    const now = Date.now();
    const data = this.store.get(key) || { count: 0, resetTime: now + windowMs };

    if (now >= data.resetTime) {
      data.count = 0;
      data.resetTime = now + windowMs;
    }

    if (data.count >= maxRequests) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: new Date(data.resetTime),
      };
    }

    data.count++;
    this.store.set(key, data);

    return {
      allowed: true,
      remainingRequests: maxRequests - data.count,
      resetTime: new Date(data.resetTime),
    };
  }

  static clear(key: string): void {
    this.store.delete(key);
  }

  static cleanup(): void {
    const now = Date.now();
    for (const [key, data] of this.store.entries()) {
      if (now >= data.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

// Enhanced Security Middleware
export class EnhancedSecurityMiddleware {
  private static config: SecurityConfig = {
    enableRateLimit: true,
    enableAuditLogging: true,
    enableSessionValidation: true,
    enableIPRestrictions: false,
    enableGeofencing: false,
    maxSessionsPerUser: 5,
    sessionTimeoutMinutes: 480, // 8 hours
    rateLimitWindow: 1, // 1 minute
    rateLimitMaxRequests: 100,
  };

  /**
   * Main security check function
   */
  static async checkSecurity(
    user: User,
    resource: ResourceType,
    action: Action,
    context: EnhancedSecurityContext,
  ): Promise<{
    allowed: boolean;
    error?: string;
    securityLevel: SecuritySeverity;
    metadata?: any;
  }> {
    try {
      // 1. Basic user validation
      const userValidation = this.validateUser(user);
      if (!userValidation.valid) {
        await this.logSecurityEvent(
          SecurityEventType.AUTHENTICATION_FAILURE,
          SecuritySeverity.HIGH,
          userValidation.error || "User validation failed",
          context,
        );
        return {
          allowed: false,
          error: userValidation.error,
          securityLevel: SecuritySeverity.HIGH,
        };
      }

      // 2. Session validation
      if (this.config.enableSessionValidation) {
        const sessionValidation = await this.validateSession(user, context);
        if (!sessionValidation.valid) {
          await this.logSecurityEvent(
            SecurityEventType.SESSION_EXPIRED,
            SecuritySeverity.MEDIUM,
            sessionValidation.error || "Session validation failed",
            context,
          );
          return {
            allowed: false,
            error: sessionValidation.error,
            securityLevel: SecuritySeverity.MEDIUM,
          };
        }
      }

      // 3. Rate limiting
      if (this.config.enableRateLimit) {
        const rateLimitCheck = this.checkRateLimit(user, action, context);
        if (!rateLimitCheck.allowed) {
          await this.logSecurityEvent(
            SecurityEventType.RATE_LIMIT_EXCEEDED,
            SecuritySeverity.MEDIUM,
            "Rate limit exceeded",
            context,
            { remainingRequests: rateLimitCheck.remainingRequests },
          );
          return {
            allowed: false,
            error: "Rate limit exceeded. Please try again later.",
            securityLevel: SecuritySeverity.MEDIUM,
            metadata: { resetTime: rateLimitCheck.resetTime },
          };
        }
      }

      // 4. IP restrictions
      if (this.config.enableIPRestrictions) {
        const ipCheck = this.checkIPRestrictions(user, context);
        if (!ipCheck.allowed) {
          await this.logSecurityEvent(
            SecurityEventType.IP_RESTRICTION_VIOLATION,
            SecuritySeverity.HIGH,
            "Access from unauthorized IP address",
            context,
          );
          return {
            allowed: false,
            error: "Access denied from this IP address",
            securityLevel: SecuritySeverity.HIGH,
          };
        }
      }

      // 5. Geofencing (for drivers and field staff)
      if (
        this.config.enableGeofencing &&
        this.requiresGeofencing(user.role as UserRole)
      ) {
        const geofenceCheck = await this.checkGeofencing(user, context);
        if (!geofenceCheck.allowed) {
          await this.logSecurityEvent(
            SecurityEventType.GEOFENCE_VIOLATION,
            SecuritySeverity.MEDIUM,
            "Access from outside authorized area",
            context,
          );
          return {
            allowed: false,
            error: "Access denied from current location",
            securityLevel: SecuritySeverity.MEDIUM,
          };
        }
      }

      // 6. Permission validation
      const permissionCheck = this.validatePermissions(
        user,
        resource,
        action,
        context,
      );
      if (!permissionCheck.allowed) {
        await this.logSecurityEvent(
          SecurityEventType.PERMISSION_DENIED,
          SecuritySeverity.MEDIUM,
          "Insufficient permissions",
          context,
          { resource, action, userRole: user.role },
        );
        return {
          allowed: false,
          error: permissionCheck.error,
          securityLevel: SecuritySeverity.MEDIUM,
        };
      }

      // 7. Suspicious activity detection
      const suspiciousActivity = await this.detectSuspiciousActivity(
        user,
        context,
      );
      if (suspiciousActivity.detected) {
        await this.logSecurityEvent(
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          SecuritySeverity.HIGH,
          "Suspicious activity detected",
          context,
          suspiciousActivity.details,
        );
        // Don't block, but log for investigation
      }

      // Success - log and allow
      await this.logSecurityEvent(
        SecurityEventType.PERMISSION_GRANTED,
        SecuritySeverity.LOW,
        "Access granted",
        context,
        { resource, action },
      );

      return {
        allowed: true,
        securityLevel: SecuritySeverity.LOW,
      };
    } catch (error) {
      console.error("Security check error:", error);
      return {
        allowed: false,
        error: "Security validation failed",
        securityLevel: SecuritySeverity.CRITICAL,
      };
    }
  }

  /**
   * Validate user account status
   */
  private static validateUser(user: User): { valid: boolean; error?: string } {
    if (!user) {
      return { valid: false, error: "User not authenticated" };
    }

    if (!user.is_active) {
      return { valid: false, error: "Account is deactivated" };
    }

    if (user.metadata?.account_locked_until) {
      const lockoutTime = new Date(user.metadata.account_locked_until);
      if (lockoutTime > new Date()) {
        return { valid: false, error: "Account is temporarily locked" };
      }
    }

    return { valid: true };
  }

  /**
   * Validate user session
   */
  private static async validateSession(
    user: User,
    context: EnhancedSecurityContext,
  ): Promise<{ valid: boolean; error?: string }> {
    if (!context.sessionId) {
      return { valid: false, error: "No session ID provided" };
    }

    // In a real implementation, validate against database
    // For now, assume session is valid if sessionId is provided
    return { valid: true };
  }

  /**
   * Check rate limiting
   */
  private static checkRateLimit(
    user: User,
    action: Action,
    context: EnhancedSecurityContext,
  ): {
    allowed: boolean;
    remainingRequests: number;
    resetTime: Date;
  } {
    const key = `rate_limit_${user.id}_${action}`;
    const windowMs = this.config.rateLimitWindow * 60 * 1000;

    return RateLimitStore.check(key, this.getActionRateLimit(action), windowMs);
  }

  /**
   * Get rate limit for specific action
   */
  private static getActionRateLimit(action: Action): number {
    const limits: Record<Action, number> = {
      [Action.CREATE]: 10,
      [Action.UPDATE]: 20,
      [Action.DELETE]: 5,
      [Action.read]: 100,
      [Action.MANAGE]: 15,
      [Action.ASSIGN]: 10,
      [Action.TRACK]: 50,
      [Action.EXPORT]: 3,
      [Action.SCHEDULE]: 5,
      [Action.ANALYZE]: 10,
      [Action.APPROVE]: 10,
      [Action.MONITOR]: 30,
      [Action.ALERT]: 20,
      [Action.CONFIGURE]: 5,
    };
    return limits[action] || this.config.rateLimitMaxRequests;
  }

  /**
   * Check IP restrictions
   */
  private static checkIPRestrictions(
    user: User,
    context: EnhancedSecurityContext,
  ): { allowed: boolean; error?: string } {
    if (!context.ipAddress) {
      return { allowed: true }; // No IP to check
    }

    const allowedIPs = user.metadata?.allowed_ips as string[];
    if (!allowedIPs || allowedIPs.length === 0) {
      return { allowed: true }; // No restrictions
    }

    if (!allowedIPs.includes(context.ipAddress)) {
      return {
        allowed: false,
        error: "Access denied from this IP address",
      };
    }

    return { allowed: true };
  }

  /**
   * Check if role requires geofencing
   */
  private static requiresGeofencing(role: UserRole): boolean {
    return [UserRole.DRIVER, UserRole.SUPERVISOR].includes(role);
  }

  /**
   * Check geofencing restrictions
   */
  private static async checkGeofencing(
    user: User,
    context: EnhancedSecurityContext,
  ): Promise<{ allowed: boolean; error?: string }> {
    if (!context.location) {
      return { allowed: true }; // No location to check
    }

    // In a real implementation, check against authorized areas
    // For now, assume location is valid
    return { allowed: true };
  }

  /**
   * Validate permissions using optimized RBAC
   */
  private static validatePermissions(
    user: User,
    resource: ResourceType,
    action: Action,
    context: EnhancedSecurityContext,
  ): { allowed: boolean; error?: string } {
    const userRole = user.role as UserRole;
    const rolePermissions = OPTIMIZED_ROLE_PERMISSIONS[userRole] || [];
    const roleDataScopes = OPTIMIZED_ROLE_DATA_SCOPE[userRole] || [];

    // Check if user has required permission
    const requiredPermission = this.getRequiredPermission(
      resource,
      action,
      userRole,
    );
    if (requiredPermission && !rolePermissions.includes(requiredPermission)) {
      return {
        allowed: false,
        error: `Insufficient permissions for ${action} on ${resource}`,
      };
    }

    // Check data scope restrictions
    const scopeCheck = this.validateDataScope(
      user,
      resource,
      roleDataScopes,
      context,
    );
    if (!scopeCheck.allowed) {
      return scopeCheck;
    }

    return { allowed: true };
  }

  /**
   * Get required permission for resource/action combination
   */
  private static getRequiredPermission(
    resource: ResourceType,
    action: Action,
    userRole: UserRole,
  ): Permission | null {
    // Simplified mapping - in production, use comprehensive mapping
    const mapping: Record<string, Permission> = {
      [`${resource}_${action}_${userRole}`]: this.getSpecificPermission(
        resource,
        action,
        userRole,
      ),
      [`${resource}_${action}`]: this.getGenericPermission(resource, action),
    };

    const key = `${resource}_${action}_${userRole}`;
    const fallbackKey = `${resource}_${action}`;

    return mapping[key] || mapping[fallbackKey] || null;
  }

  /**
   * Get specific permission for role
   */
  private static getSpecificPermission(
    resource: ResourceType,
    action: Action,
    userRole: UserRole,
  ): Permission | null {
    // Implementation would map specific combinations
    return null;
  }

  /**
   * Get generic permission
   */
  private static getGenericPermission(
    resource: ResourceType,
    action: Action,
  ): Permission | null {
    // Implementation would map generic combinations
    return null;
  }

  /**
   * Validate data scope access
   */
  private static validateDataScope(
    user: User,
    resource: ResourceType,
    allowedScopes: DataScope[],
    context: EnhancedSecurityContext,
  ): { allowed: boolean; error?: string } {
    // Global scope (admin only)
    if (allowedScopes.includes(DataScope.GLOBAL)) {
      return { allowed: true };
    }

    // Tenant scope
    if (allowedScopes.includes(DataScope.TENANT)) {
      if (context.resourceTenantId && user.tenant_id) {
        if (context.resourceTenantId === user.tenant_id) {
          return { allowed: true };
        }
      } else {
        return { allowed: true }; // No tenant restriction
      }
    }

    // Personal scope
    if (allowedScopes.includes(DataScope.PERSONAL)) {
      if (context.resourceOwnerId && context.userId) {
        if (context.resourceOwnerId === context.userId) {
          return { allowed: true };
        }
      }
    }

    // Assigned scope (for drivers, etc.)
    if (allowedScopes.includes(DataScope.ASSIGNED)) {
      if (this.checkAssignedAccess(user, resource, context)) {
        return { allowed: true };
      }
    }

    // Children scope (for parents)
    if (allowedScopes.includes(DataScope.CHILDREN)) {
      if (resource === ResourceType.STUDENT && context.resourceData) {
        if (context.resourceData.parent_id === user.id) {
          return { allowed: true };
        }
      }
    }

    return {
      allowed: false,
      error: "Access denied - insufficient data scope permissions",
    };
  }

  /**
   * Check assigned access (for drivers, etc.)
   */
  private static checkAssignedAccess(
    user: User,
    resource: ResourceType,
    context: EnhancedSecurityContext,
  ): boolean {
    if (!context.resourceData || !context.userId) {
      return false;
    }

    switch (resource) {
      case ResourceType.BUS:
        return context.resourceData.driver_id === context.userId;
      case ResourceType.ROUTE:
        return context.resourceData.bus?.driver_id === context.userId;
      case ResourceType.STUDENT:
        return (
          context.resourceData.route_stop?.route?.bus?.driver_id ===
          context.userId
        );
      default:
        return false;
    }
  }

  /**
   * Detect suspicious activity
   */
  private static async detectSuspiciousActivity(
    user: User,
    context: EnhancedSecurityContext,
  ): Promise<{ detected: boolean; details?: any }> {
    // In a real implementation, this would check patterns like:
    // - Multiple failed attempts
    // - Unusual access patterns
    // - Access from new locations/devices
    // - Rapid successive requests

    return { detected: false };
  }

  /**
   * Log security events
   */
  private static async logSecurityEvent(
    eventType: SecurityEventType,
    severity: SecuritySeverity,
    description: string,
    context: EnhancedSecurityContext,
    metadata?: any,
  ): Promise<void> {
    const securityEvent = {
      eventType,
      severity,
      description,
      userId: context.userId,
      tenantId: context.tenantId,
      sessionId: context.sessionId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      location: context.location,
      timestamp: context.timestamp,
      metadata,
    };

    // In production, store in database
    console.log("Security Event:", securityEvent);

    // Send alerts for high/critical severity events
    if ([SecuritySeverity.HIGH, SecuritySeverity.CRITICAL].includes(severity)) {
      await this.sendSecurityAlert(securityEvent);
    }
  }

  /**
   * Send security alerts
   */
  private static async sendSecurityAlert(event: any): Promise<void> {
    // In production, send to security team via email/Slack/etc.
    console.warn("SECURITY ALERT:", event);
  }

  /**
   * Update security configuration
   */
  static updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current security configuration
   */
  static getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * Cleanup expired rate limit entries
   */
  static cleanup(): void {
    RateLimitStore.cleanup();
  }
}

export default EnhancedSecurityMiddleware;
