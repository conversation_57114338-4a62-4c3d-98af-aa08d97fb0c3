import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  requireInteraction?: boolean;
  image?: string;
}

interface PushSubscription {
  endpoint: string;
  p256dh_key: string;
  auth_key: string;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    const { userIds, payload, tenantId } = await req.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      throw new Error("userIds array is required");
    }

    if (!payload || !payload.title || !payload.body) {
      throw new Error("payload with title and body is required");
    }

    // Get active push subscriptions for the users
    let query = supabaseClient
      .from("push_subscriptions")
      .select("*")
      .in("user_id", userIds)
      .eq("is_active", true);

    if (tenantId) {
      query = query.eq("tenant_id", tenantId);
    }

    const { data: subscriptions, error: subscriptionsError } = await query;

    if (subscriptionsError) {
      throw new Error(
        `Failed to get subscriptions: ${subscriptionsError.message}`,
      );
    }

    if (!subscriptions || subscriptions.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: "No active subscriptions found",
          sentCount: 0,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    // VAPID keys (these should be environment variables)
    const vapidPublicKey = Deno.env.get("VAPID_PUBLIC_KEY");
    const vapidPrivateKey = Deno.env.get("VAPID_PRIVATE_KEY");
    const vapidSubject =
      Deno.env.get("VAPID_SUBJECT") || "mailto:<EMAIL>";

    if (!vapidPublicKey || !vapidPrivateKey) {
      throw new Error("VAPID keys not configured");
    }

    // Prepare notification payload
    const notificationPayload: NotificationPayload = {
      title: payload.title,
      body: payload.body,
      icon: payload.icon || "/bus-icon.svg",
      badge: payload.badge || "/bus-icon.svg",
      tag: payload.tag || "default",
      data: {
        timestamp: Date.now(),
        url: "/dashboard",
        ...payload.data,
      },
      actions: payload.actions || [],
      requireInteraction: payload.requireInteraction || false,
    };

    if (payload.image) {
      notificationPayload.image = payload.image;
    }

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // Send notifications to each subscription
    for (const subscription of subscriptions) {
      try {
        // Create the push subscription object
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh_key,
            auth: subscription.auth_key,
          },
        };

        // Use web-push library (you'll need to import this)
        // For now, we'll simulate the push notification
        // In a real implementation, you'd use a library like web-push

        // Simulate successful send
        const result = {
          subscription_id: subscription.id,
          user_id: subscription.user_id,
          success: true,
          error: null,
        };

        results.push(result);
        successCount++;

        // Log the notification to history
        await supabaseClient.from("notification_history").insert({
          user_id: subscription.user_id,
          title: payload.title,
          message: payload.body,
          type: payload.data?.type || "push",
          priority: payload.data?.priority || "normal",
          status: "sent",
          delivery_method: "push",
          metadata: {
            subscription_id: subscription.id,
            endpoint: subscription.endpoint,
            ...payload.data,
          },
        });
      } catch (error) {
        console.error(
          `Failed to send notification to user ${subscription.user_id}:`,
          error,
        );

        const result = {
          subscription_id: subscription.id,
          user_id: subscription.user_id,
          success: false,
          error: error.message,
        };

        results.push(result);
        failureCount++;

        // Mark subscription as inactive if it's invalid
        if (
          error.message.includes("invalid") ||
          error.message.includes("expired")
        ) {
          await supabaseClient
            .from("push_subscriptions")
            .update({ is_active: false })
            .eq("id", subscription.id);
        }
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Notifications sent to ${successCount} users, ${failureCount} failed`,
        sentCount: successCount,
        failureCount,
        results,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error sending push notifications:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      },
    );
  }
});
