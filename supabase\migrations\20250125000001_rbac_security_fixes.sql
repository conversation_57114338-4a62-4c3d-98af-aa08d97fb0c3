-- RBAC Security Fixes Migration
-- Generated: 2025-01-25
-- Purpose: Fix critical security issues found in audit

-- Add missing RLS policies for uncovered tables
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;

-- Enhanced RLS policies for route_stops
CREATE POLICY "admin_all_route_stops" ON route_stops
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "school_staff_tenant_route_stops" ON route_stops
FOR ALL USING (
  auth.jwt() ->> 'role' IN ('school_manager', 'supervisor') AND
  EXISTS (
    SELECT 1 FROM routes r 
    WHERE r.id = route_stops.route_id 
    AND r.tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  )
);

CREATE POLICY "driver_assigned_route_stops" ON route_stops
FOR SELECT USING (
  auth.jwt() ->> 'role' = 'driver' AND
  EXISTS (
    SELECT 1 FROM routes r
    JOIN buses b ON b.id = r.bus_id
    WHERE r.id = route_stops.route_id
    AND b.driver_id = auth.uid()
  )
);

CREATE POLICY "parent_children_route_stops" ON route_stops
FOR SELECT USING (
  auth.jwt() ->> 'role' = 'parent' AND
  EXISTS (
    SELECT 1 FROM students s
    WHERE s.route_stop_id = route_stops.id
    AND s.parent_id = auth.uid()
  )
);

-- Enhanced RLS policies for push_subscriptions
CREATE POLICY "admin_all_push_subscriptions" ON push_subscriptions
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "users_own_push_subscriptions" ON push_subscriptions
FOR ALL USING (user_id = auth.uid());

CREATE POLICY "school_manager_tenant_push_subscriptions" ON push_subscriptions
FOR SELECT USING (
  auth.jwt() ->> 'role' = 'school_manager' AND
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

-- Enhanced RLS policies for notification_templates
CREATE POLICY "admin_all_notification_templates" ON notification_templates
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "school_manager_tenant_notification_templates" ON notification_templates
FOR ALL USING (
  auth.jwt() ->> 'role' = 'school_manager' AND
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

CREATE POLICY "supervisor_tenant_notification_templates" ON notification_templates
FOR SELECT USING (
  auth.jwt() ->> 'role' = 'supervisor' AND
  tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
);

-- Add composite indexes for better performance and security
CREATE INDEX IF NOT EXISTS idx_users_tenant_role ON users(tenant_id, role) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_buses_tenant_driver ON buses(tenant_id, driver_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_routes_tenant_bus ON routes(tenant_id, bus_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_students_tenant_parent ON students(tenant_id, parent_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_students_parent_active ON students(parent_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_attendance_tenant_date ON attendance(tenant_id, recorded_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_tenant ON notifications(user_id, tenant_id, created_at);

-- Add geospatial indexes for location-based queries
CREATE INDEX IF NOT EXISTS idx_buses_location ON buses USING GIST(last_location) WHERE last_location IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_route_stops_location ON route_stops USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_attendance_location ON attendance USING GIST(location) WHERE location IS NOT NULL;

-- Create enhanced audit log table with better structure
CREATE TABLE IF NOT EXISTS enhanced_audit_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  operation text NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
  old_data jsonb,
  new_data jsonb,
  user_id uuid REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  timestamp timestamptz DEFAULT now(),
  ip_address inet,
  user_agent text,
  session_id text,
  resource_type text,
  resource_id text,
  severity text DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  metadata jsonb DEFAULT '{}'
);

-- Create indexes for audit log performance
CREATE INDEX IF NOT EXISTS idx_enhanced_audit_logs_table ON enhanced_audit_logs(table_name, timestamp);
CREATE INDEX IF NOT EXISTS idx_enhanced_audit_logs_user ON enhanced_audit_logs(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_enhanced_audit_logs_tenant ON enhanced_audit_logs(tenant_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_enhanced_audit_logs_resource ON enhanced_audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_audit_logs_severity ON enhanced_audit_logs(severity, timestamp);

-- Create permission validation table
CREATE TABLE IF NOT EXISTS permission_validations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  resource_type text NOT NULL,
  resource_id text,
  action text NOT NULL,
  permission_granted boolean NOT NULL,
  validation_context jsonb DEFAULT '{}',
  timestamp timestamptz DEFAULT now(),
  ip_address inet,
  user_agent text
);

-- Create index for permission validation queries
CREATE INDEX IF NOT EXISTS idx_permission_validations_user ON permission_validations(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_permission_validations_resource ON permission_validations(resource_type, action, timestamp);
CREATE INDEX IF NOT EXISTS idx_permission_validations_denied ON permission_validations(permission_granted, timestamp) WHERE permission_granted = false;

-- Create session management table with enhanced security
CREATE TABLE IF NOT EXISTS secure_user_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  tenant_id uuid REFERENCES tenants(id),
  session_token text UNIQUE NOT NULL,
  refresh_token text UNIQUE,
  ip_address inet NOT NULL,
  user_agent text,
  device_fingerprint text,
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  last_activity timestamptz DEFAULT now(),
  is_active boolean DEFAULT true,
  logout_reason text,
  security_flags jsonb DEFAULT '{}'
);

-- Create indexes for session management
CREATE INDEX IF NOT EXISTS idx_secure_sessions_user ON secure_user_sessions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_token ON secure_user_sessions(session_token) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_secure_sessions_expires ON secure_user_sessions(expires_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_secure_sessions_activity ON secure_user_sessions(last_activity) WHERE is_active = true;

-- Enhanced audit trigger function with better logging
CREATE OR REPLACE FUNCTION enhanced_audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id uuid;
  current_tenant_id uuid;
  resource_type text;
  severity_level text;
BEGIN
  -- Get current user context
  current_user_id := auth.uid();
  current_tenant_id := (auth.jwt() ->> 'tenant_id')::uuid;
  
  -- Determine resource type from table name
  resource_type := TG_TABLE_NAME;
  
  -- Determine severity based on operation and table
  severity_level := CASE 
    WHEN TG_OP = 'DELETE' THEN 'warning'
    WHEN TG_TABLE_NAME IN ('users', 'tenants', 'buses') THEN 'info'
    ELSE 'info'
  END;
  
  -- Insert enhanced audit log
  INSERT INTO enhanced_audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    tenant_id,
    resource_type,
    resource_id,
    severity,
    metadata
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    current_user_id,
    COALESCE(
      current_tenant_id,
      CASE WHEN TG_OP = 'DELETE' THEN OLD.tenant_id ELSE NEW.tenant_id END
    ),
    resource_type,
    CASE WHEN TG_OP = 'DELETE' THEN OLD.id::text ELSE NEW.id::text END,
    severity_level,
    jsonb_build_object(
      'table_schema', TG_TABLE_SCHEMA,
      'trigger_name', TG_NAME,
      'session_info', current_setting('application_name', true)
    )
  );
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Replace existing audit triggers with enhanced version
DROP TRIGGER IF EXISTS audit_users_trigger ON users;
DROP TRIGGER IF EXISTS audit_buses_trigger ON buses;
DROP TRIGGER IF EXISTS audit_students_trigger ON students;
DROP TRIGGER IF EXISTS audit_attendance_trigger ON attendance;

CREATE TRIGGER enhanced_audit_users_trigger
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

CREATE TRIGGER enhanced_audit_buses_trigger
  AFTER INSERT OR UPDATE OR DELETE ON buses
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

CREATE TRIGGER enhanced_audit_students_trigger
  AFTER INSERT OR UPDATE OR DELETE ON students
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

CREATE TRIGGER enhanced_audit_attendance_trigger
  AFTER INSERT OR UPDATE OR DELETE ON attendance
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

CREATE TRIGGER enhanced_audit_routes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON routes
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

CREATE TRIGGER enhanced_audit_notifications_trigger
  AFTER INSERT OR UPDATE OR DELETE ON notifications
  FOR EACH ROW EXECUTE FUNCTION enhanced_audit_trigger_function();

-- Function to validate user permissions
CREATE OR REPLACE FUNCTION validate_user_permission(
  p_user_id uuid,
  p_resource_type text,
  p_resource_id text DEFAULT NULL,
  p_action text DEFAULT 'read',
  p_context jsonb DEFAULT '{}'
)
RETURNS boolean AS $$
DECLARE
  user_role text;
  user_tenant_id uuid;
  permission_granted boolean := false;
  validation_result boolean;
BEGIN
  -- Get user role and tenant
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM users WHERE id = p_user_id AND is_active = true;
  
  IF user_role IS NULL THEN
    RETURN false;
  END IF;
  
  -- Admin has all permissions
  IF user_role = 'admin' THEN
    permission_granted := true;
  ELSE
    -- Implement role-specific permission logic
    permission_granted := CASE
      WHEN user_role = 'school_manager' AND p_context->>'tenant_id' = user_tenant_id::text THEN true
      WHEN user_role = 'supervisor' AND p_context->>'tenant_id' = user_tenant_id::text THEN true
      WHEN user_role = 'driver' AND p_context->>'assigned_to' = p_user_id::text THEN true
      WHEN user_role = 'parent' AND p_context->>'parent_id' = p_user_id::text THEN true
      WHEN user_role = 'student' AND p_context->>'student_id' = p_user_id::text THEN true
      ELSE false
    END;
  END IF;
  
  -- Log permission validation
  INSERT INTO permission_validations (
    user_id,
    tenant_id,
    resource_type,
    resource_id,
    action,
    permission_granted,
    validation_context
  ) VALUES (
    p_user_id,
    user_tenant_id,
    p_resource_type,
    p_resource_id,
    p_action,
    permission_granted,
    p_context
  );
  
  RETURN permission_granted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS integer AS $$
DECLARE
  deleted_count integer;
BEGIN
  -- Mark expired sessions as inactive
  UPDATE secure_user_sessions 
  SET is_active = false, logout_reason = 'expired'
  WHERE (expires_at < now() OR last_activity < now() - interval '24 hours')
  AND is_active = true;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Clean up old inactive sessions (older than 30 days)
  DELETE FROM secure_user_sessions 
  WHERE is_active = false 
  AND created_at < now() - interval '30 days';
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to detect suspicious activity
CREATE OR REPLACE FUNCTION detect_suspicious_activity()
RETURNS TABLE(
  user_id uuid,
  activity_type text,
  risk_score integer,
  details jsonb
) AS $$
BEGIN
  RETURN QUERY
  -- Multiple failed permission attempts
  SELECT 
    pv.user_id,
    'multiple_permission_denials' as activity_type,
    80 as risk_score,
    jsonb_build_object(
      'denied_count', COUNT(*),
      'time_window', '1 hour',
      'resources', array_agg(DISTINCT pv.resource_type)
    ) as details
  FROM permission_validations pv
  WHERE pv.permission_granted = false
  AND pv.timestamp > now() - interval '1 hour'
  GROUP BY pv.user_id
  HAVING COUNT(*) > 10
  
  UNION ALL
  
  -- Multiple sessions from different IPs
  SELECT 
    sus.user_id,
    'multiple_ip_sessions' as activity_type,
    60 as risk_score,
    jsonb_build_object(
      'ip_count', COUNT(DISTINCT sus.ip_address),
      'ips', array_agg(DISTINCT sus.ip_address::text)
    ) as details
  FROM secure_user_sessions sus
  WHERE sus.is_active = true
  AND sus.created_at > now() - interval '1 hour'
  GROUP BY sus.user_id
  HAVING COUNT(DISTINCT sus.ip_address) > 3;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add data retention policies
CREATE OR REPLACE FUNCTION apply_data_retention_policies()
RETURNS integer AS $$
DECLARE
  total_deleted integer := 0;
  deleted_count integer;
BEGIN
  -- Clean up old audit logs (keep 1 year)
  DELETE FROM enhanced_audit_logs 
  WHERE timestamp < now() - interval '1 year';
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  total_deleted := total_deleted + deleted_count;
  
  -- Clean up old permission validations (keep 6 months)
  DELETE FROM permission_validations 
  WHERE timestamp < now() - interval '6 months';
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  total_deleted := total_deleted + deleted_count;
  
  -- Clean up old notifications (keep 3 months)
  DELETE FROM notifications 
  WHERE created_at < now() - interval '3 months'
  AND read = true;
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  total_deleted := total_deleted + deleted_count;
  
  RETURN total_deleted;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate security report
CREATE OR REPLACE FUNCTION generate_security_report(
  p_start_date timestamptz DEFAULT now() - interval '7 days',
  p_end_date timestamptz DEFAULT now()
)
RETURNS jsonb AS $$
DECLARE
  report jsonb;
BEGIN
  SELECT jsonb_build_object(
    'period', jsonb_build_object(
      'start_date', p_start_date,
      'end_date', p_end_date
    ),
    'audit_summary', (
      SELECT jsonb_build_object(
        'total_operations', COUNT(*),
        'by_operation', jsonb_object_agg(operation, operation_count),
        'by_table', jsonb_object_agg(table_name, table_count),
        'by_severity', jsonb_object_agg(severity, severity_count)
      )
      FROM (
        SELECT 
          operation,
          COUNT(*) as operation_count,
          table_name,
          COUNT(*) as table_count,
          severity,
          COUNT(*) as severity_count
        FROM enhanced_audit_logs 
        WHERE timestamp BETWEEN p_start_date AND p_end_date
        GROUP BY operation, table_name, severity
      ) audit_stats
    ),
    'permission_summary', (
      SELECT jsonb_build_object(
        'total_validations', COUNT(*),
        'denied_permissions', COUNT(*) FILTER (WHERE permission_granted = false),
        'denial_rate', ROUND(
          (COUNT(*) FILTER (WHERE permission_granted = false)::numeric / COUNT(*)) * 100, 2
        ),
        'top_denied_resources', (
          SELECT jsonb_agg(jsonb_build_object('resource', resource_type, 'count', denied_count))
          FROM (
            SELECT resource_type, COUNT(*) as denied_count
            FROM permission_validations
            WHERE permission_granted = false
            AND timestamp BETWEEN p_start_date AND p_end_date
            GROUP BY resource_type
            ORDER BY denied_count DESC
            LIMIT 5
          ) top_denied
        )
      )
      FROM permission_validations
      WHERE timestamp BETWEEN p_start_date AND p_end_date
    ),
    'session_summary', (
      SELECT jsonb_build_object(
        'active_sessions', COUNT(*) FILTER (WHERE is_active = true),
        'total_sessions', COUNT(*),
        'unique_users', COUNT(DISTINCT user_id),
        'unique_ips', COUNT(DISTINCT ip_address)
      )
      FROM secure_user_sessions
      WHERE created_at BETWEEN p_start_date AND p_end_date
    ),
    'suspicious_activity', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'user_id', user_id,
          'activity_type', activity_type,
          'risk_score', risk_score,
          'details', details
        )
      )
      FROM detect_suspicious_activity()
    )
  ) INTO report;
  
  RETURN report;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable RLS on new tables
ALTER TABLE enhanced_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_validations ENABLE ROW LEVEL SECURITY;
ALTER TABLE secure_user_sessions ENABLE ROW LEVEL SECURITY;

-- RLS policies for audit logs (admin only)
CREATE POLICY "admin_only_enhanced_audit_logs" ON enhanced_audit_logs
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- RLS policies for permission validations (admin and own records)
CREATE POLICY "admin_all_permission_validations" ON permission_validations
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "users_own_permission_validations" ON permission_validations
FOR SELECT USING (user_id = auth.uid());

-- RLS policies for sessions (admin and own sessions)
CREATE POLICY "admin_all_secure_sessions" ON secure_user_sessions
FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "users_own_secure_sessions" ON secure_user_sessions
FOR ALL USING (user_id = auth.uid());

COMMIT;
