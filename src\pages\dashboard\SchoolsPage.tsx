import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Building2,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Loader2,
  UserPlus,
} from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import { SchoolModal } from "../../components/schools/SchoolModal";
import { SchoolManagerAssignment } from "../../components/schools/SchoolManagerAssignment";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { UserRole } from "../../types";
import { usePermissions } from "../../hooks/usePermissions";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { PermissionGuard } from "../../components/auth/PermissionGuard";
import { Permission, ResourceType } from "../../lib/rbac";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

export const SchoolsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { tenant, tenants, loading, error, refreshData } = useDatabase();

  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState<
    Tables<"tenants"> | undefined
  >();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isManagerAssignmentOpen, setIsManagerAssignmentOpen] = useState(false);
  const [schoolForManagerAssignment, setSchoolForManagerAssignment] = useState<
    Tables<"tenants"> | undefined
  >();

  // Use enhanced RBAC-based data filtering
  const { filterDataByRole } = useRBACEnhancedSecurity();
  const schools = filterDataByRole(tenants, "school", "id");
  const filteredSchools = schools.filter((school) => {
    const matchesSearch =
      !searchQuery ||
      school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (school.contact_number &&
        school.contact_number
          .toLowerCase()
          .includes(searchQuery.toLowerCase()));

    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "active" && school.is_active) ||
      (filterStatus === "inactive" && !school.is_active);

    return matchesSearch && matchesStatus;
  });

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } =
    usePagination(filteredSchools.length, 10);

  const paginatedSchools = filteredSchools.slice(startIndex, endIndex);

  const handleOpenModal = (school?: Tables<"tenants">) => {
    setSelectedSchool(school);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedSchool(undefined);
    setIsModalOpen(false);
  };

  const handleOpenManagerAssignment = (school: Tables<"tenants">) => {
    setSchoolForManagerAssignment(school);
    setIsManagerAssignmentOpen(true);
  };

  const handleCloseManagerAssignment = () => {
    setSchoolForManagerAssignment(undefined);
    setIsManagerAssignmentOpen(false);
  };

  const handleManagerAssignmentSuccess = () => {
    refreshData();
    handleCloseManagerAssignment();
  };

  const handleSubmit = async (data: Partial<Tables<"tenants">>) => {
    try {
      if (selectedSchool) {
        // Update existing school
        const { error } = await supabase
          .from("tenants")
          .update(data)
          .eq("id", selectedSchool.id);

        if (error) throw error;
      } else {
        // Create new school
        const { error } = await supabase.from("tenants").insert([data]);

        if (error) throw error;
      }

      await refreshData();
    } catch (error) {
      console.error("Error saving school:", error);
      throw error;
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm(t("schools.deleteConfirmation"))) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase.from("tenants").delete().eq("id", id);

      if (error) throw error;

      await refreshData();
    } catch (error) {
      console.error("Error deleting school:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-error-600 dark:text-error-400">
          {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("nav.schools")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t("common.view")}, {t("common.add")}, {t("common.edit")}{" "}
                  {t("nav.schools").toLowerCase()}
                </p>
              </div>

              <PermissionGuard permission={Permission.SCHOOLS_CREATE}>
                <div className="mt-4 md:mt-0">
                  <Button
                    className="flex items-center gap-2"
                    onClick={() => handleOpenModal()}
                  >
                    <Plus size={16} />
                    Add School
                  </Button>
                </div>
              </PermissionGuard>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="relative max-w-xs w-full">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder={`${t("common.search")} ${t("nav.schools").toLowerCase()}`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Filter size={18} className="text-gray-400" />
                      </div>
                      <select
                        className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                        value={filterStatus}
                        onChange={(e) =>
                          setFilterStatus(
                            e.target.value as "all" | "active" | "inactive",
                          )
                        }
                      >
                        <option value="all">
                          {t("common.filter")}: {t("common.all")}
                        </option>
                        <option value="active">{t("buses.active")}</option>
                        <option value="inactive">{t("buses.inactive")}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                      >
                        School
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                      >
                        Contact
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                      >
                        {t("common.actions")}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {paginatedSchools.length > 0 ? (
                      paginatedSchools.map((school) => (
                        <tr
                          key={school.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                {school.logo_url ? (
                                  <img
                                    src={school.logo_url}
                                    alt={school.name}
                                    className="h-10 w-10 rounded-md object-cover"
                                  />
                                ) : (
                                  <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-md flex items-center justify-center text-primary-600 dark:text-primary-400">
                                    <Building2 size={20} />
                                  </div>
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {school.name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {school.address || "No address provided"}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {school.contact_number || "No contact"}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {school.domain || "No domain"}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                school.is_active
                                  ? "bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                              }`}
                            >
                              {school.is_active
                                ? t("buses.active")
                                : t("buses.inactive")}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <button
                                className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1"
                                title={t("common.view")}
                                onClick={() => handleOpenModal(school)}
                              >
                                <Eye size={18} />
                              </button>
                              <PermissionGuard
                                permission={Permission.USERS_ASSIGN_ROLES}
                              >
                                <button
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                                  title="Assign Manager"
                                  onClick={() =>
                                    handleOpenManagerAssignment(school)
                                  }
                                >
                                  <UserPlus size={18} />
                                </button>
                              </PermissionGuard>
                              <PermissionGuard
                                permission={Permission.SCHOOLS_UPDATE}
                              >
                                <button
                                  className="text-warning-600 hover:text-warning-900 dark:text-warning-400 dark:hover:text-warning-300 p-1"
                                  title={t("common.edit")}
                                  onClick={() => handleOpenModal(school)}
                                >
                                  <Edit size={18} />
                                </button>
                              </PermissionGuard>
                              <PermissionGuard
                                permission={Permission.SCHOOLS_DELETE}
                              >
                                <button
                                  className="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300 p-1"
                                  title={t("common.delete")}
                                  onClick={() => handleDelete(school.id)}
                                  disabled={isDeleting}
                                >
                                  <Trash2 size={18} />
                                </button>
                              </PermissionGuard>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={4}
                          className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                        >
                          {loading ? (
                            <div className="flex items-center justify-center">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Loading schools...
                            </div>
                          ) : searchQuery || filterStatus !== "all" ? (
                            t("common.noResultsFound")
                          ) : (
                            <div className="py-8">
                              <Building2
                                size={48}
                                className="mx-auto mb-4 text-gray-300"
                              />
                              <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                No schools found
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {user?.role === "admin"
                                  ? "Create your first school to get started"
                                  : "No school data available for your account"}
                              </p>
                            </div>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("common.showing")} {startIndex + 1} - {endIndex}{" "}
                      {t("common.of")} {filteredSchools.length}{" "}
                      {t("nav.schools").toLowerCase()}
                    </div>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={goToPage}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      <SchoolModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        school={selectedSchool}
      />

      {schoolForManagerAssignment && (
        <SchoolManagerAssignment
          tenant={schoolForManagerAssignment}
          onClose={handleCloseManagerAssignment}
          onSuccess={handleManagerAssignmentSuccess}
        />
      )}
    </div>
  );
};
