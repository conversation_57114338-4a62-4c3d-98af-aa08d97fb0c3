/**
 * RBAC Diagram Generator
 * Generates visual representations of role-permission relationships
 */

import { UserRole } from "../types";
import { Permission, DataScope } from "./rbac";
import {
  OPTIMIZED_ROLE_PERMISSIONS,
  OPTIMIZED_ROLE_DATA_SCOPE,
} from "./rbacOptimized";

export interface DiagramNode {
  id: string;
  label: string;
  type: "role" | "permission" | "scope";
  level: number;
  color: string;
  size: number;
}

export interface DiagramEdge {
  from: string;
  to: string;
  type: "has_permission" | "has_scope" | "inherits";
  weight: number;
  color: string;
}

export interface RBACDiagram {
  nodes: DiagramNode[];
  edges: DiagramEdge[];
  metadata: {
    totalRoles: number;
    totalPermissions: number;
    totalScopes: number;
    complexity: number;
  };
}

/**
 * RBAC Diagram Generator Class
 */
export class RBACDiagramGenerator {
  private static readonly ROLE_COLORS = {
    [UserRole.ADMIN]: "#dc2626", // Red - Highest risk
    [UserRole.SCHOOL_MANAGER]: "#ea580c", // Orange - High risk
    [UserRole.SUPERVISOR]: "#d97706", // Amber - Medium risk
    [UserRole.DRIVER]: "#16a34a", // Green - Low risk
    [UserRole.PARENT]: "#2563eb", // Blue - Low risk
    [UserRole.STUDENT]: "#7c3aed", // Purple - Minimal risk
  };

  private static readonly PERMISSION_COLORS = {
    SYSTEM_: "#dc2626", // Critical permissions
    DELETE: "#ea580c", // High risk permissions
    MANAGE_: "#d97706", // Medium risk permissions
    CREATE: "#16a34a", // Standard permissions
    VIEW_: "#2563eb", // Read permissions
    TRACK: "#7c3aed", // Monitoring permissions
  };

  /**
   * Generate complete RBAC diagram
   */
  static generateRBACDiagram(): RBACDiagram {
    const nodes: DiagramNode[] = [];
    const edges: DiagramEdge[] = [];

    // Generate role nodes
    const roleNodes = this.generateRoleNodes();
    nodes.push(...roleNodes);

    // Generate permission nodes
    const permissionNodes = this.generatePermissionNodes();
    nodes.push(...permissionNodes);

    // Generate scope nodes
    const scopeNodes = this.generateScopeNodes();
    nodes.push(...scopeNodes);

    // Generate role-permission edges
    const permissionEdges = this.generatePermissionEdges();
    edges.push(...permissionEdges);

    // Generate role-scope edges
    const scopeEdges = this.generateScopeEdges();
    edges.push(...scopeEdges);

    // Generate role hierarchy edges
    const hierarchyEdges = this.generateHierarchyEdges();
    edges.push(...hierarchyEdges);

    const metadata = {
      totalRoles: roleNodes.length,
      totalPermissions: permissionNodes.length,
      totalScopes: scopeNodes.length,
      complexity: this.calculateComplexity(nodes, edges),
    };

    return { nodes, edges, metadata };
  }

  /**
   * Generate role nodes
   */
  private static generateRoleNodes(): DiagramNode[] {
    return Object.values(UserRole).map((role, index) => {
      const permissions = OPTIMIZED_ROLE_PERMISSIONS[role] || [];
      const size = Math.max(30, Math.min(80, permissions.length * 2));

      return {
        id: `role_${role}`,
        label: this.formatRoleLabel(role),
        type: "role",
        level: this.getRoleLevel(role),
        color: this.ROLE_COLORS[role],
        size,
      };
    });
  }

  /**
   * Generate permission nodes
   */
  private static generatePermissionNodes(): DiagramNode[] {
    const allPermissions = new Set<Permission>();

    // Collect all unique permissions
    Object.values(OPTIMIZED_ROLE_PERMISSIONS).forEach((permissions) => {
      permissions.forEach((permission) => allPermissions.add(permission));
    });

    return Array.from(allPermissions).map((permission) => {
      const permissionStr = permission.toString();
      const color = this.getPermissionColor(permissionStr);
      const size = this.getPermissionSize(permission);

      return {
        id: `perm_${permissionStr}`,
        label: this.formatPermissionLabel(permissionStr),
        type: "permission",
        level: 2,
        color,
        size,
      };
    });
  }

  /**
   * Generate scope nodes
   */
  private static generateScopeNodes(): DiagramNode[] {
    const allScopes = new Set<DataScope>();

    // Collect all unique scopes
    Object.values(OPTIMIZED_ROLE_DATA_SCOPE).forEach((scopes) => {
      scopes.forEach((scope) => allScopes.add(scope));
    });

    return Array.from(allScopes).map((scope) => {
      const scopeStr = scope.toString();

      return {
        id: `scope_${scopeStr}`,
        label: this.formatScopeLabel(scopeStr),
        type: "scope",
        level: 3,
        color: this.getScopeColor(scope),
        size: 25,
      };
    });
  }

  /**
   * Generate role-permission edges
   */
  private static generatePermissionEdges(): DiagramEdge[] {
    const edges: DiagramEdge[] = [];

    Object.entries(OPTIMIZED_ROLE_PERMISSIONS).forEach(
      ([role, permissions]) => {
        permissions.forEach((permission) => {
          const permissionStr = permission.toString();
          const weight = this.getPermissionWeight(permission);

          edges.push({
            from: `role_${role}`,
            to: `perm_${permissionStr}`,
            type: "has_permission",
            weight,
            color: this.getEdgeColor(weight),
          });
        });
      },
    );

    return edges;
  }

  /**
   * Generate role-scope edges
   */
  private static generateScopeEdges(): DiagramEdge[] {
    const edges: DiagramEdge[] = [];

    Object.entries(OPTIMIZED_ROLE_DATA_SCOPE).forEach(([role, scopes]) => {
      scopes.forEach((scope) => {
        const scopeStr = scope.toString();

        edges.push({
          from: `role_${role}`,
          to: `scope_${scopeStr}`,
          type: "has_scope",
          weight: 1,
          color: "#6b7280",
        });
      });
    });

    return edges;
  }

  /**
   * Generate role hierarchy edges
   */
  private static generateHierarchyEdges(): DiagramEdge[] {
    const hierarchy = {
      [UserRole.ADMIN]: [UserRole.SCHOOL_MANAGER],
      [UserRole.SCHOOL_MANAGER]: [UserRole.SUPERVISOR],
      [UserRole.SUPERVISOR]: [UserRole.DRIVER],
      // Parent and Student are separate branches
    };

    const edges: DiagramEdge[] = [];

    Object.entries(hierarchy).forEach(([parent, children]) => {
      children.forEach((child) => {
        edges.push({
          from: `role_${parent}`,
          to: `role_${child}`,
          type: "inherits",
          weight: 3,
          color: "#374151",
        });
      });
    });

    return edges;
  }

  /**
   * Format role label for display
   */
  private static formatRoleLabel(role: UserRole): string {
    const labels = {
      [UserRole.ADMIN]: "System Admin",
      [UserRole.SCHOOL_MANAGER]: "School Manager",
      [UserRole.SUPERVISOR]: "Supervisor",
      [UserRole.DRIVER]: "Driver",
      [UserRole.PARENT]: "Parent",
      [UserRole.STUDENT]: "Student",
    };

    return labels[role] || role;
  }

  /**
   * Format permission label for display
   */
  private static formatPermissionLabel(permission: string): string {
    return permission
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  }

  /**
   * Format scope label for display
   */
  private static formatScopeLabel(scope: string): string {
    const labels = {
      global: "Global Access",
      tenant: "School Level",
      personal: "Personal Data",
      assigned: "Assigned Resources",
      children: "Children Data",
    };

    return labels[scope] || scope;
  }

  /**
   * Get role hierarchy level
   */
  private static getRoleLevel(role: UserRole): number {
    const levels = {
      [UserRole.ADMIN]: 1,
      [UserRole.SCHOOL_MANAGER]: 2,
      [UserRole.SUPERVISOR]: 3,
      [UserRole.DRIVER]: 4,
      [UserRole.PARENT]: 4,
      [UserRole.STUDENT]: 5,
    };

    return levels[role] || 0;
  }

  /**
   * Get permission color based on risk level
   */
  private static getPermissionColor(permission: string): string {
    for (const [pattern, color] of Object.entries(this.PERMISSION_COLORS)) {
      if (permission.includes(pattern)) {
        return color;
      }
    }
    return "#6b7280"; // Default gray
  }

  /**
   * Get permission size based on usage
   */
  private static getPermissionSize(permission: Permission): number {
    // Count how many roles have this permission
    let usageCount = 0;
    Object.values(OPTIMIZED_ROLE_PERMISSIONS).forEach((permissions) => {
      if (permissions.includes(permission)) {
        usageCount++;
      }
    });

    return Math.max(15, Math.min(40, usageCount * 8));
  }

  /**
   * Get scope color
   */
  private static getScopeColor(scope: DataScope): string {
    const colors = {
      [DataScope.GLOBAL]: "#dc2626",
      [DataScope.TENANT]: "#ea580c",
      [DataScope.ASSIGNED]: "#d97706",
      [DataScope.CHILDREN]: "#16a34a",
      [DataScope.PERSONAL]: "#2563eb",
    };

    return colors[scope] || "#6b7280";
  }

  /**
   * Get permission weight for edge thickness
   */
  private static getPermissionWeight(permission: Permission): number {
    const permissionStr = permission.toString();

    if (permissionStr.includes("SYSTEM_")) return 5;
    if (permissionStr.includes("DELETE")) return 4;
    if (permissionStr.includes("MANAGE_")) return 3;
    if (permissionStr.includes("CREATE")) return 2;
    return 1;
  }

  /**
   * Get edge color based on weight
   */
  private static getEdgeColor(weight: number): string {
    const colors = {
      5: "#dc2626", // Critical
      4: "#ea580c", // High
      3: "#d97706", // Medium
      2: "#16a34a", // Standard
      1: "#6b7280", // Low
    };

    return colors[weight] || "#6b7280";
  }

  /**
   * Calculate diagram complexity
   */
  private static calculateComplexity(
    nodes: DiagramNode[],
    edges: DiagramEdge[],
  ): number {
    // Simple complexity metric based on nodes and edges
    const nodeComplexity = nodes.length;
    const edgeComplexity = edges.length;
    const density = edges.length / (nodes.length * (nodes.length - 1));

    return Math.round((nodeComplexity + edgeComplexity) * density * 100);
  }

  /**
   * Generate Mermaid diagram syntax
   */
  static generateMermaidDiagram(): string {
    const diagram = this.generateRBACDiagram();

    let mermaid = "graph TD\n";

    // Add role nodes
    diagram.nodes
      .filter((node) => node.type === "role")
      .forEach((node) => {
        mermaid += `  ${node.id}["${node.label}"]\n`;
      });

    // Add hierarchy edges
    diagram.edges
      .filter((edge) => edge.type === "inherits")
      .forEach((edge) => {
        mermaid += `  ${edge.from} --> ${edge.to}\n`;
      });

    // Add styling
    mermaid += "\n  classDef admin fill:#dc2626,stroke:#991b1b,color:#fff\n";
    mermaid += "  classDef manager fill:#ea580c,stroke:#c2410c,color:#fff\n";
    mermaid += "  classDef supervisor fill:#d97706,stroke:#a16207,color:#fff\n";
    mermaid += "  classDef driver fill:#16a34a,stroke:#15803d,color:#fff\n";
    mermaid += "  classDef parent fill:#2563eb,stroke:#1d4ed8,color:#fff\n";
    mermaid += "  classDef student fill:#7c3aed,stroke:#6d28d9,color:#fff\n";

    // Apply classes
    mermaid += `\n  class role_${UserRole.ADMIN} admin\n`;
    mermaid += `  class role_${UserRole.SCHOOL_MANAGER} manager\n`;
    mermaid += `  class role_${UserRole.SUPERVISOR} supervisor\n`;
    mermaid += `  class role_${UserRole.DRIVER} driver\n`;
    mermaid += `  class role_${UserRole.PARENT} parent\n`;
    mermaid += `  class role_${UserRole.STUDENT} student\n`;

    return mermaid;
  }

  /**
   * Generate DOT (Graphviz) diagram
   */
  static generateDotDiagram(): string {
    const diagram = this.generateRBACDiagram();

    let dot = "digraph RBAC {\n";
    dot += "  rankdir=TB;\n";
    dot += "  node [shape=box, style=filled];\n\n";

    // Add nodes
    diagram.nodes.forEach((node) => {
      const color = node.color.replace("#", "");
      dot += `  "${node.id}" [label="${node.label}", fillcolor="#${color}"];\n`;
    });

    dot += "\n";

    // Add edges
    diagram.edges.forEach((edge) => {
      const style = edge.type === "inherits" ? "solid" : "dashed";
      dot += `  "${edge.from}" -> "${edge.to}" [style=${style}];\n`;
    });

    dot += "}\n";

    return dot;
  }

  /**
   * Export diagram data as JSON
   */
  static exportDiagramData(): string {
    const diagram = this.generateRBACDiagram();
    return JSON.stringify(diagram, null, 2);
  }
}

export default RBACDiagramGenerator;
