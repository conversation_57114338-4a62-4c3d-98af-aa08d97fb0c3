/**
 * Enhanced RBAC Hook with Comprehensive Security Controls
 * Addresses critical audit findings and provides centralized permission management
 */

import { useMemo, useCallback, useRef } from "react";
import { useAuth } from "../contexts/AuthContext";
import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "../lib/rbac";
import {
  ENHANCED_ROUTE_PERMISSIONS,
  ENHANCED_COMPONENT_PERMISSIONS,
  ENHANCED_DATA_ACCESS_PATTERNS,
  ENHANCED_ACTION_PERMISSION_MATRIX,
  SECURITY_POLICIES,
  EN<PERSON>NCED_PERMISSION_ERROR_MESSAGES,
  RoutePermissionConfig,
  ComponentPermissionConfig,
} from "../lib/rbacCentralizedConfigEnhanced";
import { usePermissions } from "./usePermissions";

export interface EnhancedRBACResult {
  allowed: boolean;
  error?: string;
  fallbackRoute?: string;
  securityLevel?: "low" | "medium" | "high" | "critical";
  auditRequired?: boolean;
  details?: any;
}

export interface SecurityContext {
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp: Date;
  riskScore?: number;
}

export interface EnhancedRBACHook {
  // Route-level permissions with enhanced security
  canAccessRoute: (
    route: string,
    context?: SecurityContext,
  ) => EnhancedRBACResult;

  // Component-level permissions using centralized config
  canUseComponent: (
    componentKey: string,
    context?: SecurityContext,
  ) => EnhancedRBACResult;

  // Action-based permissions with security metadata
  canPerformAction: (
    resource: ResourceType,
    action: Action,
    context?: any,
  ) => EnhancedRBACResult;

  // Enhanced data filtering with security controls
  filterDataByRole: <T extends { tenant_id?: string; id?: string }>(
    data: T[],
    resourceType: ResourceType,
    ownerField?: keyof T,
    securityLevel?: "strict" | "normal",
  ) => T[];

  // Security-aware navigation helpers
  getAccessibleRoutes: (
    securityLevel?: "low" | "medium" | "high" | "critical",
  ) => string[];
  getSecureNavigationItems: () => Array<{
    key: string;
    route: string;
    accessible: boolean;
    securityLevel: string;
    auditRequired: boolean;
  }>;

  // Enhanced permission checking with audit trail
  hasPermissionSecure: (
    permission: Permission,
    auditAccess?: boolean,
  ) => boolean;
  hasAllPermissionsSecure: (
    permissions: Permission[],
    auditAccess?: boolean,
  ) => boolean;
  hasAnyPermissionSecure: (
    permissions: Permission[],
    auditAccess?: boolean,
  ) => boolean;

  // Security policy enforcement
  checkSecurityPolicy: (action?: string) => {
    allowed: boolean;
    reason?: string;
    remainingTime?: number;
  };

  // Rate limiting
  checkRateLimit: (
    action: string,
    customLimit?: { maxRequests: number; windowMinutes: number },
  ) => {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  };

  // Risk assessment
  calculateRiskScore: (action: string, context?: SecurityContext) => number;

  // Audit logging
  logSecurityEvent: (event: string, details?: any) => void;

  // Current user context with security metadata
  currentUser: any;
  userRole: UserRole | null;
  securityLevel: "low" | "medium" | "high" | "critical";
  sessionValid: boolean;
  mfaRequired: boolean;
}

/**
 * Enhanced RBAC Hook Implementation
 */
export const useRBACEnhancedSecurity = (): EnhancedRBACHook => {
  const { user } = useAuth();
  const permissions = usePermissions();
  const auditLogRef = useRef<any[]>([]);
  const rateLimitRef = useRef<
    Map<string, { count: number; resetTime: number }>
  >(new Map());

  const userRole = user?.role as UserRole | null;
  const securityPolicy = userRole ? SECURITY_POLICIES[userRole] : null;

  // Calculate user's security level based on role and permissions
  const securityLevel = useMemo(() => {
    if (!userRole) return "low";

    switch (userRole) {
      case UserRole.ADMIN:
        return "critical";
      case UserRole.SCHOOL_MANAGER:
        return "high";
      case UserRole.SUPERVISOR:
        return "medium";
      default:
        return "low";
    }
  }, [userRole]);

  // Check if session is valid based on security policy
  const sessionValid = useMemo(() => {
    if (!user || !securityPolicy) return false;

    // Check session timeout
    const lastActivity = user.metadata?.lastActivity;
    if (lastActivity) {
      const sessionAge = Date.now() - new Date(lastActivity).getTime();
      const maxAge = securityPolicy.sessionTimeout * 60 * 1000;
      return sessionAge < maxAge;
    }

    return true;
  }, [user, securityPolicy]);

  // Check if MFA is required
  const mfaRequired = useMemo(() => {
    return securityPolicy?.requireMFA || false;
  }, [securityPolicy]);

  // Enhanced route access checking with security controls
  const canAccessRoute = useCallback(
    (route: string, context?: SecurityContext): EnhancedRBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          fallbackRoute: "/login",
          securityLevel: "critical",
        };
      }

      if (!sessionValid) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          fallbackRoute: "/login",
          securityLevel: "critical",
        };
      }

      const routeConfig = ENHANCED_ROUTE_PERMISSIONS[route];
      if (!routeConfig) {
        // Route not configured - deny access for security
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.ROUTE_ACCESS_DENIED,
          securityLevel: "medium",
        };
      }

      // Check rate limiting if configured
      if (routeConfig.rateLimit) {
        const rateLimitResult = checkRateLimit(
          `route:${route}`,
          routeConfig.rateLimit,
        );
        if (!rateLimitResult.allowed) {
          return {
            allowed: false,
            error: ENHANCED_PERMISSION_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
            securityLevel: "high",
          };
        }
      }

      // Check permissions
      if (routeConfig.permissions.length > 0) {
        const hasPermission = routeConfig.requireAll
          ? routeConfig.permissions.every((p) => permissions.hasPermission(p))
          : routeConfig.permissions.some((p) => permissions.hasPermission(p));

        if (!hasPermission) {
          logSecurityEvent("route_access_denied", {
            route,
            userRole,
            requiredPermissions: routeConfig.permissions,
            context,
          });

          return {
            allowed: false,
            error: ENHANCED_PERMISSION_ERROR_MESSAGES.ROUTE_ACCESS_DENIED,
            fallbackRoute: routeConfig.fallbackRoute || "/dashboard",
            securityLevel: routeConfig.auditLevel as any,
            auditRequired: true,
          };
        }
      }

      // Log successful access for high-security routes
      if (
        routeConfig.auditLevel === "high" ||
        routeConfig.auditLevel === "critical"
      ) {
        logSecurityEvent("route_access_granted", {
          route,
          userRole,
          context,
        });
      }

      return {
        allowed: true,
        securityLevel: routeConfig.auditLevel as any,
        auditRequired: routeConfig.auditLevel === "critical",
      };
    },
    [user, userRole, sessionValid, permissions],
  );

  // Enhanced component access checking
  const canUseComponent = useCallback(
    (componentKey: string, context?: SecurityContext): EnhancedRBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          securityLevel: "critical",
        };
      }

      const componentConfig = ENHANCED_COMPONENT_PERMISSIONS[componentKey];
      if (!componentConfig) {
        // Component not configured - allow access for backward compatibility
        return { allowed: true, securityLevel: "low" };
      }

      // Check role restrictions
      if (componentConfig.roles && !componentConfig.roles.includes(userRole)) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
          securityLevel: componentConfig.securityLevel,
        };
      }

      // Check rate limiting if configured
      if (componentConfig.rateLimit) {
        const rateLimitResult = checkRateLimit(
          `component:${componentKey}`,
          componentConfig.rateLimit,
        );
        if (!rateLimitResult.allowed) {
          return {
            allowed: false,
            error: ENHANCED_PERMISSION_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
            securityLevel: "high",
          };
        }
      }

      // Check permissions
      const hasPermission = componentConfig.permissions.some((p) =>
        permissions.hasPermission(p),
      );

      if (!hasPermission) {
        if (componentConfig.auditRequired) {
          logSecurityEvent("component_access_denied", {
            componentKey,
            userRole,
            requiredPermissions: componentConfig.permissions,
            context,
          });
        }

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED,
          securityLevel: componentConfig.securityLevel,
          auditRequired: componentConfig.auditRequired,
        };
      }

      return {
        allowed: true,
        securityLevel: componentConfig.securityLevel,
        auditRequired: componentConfig.auditRequired,
      };
    },
    [user, userRole, permissions],
  );

  // Enhanced action-based permission checking
  const canPerformAction = useCallback(
    (
      resource: ResourceType,
      action: Action,
      context?: any,
    ): EnhancedRBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          securityLevel: "critical",
        };
      }

      const actionConfig =
        ENHANCED_ACTION_PERMISSION_MATRIX[resource]?.[action];
      if (!actionConfig) {
        // Action not configured - deny access for security
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED,
          securityLevel: "medium",
        };
      }

      // Check rate limiting if configured
      if (actionConfig.rateLimit) {
        const rateLimitResult = checkRateLimit(
          `action:${resource}:${action}`,
          actionConfig.rateLimit,
        );
        if (!rateLimitResult.allowed) {
          return {
            allowed: false,
            error: ENHANCED_PERMISSION_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
            securityLevel: "high",
          };
        }
      }

      // Check MFA requirement
      if (actionConfig.requiresMFA && !checkMFACompliance()) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.MFA_REQUIRED,
          securityLevel: "critical",
        };
      }

      // Check IP restrictions
      if (
        actionConfig.ipRestrictions &&
        !checkIPRestrictions(context?.ipAddress)
      ) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.IP_RESTRICTED,
          securityLevel: "critical",
        };
      }

      // Check permissions
      const hasPermission = actionConfig.permissions.some((p) =>
        permissions.hasPermission(p),
      );

      if (!hasPermission) {
        if (actionConfig.auditRequired) {
          logSecurityEvent("action_access_denied", {
            resource,
            action,
            userRole,
            requiredPermissions: actionConfig.permissions,
            context,
          });
        }

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
          securityLevel: actionConfig.securityLevel,
          auditRequired: actionConfig.auditRequired,
        };
      }

      // Additional context-based checks
      if (context) {
        const contextResult = permissions.checkResourceAccess(
          resource,
          action,
          context,
        );
        if (!contextResult.allowed) {
          return {
            allowed: false,
            error:
              contextResult.error ||
              ENHANCED_PERMISSION_ERROR_MESSAGES.DATA_ACCESS_DENIED,
            securityLevel: actionConfig.securityLevel,
            details: contextResult.details,
          };
        }
      }

      return {
        allowed: true,
        securityLevel: actionConfig.securityLevel,
        auditRequired: actionConfig.auditRequired,
      };
    },
    [user, userRole, permissions],
  );

  // Enhanced data filtering with security controls
  const filterDataByRole = useCallback(
    <T extends { tenant_id?: string; id?: string }>(
      data: T[],
      resourceType: ResourceType,
      ownerField?: keyof T,
      securityLevel: "strict" | "normal" = "normal",
    ): T[] => {
      if (!user || !userRole) {
        return [];
      }

      const dataAccessPattern = ENHANCED_DATA_ACCESS_PATTERNS[userRole];
      if (!dataAccessPattern) {
        return [];
      }

      // Log data access for high-security patterns
      if (
        dataAccessPattern.auditLevel === "comprehensive" ||
        dataAccessPattern.auditLevel === "detailed"
      ) {
        logSecurityEvent("data_access", {
          resourceType,
          dataCount: data.length,
          userRole,
          securityLevel,
        });
      }

      return permissions.filterDataByPermissions(
        data,
        resourceType,
        ownerField,
      );
    },
    [user, userRole, permissions],
  );

  // Get accessible routes with security filtering
  const getAccessibleRoutes = useCallback(
    (securityLevel?: "low" | "medium" | "high" | "critical"): string[] => {
      if (!user || !userRole) {
        return [];
      }

      return Object.keys(ENHANCED_ROUTE_PERMISSIONS).filter((route) => {
        const routeConfig = ENHANCED_ROUTE_PERMISSIONS[route];

        // Filter by security level if specified
        if (securityLevel && routeConfig.auditLevel !== securityLevel) {
          return false;
        }

        const result = canAccessRoute(route);
        return result.allowed;
      });
    },
    [user, userRole, canAccessRoute],
  );

  // Get navigation items with security metadata
  const getSecureNavigationItems = useCallback(() => {
    const items = [];

    for (const [route, config] of Object.entries(ENHANCED_ROUTE_PERMISSIONS)) {
      const result = canAccessRoute(route);
      items.push({
        key: route,
        route,
        accessible: result.allowed,
        securityLevel: config.auditLevel || "low",
        auditRequired: config.auditLevel === "critical",
      });
    }

    return items;
  }, [canAccessRoute]);

  // Enhanced permission checking with audit trail
  const hasPermissionSecure = useCallback(
    (permission: Permission, auditAccess: boolean = false): boolean => {
      const result = permissions.hasPermission(permission);

      if (auditAccess) {
        logSecurityEvent("permission_check", {
          permission,
          result,
          userRole,
        });
      }

      return result;
    },
    [permissions, userRole],
  );

  const hasAllPermissionsSecure = useCallback(
    (permissionList: Permission[], auditAccess: boolean = false): boolean => {
      const result = permissionList.every((p) => permissions.hasPermission(p));

      if (auditAccess) {
        logSecurityEvent("permissions_check_all", {
          permissions: permissionList,
          result,
          userRole,
        });
      }

      return result;
    },
    [permissions, userRole],
  );

  const hasAnyPermissionSecure = useCallback(
    (permissionList: Permission[], auditAccess: boolean = false): boolean => {
      const result = permissionList.some((p) => permissions.hasPermission(p));

      if (auditAccess) {
        logSecurityEvent("permissions_check_any", {
          permissions: permissionList,
          result,
          userRole,
        });
      }

      return result;
    },
    [permissions, userRole],
  );

  // Security policy enforcement
  const checkSecurityPolicy = useCallback(
    (action?: string) => {
      if (!securityPolicy) {
        return { allowed: true };
      }

      // Check time restrictions
      if (securityPolicy.allowedTimeWindows) {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentDay = now.getDay();
        const currentTime = currentHour * 60 + currentMinute;

        const isAllowedTime = securityPolicy.allowedTimeWindows.some(
          (window) => {
            if (!window.days.includes(currentDay)) {
              return false;
            }

            const [startHour, startMinute] = window.start
              .split(":")
              .map(Number);
            const [endHour, endMinute] = window.end.split(":").map(Number);
            const startTime = startHour * 60 + startMinute;
            const endTime = endHour * 60 + endMinute;

            return currentTime >= startTime && currentTime <= endTime;
          },
        );

        if (!isAllowedTime) {
          return {
            allowed: false,
            reason: ENHANCED_PERMISSION_ERROR_MESSAGES.TIME_RESTRICTED,
          };
        }
      }

      return { allowed: true };
    },
    [securityPolicy],
  );

  // Rate limiting implementation
  const checkRateLimit = useCallback(
    (
      action: string,
      customLimit?: { maxRequests: number; windowMinutes: number },
    ) => {
      const limit = customLimit || { maxRequests: 10, windowMinutes: 1 };
      const key = `${user?.id}:${action}`;
      const now = Date.now();
      const windowMs = limit.windowMinutes * 60 * 1000;

      const current = rateLimitRef.current.get(key);

      if (!current || now - current.resetTime > windowMs) {
        rateLimitRef.current.set(key, {
          count: 1,
          resetTime: now,
        });
        return {
          allowed: true,
          remaining: limit.maxRequests - 1,
          resetTime: new Date(now + windowMs),
        };
      }

      if (current.count >= limit.maxRequests) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: new Date(current.resetTime + windowMs),
        };
      }

      current.count++;
      return {
        allowed: true,
        remaining: limit.maxRequests - current.count,
        resetTime: new Date(current.resetTime + windowMs),
      };
    },
    [user],
  );

  // Risk assessment calculation
  const calculateRiskScore = useCallback(
    (action: string, context?: SecurityContext): number => {
      let riskScore = 0;

      // Base risk by user role
      switch (userRole) {
        case UserRole.ADMIN:
          riskScore += 50;
          break;
        case UserRole.SCHOOL_MANAGER:
          riskScore += 30;
          break;
        case UserRole.SUPERVISOR:
          riskScore += 20;
          break;
        default:
          riskScore += 10;
      }

      // Risk by action type
      if (action.includes("delete") || action.includes("remove")) {
        riskScore += 30;
      } else if (action.includes("create") || action.includes("update")) {
        riskScore += 20;
      } else if (action.includes("export") || action.includes("download")) {
        riskScore += 15;
      }

      // Context-based risk factors
      if (context) {
        if (context.riskScore) {
          riskScore += context.riskScore;
        }
      }

      return Math.min(100, riskScore);
    },
    [userRole],
  );

  // Security event logging
  const logSecurityEvent = useCallback(
    (event: string, details?: any) => {
      const logEntry = {
        timestamp: new Date().toISOString(),
        event,
        userId: user?.id,
        userRole,
        details,
        sessionId: user?.metadata?.sessionId,
      };

      auditLogRef.current.push(logEntry);

      // Keep only last 1000 entries
      if (auditLogRef.current.length > 1000) {
        auditLogRef.current.shift();
      }

      // Log to console (in production, send to audit service)
      console.log("🔒 Security Event:", logEntry);
    },
    [user, userRole],
  );

  // Helper functions
  const checkMFACompliance = (): boolean => {
    // Implementation would check if user has completed MFA
    return !mfaRequired || user?.metadata?.mfaCompleted === true;
  };

  const checkIPRestrictions = (ipAddress?: string): boolean => {
    // Implementation would check against allowed IP ranges
    return true; // Simplified for demo
  };

  return useMemo(
    () => ({
      canAccessRoute,
      canUseComponent,
      canPerformAction,
      filterDataByRole,
      getAccessibleRoutes,
      getSecureNavigationItems,
      hasPermissionSecure,
      hasAllPermissionsSecure,
      hasAnyPermissionSecure,
      checkSecurityPolicy,
      checkRateLimit,
      calculateRiskScore,
      logSecurityEvent,
      currentUser: user,
      userRole,
      securityLevel,
      sessionValid,
      mfaRequired,
    }),
    [
      canAccessRoute,
      canUseComponent,
      canPerformAction,
      filterDataByRole,
      getAccessibleRoutes,
      getSecureNavigationItems,
      hasPermissionSecure,
      hasAllPermissionsSecure,
      hasAnyPermissionSecure,
      checkSecurityPolicy,
      checkRateLimit,
      calculateRiskScore,
      logSecurityEvent,
      user,
      userRole,
      securityLevel,
      sessionValid,
      mfaRequired,
    ],
  );
};

export default useRBACEnhancedSecurity;
