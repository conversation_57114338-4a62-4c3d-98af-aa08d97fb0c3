-- Create a function to get all users for admin access
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS SETOF "users" AS $$
BEGIN
  RETURN QUERY SELECT * FROM "users" ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission on the function
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;

-- Ensure RLS is enabled on users table
ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;

-- Create policy for admin users to see all users
DROP POLICY IF EXISTS "Admin users can see all users" ON "users";
CREATE POLICY "Admin users can see all users"
  ON "users"
  FOR SELECT
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'admin'
  );

-- Create policy for non-admin users to see only users in their tenant
DROP POLICY IF EXISTS "Users can see users in their tenant" ON "users";
CREATE POLICY "Users can see users in their tenant"
  ON "users"
  FOR SELECT
  USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );
