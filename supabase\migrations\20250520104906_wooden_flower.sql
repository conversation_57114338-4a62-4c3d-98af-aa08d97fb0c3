/*
  # Create Additional Admin User

  1. Data Setup
    - Create admin user in auth.users
    - Create admin user profile in public.users
    - Link user to existing tenant
*/

-- Create admin user in auth.users
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  role,
  aud,
  confirmation_token
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('Admin123!@#', gen_salt('bf')),
  now(),
  '{"provider":"email","providers":["email"]}',
  '{"name":"Moataz Admin"}',
  now(),
  now(),
  'authenticated',
  'authenticated',
  ''
) ON CONFLICT DO NOTHING;

-- Create admin user profile
INSERT INTO public.users (
  id,
  tenant_id,
  role,
  name,
  email,
  is_active
)
SELECT 
  au.id,
  t.id as tenant_id,
  'admin'::user_role,
  'Moataz Admin',
  '<EMAIL>',
  true
FROM 
  auth.users au
  CROSS JOIN public.tenants t
WHERE 
  au.email = '<EMAIL>'
ON CONFLICT DO NOTHING;