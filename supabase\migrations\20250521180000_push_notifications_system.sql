-- Push Subscriptions Table
CREATE TABLE IF NOT EXISTS push_subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL,
  p256dh_key TEXT NOT NULL,
  auth_key TEXT NOT NULL,
  user_agent TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, endpoint)
);

-- Notification Templates Table
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('geofence', 'attendance', 'maintenance', 'announcements')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  variables TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification Actions Table
CREATE TABLE IF NOT EXISTS notification_actions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  action_data JSONB,
  result TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification Acknowledgments Table
CREATE TABLE IF NOT EXISTS notification_acknowledgments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  acknowledged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT
);

-- Notification Groups Table
CREATE TABLE IF NOT EXISTS notification_groups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  group_key TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_count INTEGER DEFAULT 1,
  last_notification_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, group_key)
);

-- Notification Analytics Table
CREATE TABLE IF NOT EXISTS notification_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL CHECK (event_type IN ('push_sent', 'notification_opened', 'notification_clicked', 'notification_dismissed')),
  user_ids UUID[],
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  notification_data JSONB,
  subscription_count INTEGER,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CRETE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_tenant_id ON push_subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON push_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_templates_tenant_id ON notification_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_actions_notification_id ON notification_actions(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_actions_user_id ON notification_actions(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_notification_id ON notification_acknowledgments(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_groups_tenant_id ON notification_groups(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_groups_group_key ON notification_groups(group_key);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_tenant_id ON notification_analytics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_type ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_timestamp ON notification_analytics(timestamp);

-- Add type and priority columns to notifications table
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS type TEXT DEFAULT 'general';
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high'));
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Enable realtime for push subscriptions
alter publication supabase_realtime add table push_subscriptions;
alter publication supabase_realtime add table notification_templates;
alter publication supabase_realtime add table notification_actions;
alter publication supabase_realtime add table notification_acknowledgments;
alter publication supabase_realtime add table notification_groups;
alter publication supabase_realtime add table notification_analytics;

-- Create function to get notification engagement metrics
CREATE OR REPLACE FUNCTION get_notification_engagement_metrics(tenant_id UUID)
RETURNS TABLE (
  total_sent BIGINT,
  total_opened BIGINT,
  total_clicked BIGINT,
  total_dismissed BIGINT,
  open_rate NUMERIC,
  click_rate NUMERIC,
  dismiss_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  WITH metrics AS (
    SELECT 
      COUNT(*) FILTER (WHERE event_type = 'push_sent') as sent,
      COUNT(*) FILTER (WHERE event_type = 'notification_opened') as opened,
      COUNT(*) FILTER (WHERE event_type = 'notification_clicked') as clicked,
      COUNT(*) FILTER (WHERE event_type = 'notification_dismissed') as dismissed
    FROM notification_analytics 
    WHERE notification_analytics.tenant_id = get_notification_engagement_metrics.tenant_id
  )
  SELECT 
    sent,
    opened,
    clicked,
    dismissed,
    CASE WHEN sent > 0 THEN ROUND((opened::NUMERIC / sent::NUMERIC) * 100, 2) ELSE 0 END,
    CASE WHEN sent > 0 THEN ROUND((clicked::NUMERIC / sent::NUMERIC) * 100, 2) ELSE 0 END,
    CASE WHEN sent > 0 THEN ROUND((dismissed::NUMERIC / sent::NUMERIC) * 100, 2) ELSE 0 END
  FROM metrics;
END;
$$ LANGUAGE plpgsql;
