import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import "./i18n";
import { ThemeProvider } from "./contexts/ThemeContext";
import { AuthProvider } from "./contexts/AuthContext";
import DatabaseProvider from "./contexts/DatabaseContext";
import NotificationsProvider from "./contexts/NotificationsContext";
import { TempoDevtools } from "tempo-devtools";
import App from "./App";
import "./index.css";
import { ErrorHandler } from "./utils/errorBoundary";

// Initialize error handling
try {
  // Initialize Tempo Devtools
  TempoDevtools.init();
} catch (error) {
  ErrorHandler.handleError(error as Error, "TempoDevtools");
}

// Register service worker for push notifications
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .then((registration) => {
        console.log("SW registered: ", registration);
      })
      .catch((registrationError) => {
        console.log("SW registration failed: ", registrationError);
      });
  });
}

// Create a root element and render the app
const rootElement = document.getElementById("root");
if (rootElement) {
  try {
    const root = createRoot(rootElement);
    root.render(
      <StrictMode>
        <BrowserRouter>
          <ThemeProvider>
            <AuthProvider>
              <DatabaseProvider>
                <NotificationsProvider>
                  <App />
                </NotificationsProvider>
              </DatabaseProvider>
            </AuthProvider>
          </ThemeProvider>
        </BrowserRouter>
      </StrictMode>,
    );
  } catch (error) {
    ErrorHandler.handleError(error as Error, "ReactRender");

    // Fallback rendering
    rootElement.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 2rem;">
          <h1 style="color: #dc2626; margin-bottom: 1rem;">Application Error</h1>
          <p style="color: #6b7280; margin-bottom: 1rem;">Something went wrong. Please refresh the page.</p>
          <button onclick="window.location.reload()" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.25rem; cursor: pointer;">
            Refresh Page
          </button>
        </div>
      </div>
    `;
  }
} else {
  console.error("Root element not found");
  ErrorHandler.handleError(new Error("Root element not found"), "DOM");
}
