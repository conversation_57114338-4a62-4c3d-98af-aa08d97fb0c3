-- Add tracking and maintenance features

-- Create bus_locations table for real-time tracking
CREATE TABLE IF NOT EXISTS bus_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    speed DECIMAL(5, 2) DEFAULT 0,
    heading INTEGER DEFAULT 0,
    accuracy DECIMAL(5, 2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create geofences table
CREATE TABLE IF NOT EXISTS geofences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    center_lat DECIMAL(10, 8) NOT NULL,
    center_lng DECIMAL(11, 8) NOT NULL,
    radius INTEGER NOT NULL, -- in meters
    is_active BOOLEAN DEFAULT true,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create geofence_alerts table
CREATE TABLE IF NOT EXISTS geofence_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    geofence_id UUID NOT NULL REFERENCES geofences(id) ON DELETE CASCADE,
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('entry', 'exit')),
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_acknowledged BOOLEAN DEFAULT false,
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bus_maintenance table
CREATE TABLE IF NOT EXISTS bus_maintenance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL,
    description TEXT,
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    cost DECIMAL(10, 2),
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create route_stops table for detailed route management
CREATE TABLE IF NOT EXISTS route_stops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id UUID NOT NULL REFERENCES routes(id) ON DELETE CASCADE,
    stop_name VARCHAR(255) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    stop_order INTEGER NOT NULL,
    estimated_arrival_time TIME,
    is_pickup BOOLEAN DEFAULT true,
    is_dropoff BOOLEAN DEFAULT true,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bus_tracking_history table for historical data
CREATE TABLE IF NOT EXISTS bus_tracking_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    route_id UUID REFERENCES routes(id),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    total_distance DECIMAL(8, 2), -- in kilometers
    average_speed DECIMAL(5, 2), -- in km/h
    max_speed DECIMAL(5, 2), -- in km/h
    fuel_consumed DECIMAL(6, 2), -- in liters
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create emergency_alerts table
CREATE TABLE IF NOT EXISTS emergency_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES users(id),
    alert_type VARCHAR(100) NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vehicle_inspections table
CREATE TABLE IF NOT EXISTS vehicle_inspections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bus_id UUID NOT NULL REFERENCES buses(id) ON DELETE CASCADE,
    inspector_id UUID NOT NULL REFERENCES users(id),
    inspection_date DATE NOT NULL,
    inspection_type VARCHAR(100) NOT NULL,
    overall_status VARCHAR(50) DEFAULT 'pending' CHECK (overall_status IN ('pending', 'passed', 'failed', 'needs_attention')),
    notes TEXT,
    checklist_data JSONB, -- Store inspection checklist results
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bus_locations_bus_id ON bus_locations(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_locations_timestamp ON bus_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_bus_locations_tenant_id ON bus_locations(tenant_id);

CREATE INDEX IF NOT EXISTS idx_geofences_tenant_id ON geofences(tenant_id);
CREATE INDEX IF NOT EXISTS idx_geofences_active ON geofences(is_active);

CREATE INDEX IF NOT EXISTS idx_geofence_alerts_bus_id ON geofence_alerts(bus_id);
CREATE INDEX IF NOT EXISTS idx_geofence_alerts_triggered_at ON geofence_alerts(triggered_at);
CREATE INDEX IF NOT EXISTS idx_geofence_alerts_tenant_id ON geofence_alerts(tenant_id);

CREATE INDEX IF NOT EXISTS idx_bus_maintenance_bus_id ON bus_maintenance(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_scheduled_date ON bus_maintenance(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_status ON bus_maintenance(status);
CREATE INDEX IF NOT EXISTS idx_bus_maintenance_tenant_id ON bus_maintenance(tenant_id);

CREATE INDEX IF NOT EXISTS idx_route_stops_route_id ON route_stops(route_id);
CREATE INDEX IF NOT EXISTS idx_route_stops_order ON route_stops(route_id, stop_order);
CREATE INDEX IF NOT EXISTS idx_route_stops_tenant_id ON route_stops(tenant_id);

CREATE INDEX IF NOT EXISTS idx_bus_tracking_history_bus_id ON bus_tracking_history(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_tracking_history_start_time ON bus_tracking_history(start_time);
CREATE INDEX IF NOT EXISTS idx_bus_tracking_history_tenant_id ON bus_tracking_history(tenant_id);

CREATE INDEX IF NOT EXISTS idx_emergency_alerts_bus_id ON emergency_alerts(bus_id);
CREATE INDEX IF NOT EXISTS idx_emergency_alerts_resolved ON emergency_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_emergency_alerts_tenant_id ON emergency_alerts(tenant_id);

CREATE INDEX IF NOT EXISTS idx_vehicle_inspections_bus_id ON vehicle_inspections(bus_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_inspections_date ON vehicle_inspections(inspection_date);
CREATE INDEX IF NOT EXISTS idx_vehicle_inspections_tenant_id ON vehicle_inspections(tenant_id);

-- Enable RLS on all tables
ALTER TABLE bus_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE geofences ENABLE ROW LEVEL SECURITY;
ALTER TABLE geofence_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_tracking_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicle_inspections ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Bus locations policies
CREATE POLICY "Users can view bus locations for their tenant" ON bus_locations
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can insert bus locations for their tenant" ON bus_locations
    FOR INSERT WITH CHECK (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Geofences policies
CREATE POLICY "Users can view geofences for their tenant" ON geofences
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage geofences for their tenant" ON geofences
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Geofence alerts policies
CREATE POLICY "Users can view geofence alerts for their tenant" ON geofence_alerts
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage geofence alerts for their tenant" ON geofence_alerts
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Bus maintenance policies
CREATE POLICY "Users can view bus maintenance for their tenant" ON bus_maintenance
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage bus maintenance for their tenant" ON bus_maintenance
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Route stops policies
CREATE POLICY "Users can view route stops for their tenant" ON route_stops
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage route stops for their tenant" ON route_stops
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Bus tracking history policies
CREATE POLICY "Users can view bus tracking history for their tenant" ON bus_tracking_history
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage bus tracking history for their tenant" ON bus_tracking_history
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Emergency alerts policies
CREATE POLICY "Users can view emergency alerts for their tenant" ON emergency_alerts
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage emergency alerts for their tenant" ON emergency_alerts
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Vehicle inspections policies
CREATE POLICY "Users can view vehicle inspections for their tenant" ON vehicle_inspections
    FOR SELECT USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

CREATE POLICY "Users can manage vehicle inspections for their tenant" ON vehicle_inspections
    FOR ALL USING (tenant_id = auth.jwt() ->> 'tenant_id'::text);

-- Create function to check overdue maintenance
CREATE OR REPLACE FUNCTION check_overdue_maintenance()
RETURNS TABLE(
    maintenance_id UUID,
    bus_id UUID,
    bus_plate_number VARCHAR,
    scheduled_date DATE,
    days_overdue INTEGER,
    tenant_id UUID
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        bm.id AS maintenance_id,
        bm.bus_id,
        COALESCE(b.plate_number, 'Unknown') AS bus_plate_number,
        bm.scheduled_date,
        (CURRENT_DATE - bm.scheduled_date)::INTEGER AS days_overdue,
        bm.tenant_id
    FROM bus_maintenance bm
    LEFT JOIN buses b ON bm.bus_id = b.id
    WHERE bm.status = 'scheduled'
    AND bm.scheduled_date < CURRENT_DATE;
END;
$ LANGUAGE plpgsql;

-- Enable realtime for all tables
alter publication supabase_realtime add table bus_locations;
alter publication supabase_realtime add table geofences;
alter publication supabase_realtime add table geofence_alerts;
alter publication supabase_realtime add table bus_maintenance;
alter publication supabase_realtime add table route_stops;
alter publication supabase_realtime add table bus_tracking_history;
alter publication supabase_realtime add table emergency_alerts;
alter publication supabase_realtime add table vehicle_inspections;