import React from "react";
import { useTranslation } from "react-i18next";
import { Building2, Bus, Users, MapPin, Bell, TrendingUp } from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { StatCard } from "../../components/dashboard/StatCard";
import { AdminStats } from "../../components/dashboard/AdminStats";
import { LiveMap } from "../../components/map/LiveMap";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { UserRole } from "../../types";

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses, students, routes, users } = useDatabase();
  const { filterDataByRole } = useRBACEnhancedSecurity();

  // Get stats based on user role and tenant data using RBAC filtering
  const getStats = () => {
    if (!user) return {};

    // Use RBAC-filtered data for consistent access control
    const filteredBuses = filterDataByRole(buses, "bus", "driver_id");
    const filteredStudents = filterDataByRole(students, "student", "parent_id");
    const filteredRoutes = filterDataByRole(routes, "route");
    const filteredUsers = filterDataByRole(users, "user", "id");

    if (user.role === UserRole.ADMIN) {
      // Admin - show system-wide stats
      const activeBuses = filteredBuses.filter((b) => b.is_active);
      const activeStudents = filteredStudents.filter((s) => s.is_active);
      const activeRoutes = filteredRoutes.filter((r) => r.is_active);
      const activeUsers = filteredUsers.filter((u) => u.is_active);

      return {
        totalUsers: filteredUsers.length,
        activeUsers: activeUsers.length,
        totalBuses: filteredBuses.length,
        activeBuses: activeBuses.length,
        totalStudents: filteredStudents.length,
        activeStudents: activeStudents.length,
        totalRoutes: filteredRoutes.length,
        activeRoutes: activeRoutes.length,
        drivers: filteredUsers.filter((u) => u.role === "driver").length,
        parents: filteredUsers.filter((u) => u.role === "parent").length,
        schoolManagers: filteredUsers.filter((u) => u.role === "school_manager")
          .length,
        supervisors: filteredUsers.filter((u) => u.role === "supervisor")
          .length,
      };
    } else if (user.tenant_id) {
      // Tenant-scoped stats
      const activeBuses = filteredBuses.filter((b) => b.is_active);
      const activeStudents = filteredStudents.filter((s) => s.is_active);
      const activeRoutes = filteredRoutes.filter((r) => r.is_active);
      const tenantUsers = filteredUsers.filter(
        (u) => u.tenant_id === user.tenant_id,
      );

      if (user.role === UserRole.PARENT) {
        const myChildren = activeStudents.filter(
          (s) => s.parent_id === user.id,
        );
        return {
          children: myChildren.length,
          activeBuses: activeBuses.length,
          routes: activeRoutes.length,
        };
      } else if (user.role === UserRole.DRIVER) {
        const myBus = activeBuses.find((b) => b.driver_id === user.id);
        const myRoute = myBus
          ? activeRoutes.find((r) => r.bus_id === myBus.id)
          : null;
        const routeStudents = myRoute
          ? activeStudents.filter((s) => {
              // Students on stops of this route
              return myRoute.stops?.some((stop) => stop.id === s.route_stop_id);
            })
          : [];

        return {
          myBus: myBus ? 1 : 0,
          myRoute: myRoute ? 1 : 0,
          studentsOnRoute: routeStudents.length,
        };
      } else {
        // School managers, supervisors, admins
        return {
          buses: activeBuses.length,
          students: activeStudents.length,
          routes: activeRoutes.length,
          users: tenantUsers.length,
          drivers: tenantUsers.filter((u) => u.role === "driver").length,
          parents: tenantUsers.filter((u) => u.role === "parent").length,
        };
      }
    }

    return {};
  };

  const stats = getStats();

  // Generate recent activity based on RBAC-filtered data
  const getRecentActivity = () => {
    const activities = [];

    // Use RBAC-filtered data for consistent access control
    const filteredBuses = filterDataByRole(buses, "bus", "driver_id");
    const filteredStudents = filterDataByRole(students, "student", "parent_id");
    const filteredRoutes = filterDataByRole(routes, "route");

    // Recent bus updates
    const recentBuses = filteredBuses
      .filter((b) => b.last_updated)
      .sort(
        (a, b) =>
          new Date(b.last_updated!).getTime() -
          new Date(a.last_updated!).getTime(),
      )
      .slice(0, 2);

    recentBuses.forEach((bus) => {
      activities.push({
        id: `bus-${bus.id}`,
        title: `Bus ${bus.plate_number} location updated`,
        time: new Date(bus.last_updated!).toLocaleString(),
        icon: <Bus size={16} className="text-primary-500" />,
      });
    });

    // Recent students
    const recentStudents = filteredStudents
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      )
      .slice(0, 2);

    recentStudents.forEach((student) => {
      activities.push({
        id: `student-${student.id}`,
        title: `Student ${student.name} registered`,
        time: new Date(student.created_at).toLocaleString(),
        icon: <Users size={16} className="text-accent-500" />,
      });
    });

    // Recent routes
    const recentRoutes = filteredRoutes
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      )
      .slice(0, 1);

    recentRoutes.forEach((route) => {
      activities.push({
        id: `route-${route.id}`,
        title: `Route ${route.name} updated`,
        time: new Date(route.created_at).toLocaleString(),
        icon: <MapPin size={16} className="text-secondary-500" />,
      });
    });

    return activities.slice(0, 4);
  };

  const recentActivity = getRecentActivity();

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {tenant?.settings?.branding?.schoolName ||
                    tenant?.name ||
                    t("dashboard.overview")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t("dashboard.welcome")}, {user?.name}
                </p>
                {tenant?.settings?.branding?.tagline && (
                  <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                    {tenant.settings.branding.tagline}
                  </p>
                )}
              </div>

              <div className="mt-4 md:mt-0">
                <div className="inline-flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md">
                    <TrendingUp size={16} className="mr-2 text-primary-500" />
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Admin Dashboard */}
            {user?.role === UserRole.ADMIN ? (
              <AdminStats />
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                  {/* Tenant-specific Stats */}
                  {"buses" in stats && (
                    <StatCard
                      title={t("dashboard.activeBuses")}
                      value={stats.buses}
                      icon={<Bus size={24} />}
                      trend={{ value: 2, isPositive: true }}
                    />
                  )}

                  {"students" in stats && (
                    <StatCard
                      title={t("dashboard.totalStudents")}
                      value={stats.students}
                      icon={<Users size={24} />}
                      trend={{ value: 8, isPositive: true }}
                    />
                  )}

                  {"routes" in stats && (
                    <StatCard
                      title={t("dashboard.totalRoutes")}
                      value={stats.routes}
                      icon={<MapPin size={24} />}
                      trend={{ value: 0, isPositive: true }}
                    />
                  )}

                  {"drivers" in stats && (
                    <StatCard
                      title="Active Drivers"
                      value={stats.drivers}
                      icon={<Users size={24} />}
                      trend={{ value: 3, isPositive: true }}
                    />
                  )}

                  {"parents" in stats && (
                    <StatCard
                      title="Registered Parents"
                      value={stats.parents}
                      icon={<Users size={24} />}
                      trend={{ value: 15, isPositive: true }}
                    />
                  )}

                  {/* Parent-specific Stats */}
                  {"children" in stats && (
                    <StatCard
                      title={t("students.manageStudents")}
                      value={stats.children}
                      icon={<Users size={24} />}
                    />
                  )}

                  {"activeBuses" in stats && user?.role === UserRole.PARENT && (
                    <StatCard
                      title="Active Buses"
                      value={stats.activeBuses}
                      icon={<Bus size={24} />}
                    />
                  )}

                  {/* Driver-specific Stats */}
                  {"myBus" in stats && (
                    <StatCard
                      title="My Bus"
                      value={stats.myBus}
                      icon={<Bus size={24} />}
                    />
                  )}

                  {"myRoute" in stats && (
                    <StatCard
                      title="My Route"
                      value={stats.myRoute}
                      icon={<MapPin size={24} />}
                    />
                  )}

                  {"studentsOnRoute" in stats && (
                    <StatCard
                      title="Students on Route"
                      value={stats.studentsOnRoute}
                      icon={<Users size={24} />}
                    />
                  )}
                </div>
              </>
            )}

            {/* Show map and activity for non-admin users */}
            {user?.role !== UserRole.ADMIN && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                        {t("tracking.liveTracking")}
                      </h2>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100">
                        Live
                      </span>
                    </div>
                    <LiveMap />
                  </div>
                </div>

                <div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {t("dashboard.recentActivity")}
                    </h2>

                    <div className="space-y-4">
                      {recentActivity.length > 0 ? (
                        recentActivity.map((activity) => (
                          <div
                            key={activity.id}
                            className="flex items-start space-x-3"
                          >
                            <div className="flex-shrink-0 mt-1">
                              {activity.icon}
                            </div>
                            <div className="min-w-0 flex-1">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {activity.title}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {activity.time}
                              </p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                          <Bell size={48} className="mx-auto mb-4 opacity-50" />
                          <p className="text-sm">No recent activity</p>
                          <p className="text-xs">
                            Activity will appear here as you use the system
                          </p>
                        </div>
                      )}
                    </div>

                    <button className="mt-4 w-full text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-center">
                      View all activity
                    </button>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mt-4">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {t("dashboard.todayAttendance")}
                    </h2>

                    <div className="space-y-3">
                      {(() => {
                        const filteredStudents = filterDataByRole(
                          students,
                          "student",
                          "parent_id",
                        );
                        const filteredRoutes = filterDataByRole(
                          routes,
                          "route",
                        );
                        return filteredStudents.length > 0 ? (
                          <>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                Total Students
                              </span>
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-gray-900 dark:text-white">
                                  {
                                    filteredStudents.filter((s) => s.is_active)
                                      .length
                                  }
                                </span>
                              </div>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-accent-500 h-2 rounded-full"
                                style={{ width: "100%" }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between mt-2">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                Active Routes
                              </span>
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-gray-900 dark:text-white">
                                  {
                                    filteredRoutes.filter((r) => r.is_active)
                                      .length
                                  }
                                </span>
                              </div>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-primary-500 h-2 rounded-full"
                                style={{
                                  width:
                                    filteredRoutes.length > 0 ? "100%" : "0%",
                                }}
                              ></div>
                            </div>
                          </>
                        ) : (
                          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                            <Users
                              size={32}
                              className="mx-auto mb-2 opacity-50"
                            />
                            <p className="text-sm">
                              No students registered yet
                            </p>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};
