import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "./rbac";

/**
 * مصفوفة الصلاحيات الشاملة - Access Control Matrix
 * تحدد بوضوح ما يمكن لكل دور فعله في النظام
 */
export interface AccessControlMatrix {
  role: UserRole;
  permissions: Permission[];
  dataScopes: DataScope[];
  allowedActions: {
    [key in ResourceType]?: Action[];
  };
  restrictions: {
    tenantScope?: boolean; // محدود بنطاق المدرسة
    personalScope?: boolean; // محدود بالبيانات الشخصية
    assignedScope?: boolean; // محدود بالبيانات المخصصة
    childrenScope?: boolean; // محدود ببيانات الأطفال
  };
  description: string;
}

/**
 * مصفوفة التحكم في الوصول الكاملة
 */
export const ACCESS_CONTROL_MATRIX: Record<UserRole, AccessControlMatrix> = {
  [UserRole.ADMIN]: {
    role: UserRole.ADMIN,
    permissions: [
      Permission.SYSTEM_ADMIN,
      Permission.SYSTEM_SETTINGS,
      Permission.SYSTEM_AUDIT,
      Permission.SYSTEM_BACKUP,
      Permission.VIEW_ALL_SCHOOLS,
      Permission.MANAGE_SCHOOLS,
      Permission.VIEW_ALL_USERS,
      Permission.MANAGE_ALL_USERS,
      Permission.ASSIGN_USER_ROLES,
      Permission.VIEW_ALL_BUSES,
      Permission.MANAGE_BUSES,
      Permission.ASSIGN_BUS_DRIVERS,
      Permission.TRACK_BUS,
      Permission.MANAGE_BUS_MAINTENANCE,
      Permission.VIEW_ALL_STUDENTS,
      Permission.MANAGE_STUDENTS,
      Permission.MANAGE_ATTENDANCE,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_ALL_ROUTES,
      Permission.MANAGE_ROUTES,
      Permission.OPTIMIZE_ROUTES,
      Permission.VIEW_SYSTEM_REPORTS,
      Permission.EXPORT_REPORTS,
      Permission.SCHEDULE_REPORTS,
      Permission.VIEW_ALL_NOTIFICATIONS,
      Permission.SEND_NOTIFICATIONS,
      Permission.MANAGE_NOTIFICATION_SETTINGS,
      Permission.VIEW_MAINTENANCE,
      Permission.MANAGE_MAINTENANCE,
      Permission.SCHEDULE_MAINTENANCE,
      Permission.VIEW_EVALUATIONS,
      Permission.CREATE_EVALUATION,
      Permission.RESPOND_TO_EVALUATION,
      Permission.ANALYZE_EVALUATIONS,
    ],
    dataScopes: [
      DataScope.GLOBAL,
      DataScope.TENANT,
      DataScope.PERSONAL,
      DataScope.ASSIGNED,
      DataScope.CHILDREN,
    ],
    allowedActions: {
      [ResourceType.SYSTEM]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.SCHOOL]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.USER]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
      ],
      [ResourceType.BUS]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
        Action.TRACK,
      ],
      [ResourceType.ROUTE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ANALYZE,
      ],
      [ResourceType.STUDENT]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.NOTIFICATION]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.REPORT]: [
        Action.CREATE,
        Action.READ,
        Action.EXPORT,
        Action.SCHEDULE,
        Action.ANALYZE,
      ],
      [ResourceType.MAINTENANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.SCHEDULE,
      ],
      [ResourceType.EVALUATION]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ANALYZE,
      ],
    },
    restrictions: {},
    description: "مدير النظام - صلاحيات كاملة لجميع أجزاء النظام بدون قيود",
  },

  [UserRole.SCHOOL_MANAGER]: {
    role: UserRole.SCHOOL_MANAGER,
    permissions: [
      Permission.VIEW_SCHOOL_SETTINGS,
      Permission.MANAGE_SCHOOL_SETTINGS,
      Permission.VIEW_TENANT_USERS,
      Permission.MANAGE_TENANT_USERS,
      Permission.VIEW_OWN_PROFILE,
      Permission.MANAGE_OWN_PROFILE,
      Permission.VIEW_TENANT_BUSES,
      Permission.MANAGE_BUSES,
      Permission.ASSIGN_BUS_DRIVERS,
      Permission.TRACK_BUS,
      Permission.MANAGE_BUS_MAINTENANCE,
      Permission.VIEW_TENANT_STUDENTS,
      Permission.MANAGE_STUDENTS,
      Permission.MANAGE_ATTENDANCE,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_TENANT_ROUTES,
      Permission.MANAGE_ROUTES,
      Permission.OPTIMIZE_ROUTES,
      Permission.VIEW_TENANT_REPORTS,
      Permission.EXPORT_REPORTS,
      Permission.SCHEDULE_REPORTS,
      Permission.SEND_NOTIFICATIONS,
      Permission.MANAGE_NOTIFICATION_SETTINGS,
      Permission.VIEW_MAINTENANCE,
      Permission.MANAGE_MAINTENANCE,
      Permission.SCHEDULE_MAINTENANCE,
      Permission.VIEW_EVALUATIONS,
      Permission.CREATE_EVALUATION,
      Permission.RESPOND_TO_EVALUATION,
      Permission.ANALYZE_EVALUATIONS,
    ],
    dataScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    allowedActions: {
      [ResourceType.SCHOOL]: [Action.READ, Action.UPDATE, Action.MANAGE],
      [ResourceType.USER]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.BUS]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ASSIGN,
        Action.TRACK,
      ],
      [ResourceType.ROUTE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
        Action.ANALYZE,
      ],
      [ResourceType.STUDENT]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.DELETE,
        Action.MANAGE,
      ],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
      ],
      [ResourceType.NOTIFICATION]: [Action.CREATE, Action.READ, Action.MANAGE],
      [ResourceType.REPORT]: [
        Action.READ,
        Action.EXPORT,
        Action.SCHEDULE,
        Action.ANALYZE,
      ],
      [ResourceType.MAINTENANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
        Action.SCHEDULE,
      ],
      [ResourceType.EVALUATION]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
        Action.ANALYZE,
      ],
    },
    restrictions: {
      tenantScope: true,
    },
    description: "مدير المدرسة - إدارة كاملة للمدرسة المخصصة له فقط",
  },

  [UserRole.SUPERVISOR]: {
    role: UserRole.SUPERVISOR,
    permissions: [
      Permission.VIEW_TENANT_USERS,
      Permission.VIEW_OWN_PROFILE,
      Permission.MANAGE_OWN_PROFILE,
      Permission.VIEW_TENANT_BUSES,
      Permission.TRACK_BUS,
      Permission.VIEW_TENANT_STUDENTS,
      Permission.MANAGE_ATTENDANCE,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_TENANT_ROUTES,
      Permission.VIEW_TENANT_REPORTS,
      Permission.SEND_NOTIFICATIONS,
      Permission.VIEW_MAINTENANCE,
      Permission.VIEW_EVALUATIONS,
      Permission.CREATE_EVALUATION,
      Permission.RESPOND_TO_EVALUATION,
    ],
    dataScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    allowedActions: {
      [ResourceType.USER]: [Action.READ],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ, Action.MANAGE],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
      ],
      [ResourceType.NOTIFICATION]: [Action.CREATE, Action.READ],
      [ResourceType.REPORT]: [Action.READ],
      [ResourceType.MAINTENANCE]: [Action.READ],
      [ResourceType.EVALUATION]: [Action.CREATE, Action.READ, Action.UPDATE],
    },
    restrictions: {
      tenantScope: true,
    },
    description: "مشرف النقل - مراقبة وإدارة العمليات اليومية للنقل في المدرسة",
  },

  [UserRole.DRIVER]: {
    role: UserRole.DRIVER,
    permissions: [
      Permission.VIEW_OWN_PROFILE,
      Permission.MANAGE_OWN_PROFILE,
      Permission.VIEW_ASSIGNED_BUSES,
      Permission.TRACK_BUS,
      Permission.MANAGE_ATTENDANCE,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_ASSIGNED_ROUTE,
      Permission.VIEW_MAINTENANCE,
      Permission.MANAGE_MAINTENANCE,
      Permission.RESPOND_TO_EVALUATION,
    ],
    dataScopes: [DataScope.ASSIGNED, DataScope.PERSONAL],
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ],
      [ResourceType.ATTENDANCE]: [
        Action.CREATE,
        Action.READ,
        Action.UPDATE,
        Action.MANAGE,
      ],
      [ResourceType.MAINTENANCE]: [Action.CREATE, Action.READ],
      [ResourceType.EVALUATION]: [Action.READ, Action.UPDATE],
    },
    restrictions: {
      assignedScope: true,
      personalScope: true,
    },
    description: "السائق - إدارة الحضور والصيانة للحافلة المخصصة له",
  },

  [UserRole.PARENT]: {
    role: UserRole.PARENT,
    permissions: [
      Permission.VIEW_OWN_PROFILE,
      Permission.MANAGE_OWN_PROFILE,
      Permission.TRACK_BUS,
      Permission.VIEW_OWN_CHILDREN,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_ASSIGNED_ROUTE,
      Permission.CREATE_EVALUATION,
      Permission.RESPOND_TO_EVALUATION,
    ],
    dataScopes: [DataScope.PERSONAL, DataScope.CHILDREN],
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.STUDENT]: [Action.READ],
      [ResourceType.ATTENDANCE]: [Action.READ],
      [ResourceType.EVALUATION]: [Action.CREATE, Action.UPDATE],
    },
    restrictions: {
      childrenScope: true,
      personalScope: true,
    },
    description: "ولي الأمر - متابعة أطفاله وتقييم الخدمة",
  },

  [UserRole.STUDENT]: {
    role: UserRole.STUDENT,
    permissions: [
      Permission.VIEW_OWN_PROFILE,
      Permission.MANAGE_OWN_PROFILE,
      Permission.TRACK_BUS,
      Permission.VIEW_ATTENDANCE,
      Permission.VIEW_ASSIGNED_ROUTE,
    ],
    dataScopes: [DataScope.PERSONAL],
    allowedActions: {
      [ResourceType.USER]: [Action.READ, Action.UPDATE],
      [ResourceType.BUS]: [Action.READ, Action.TRACK],
      [ResourceType.ROUTE]: [Action.READ],
      [ResourceType.ATTENDANCE]: [Action.READ],
    },
    restrictions: {
      personalScope: true,
    },
    description: "الطالب - عرض البيانات الشخصية والجداول فقط",
  },
};

/**
 * دالة للحصول على مصفوفة التحكم لدور معين
 */
export function getAccessControlMatrix(role: UserRole): AccessControlMatrix {
  return ACCESS_CONTROL_MATRIX[role];
}

/**
 * دالة للتحقق من صلاحية معينة لدور
 */
export function hasRolePermission(
  role: UserRole,
  permission: Permission,
): boolean {
  const matrix = getAccessControlMatrix(role);
  return matrix.permissions.includes(permission);
}

/**
 * دالة للتحقق من إمكانية تنفيذ عملية على مورد
 */
export function canPerformAction(
  role: UserRole,
  resource: ResourceType,
  action: Action,
): boolean {
  const matrix = getAccessControlMatrix(role);
  const allowedActions = matrix.allowedActions[resource];
  return allowedActions ? allowedActions.includes(action) : false;
}

/**
 * دالة للحصول على نطاقات البيانات المسموحة لدور
 */
export function getAllowedDataScopes(role: UserRole): DataScope[] {
  const matrix = getAccessControlMatrix(role);
  return matrix.dataScopes;
}

/**
 * دالة للتحقق من القيود المطبقة على دور
 */
export function getRoleRestrictions(role: UserRole) {
  const matrix = getAccessControlMatrix(role);
  return matrix.restrictions;
}

/**
 * دالة لتصدير مصفوفة الصلاحيات كـ JSON
 */
export function exportAccessControlMatrix(): string {
  return JSON.stringify(ACCESS_CONTROL_MATRIX, null, 2);
}

/**
 * دالة لطباعة تقرير الصلاحيات المحسن مع تحليل الأمان
 */
export function generatePermissionsReport(): string {
  let report = "=== 🔒 تقرير صلاحيات النظام المحسن ===\n\n";
  report += `📅 تاريخ التقرير: ${new Date().toLocaleString("ar-SA")}\n\n`;

  let totalVulnerabilities = 0;
  let totalRecommendations = 0;

  Object.values(UserRole).forEach((role) => {
    const matrix = getAccessControlMatrix(role);
    const securityAnalysis = analyzeRoleSecurity(role);

    report += `## 👤 ${role.toUpperCase()}\n`;
    report += `📝 الوصف: ${matrix.description}\n`;
    report += `🔢 عدد الصلاحيات: ${matrix.permissions.length}\n`;
    report += `🎯 نطاقات البيانات: ${matrix.dataScopes.join(", ")}\n`;
    report += `🛡️ مستوى الأمان: ${securityAnalysis.securityLevel}\n`;
    report += `📊 نقاط الأمان: ${securityAnalysis.securityScore}/100\n`;

    if (Object.keys(matrix.restrictions).length > 0) {
      report += `⚠️ القيود: ${Object.keys(matrix.restrictions).join(", ")}\n`;
    }

    if (securityAnalysis.vulnerabilities.length > 0) {
      report += `\n🚨 الثغرات الأمنية (${securityAnalysis.vulnerabilities.length}):\n`;
      securityAnalysis.vulnerabilities.forEach((vuln) => {
        report += `  ❌ ${vuln}\n`;
      });
      totalVulnerabilities += securityAnalysis.vulnerabilities.length;
    }

    if (securityAnalysis.recommendations.length > 0) {
      report += `\n💡 التوصيات (${securityAnalysis.recommendations.length}):\n`;
      securityAnalysis.recommendations.forEach((rec) => {
        report += `  ✅ ${rec}\n`;
      });
      totalRecommendations += securityAnalysis.recommendations.length;
    }

    report += "\n📋 الصلاحيات:\n";
    matrix.permissions.forEach((permission) => {
      const riskLevel = assessPermissionRisk(permission);
      const riskIcon =
        riskLevel === "high" ? "🔴" : riskLevel === "medium" ? "🟡" : "🟢";
      report += `  ${riskIcon} ${permission}\n`;
    });

    report += "\n🔧 العمليات المسموحة:\n";
    Object.entries(matrix.allowedActions).forEach(([resource, actions]) => {
      report += `  📂 ${resource}: ${actions.join(", ")}\n`;
    });

    report += "\n" + "=".repeat(60) + "\n\n";
  });

  // إضافة ملخص عام
  report += "\n📊 === ملخص التقرير ===\n";
  report += `🚨 إجمالي الثغرات: ${totalVulnerabilities}\n`;
  report += `💡 إجمالي التوصيات: ${totalRecommendations}\n`;
  report += `🎯 حالة الامتثال: ${totalVulnerabilities === 0 ? "✅ ممتاز" : totalVulnerabilities < 5 ? "⚠️ يحتاج تحسين" : "🚨 حرج"}\n`;

  return report;
}

/**
 * تحليل أمان الدور
 */
function analyzeRoleSecurity(role: UserRole): {
  securityLevel: "high" | "medium" | "low";
  securityScore: number;
  vulnerabilities: string[];
  recommendations: string[];
} {
  const matrix = getAccessControlMatrix(role);
  const vulnerabilities: string[] = [];
  const recommendations: string[] = [];
  let securityScore = 100;

  // فحص الصلاحيات الزائدة
  const highRiskPermissions = matrix.permissions.filter(
    (p) => assessPermissionRisk(p) === "high",
  );
  if (highRiskPermissions.length > 0 && role !== UserRole.ADMIN) {
    vulnerabilities.push(
      `صلاحيات عالية المخاطر: ${highRiskPermissions.length}`,
    );
    securityScore -= highRiskPermissions.length * 10;
  }

  // فحص نطاق البيانات
  if (matrix.dataScopes.includes("global" as any) && role !== UserRole.ADMIN) {
    vulnerabilities.push("وصول عام للبيانات لدور غير إداري");
    securityScore -= 30;
  }

  // توصيات عامة
  if (role === UserRole.ADMIN) {
    recommendations.push("تفعيل المصادقة الثنائية");
    recommendations.push("مراجعة دورية للصلاحيات");
  }

  if (role === UserRole.DRIVER || role === UserRole.PARENT) {
    recommendations.push("تحديد مهلة زمنية للجلسة");
  }

  const securityLevel =
    securityScore >= 80 ? "high" : securityScore >= 60 ? "medium" : "low";

  return {
    securityLevel,
    securityScore: Math.max(0, securityScore),
    vulnerabilities,
    recommendations,
  };
}

/**
 * تقييم مخاطر الصلاحية
 */
function assessPermissionRisk(permission: string): "high" | "medium" | "low" {
  const highRiskPatterns = ["SYSTEM_", "DELETE", "ADMIN", "MANAGE_ALL"];
  const mediumRiskPatterns = ["MANAGE_", "CREATE", "UPDATE"];

  if (highRiskPatterns.some((pattern) => permission.includes(pattern))) {
    return "high";
  }

  if (mediumRiskPatterns.some((pattern) => permission.includes(pattern))) {
    return "medium";
  }

  return "low";
}
