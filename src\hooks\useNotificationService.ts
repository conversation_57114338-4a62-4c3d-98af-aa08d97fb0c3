const sendMaintenanceNotification = useCallback(
    async ({
      busId,
      maintenanceType,
      description,
      scheduledDate,
      cost,
    }: {
      busId: string;
      maintenanceType: "routine" | "repair" | "inspection";
      description: string;
      scheduledDate: Date;
      cost?: number;
    }) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      await createMaintenanceNotification(
        busId,
        maintenanceType,
        description,
        scheduledDate,
        user.tenant_id,
        cost,
      );
    },
    [user?.tenant_id],
  );

  const sendEmergencyNotification = useCallback(
    async ({
      title,
      message,
      emergencyType,
      location,
      busId,
    }: {
      title: string;
      message: string;
      emergencyType: "accident" | "breakdown" | "medical" | "security" | "weather";
      location?: { lat: number; lng: number };
      busId?: string;
    }) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      await createEmergencyNotification(
        title,
        message,
        user.tenant_id,
        emergencyType,
        location,
        busId,
      );
    },
    [user?.tenant_id],
  );

  const sendRouteChangeNotification = useCallback(
    async ({
      routeId,
      changeType,
      description,
      effectiveDate,
    }: {
      routeId: string;
      changeType: "schedule" | "stops" | "driver" | "bus" | "cancellation";
      description: string;
      effectiveDate: Date;
    }) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      await createRouteChangeNotification(
        routeId,
        changeType,
        description,
        effectiveDate,
        user.tenant_id,
      );
    },
    [user?.tenant_id],
  );

  const sendBusDelayNotification = useCallback(
    async ({
      busId,
      routeId,
      delayMinutes,
      reason,
      estimatedArrival,
    }: {
      busId: string;
      routeId: string;
      delayMinutes: number;
      reason: string;
      estimatedArrival: Date;
    }) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      await createBusDelayNotification(
        busId,
        routeId,
        delayMinutes,
        reason,
        estimatedArrival,
        user.tenant_id,
      );
    },
    [user?.tenant_id],
  );

  const sendCustomAnnouncement = useCallback(
    async ({
      title,
      message,
      targetRoles,
      priority = "normal",
      scheduleFor,
    }: {
      title: string;
      message: string;
      targetRoles?: string[];
      priority?: "low" | "normal" | "high";
      scheduleFor?: Date;
    }) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      await notificationService.sendAnnouncement({
        title,
        message,
        targetRoles,
        priority,
        tenantId: user.tenant_id,
        scheduleFor,
      });
    },
    [user?.tenant_id],
  );

  const sendNotificationFromTemplate = useCallback(
    async ({
      templateId,
      variables,
      userIds,
    }: {
      templateId: string;
      variables: Record<string, string>;
      userIds?: string[];
    }) => {
      await notificationService.sendNotificationFromTemplate(
        templateId,
        variables,
        userIds,
      );
    },
    [],
  );

  const getNotificationTemplates = useCallback(
    async (type?: string) => {
      if (!user?.tenant_id) throw new Error("No tenant ID available");
      
      return await notificationService.getNotificationTemplates(
        user.tenant_id,
        type,
      );
    },
    [user?.tenant_id],
  );

  return {
    sendAttendanceNotification,
    sendMaintenanceNotification,
    sendEmergencyNotification,
    sendRouteChangeNotification,
    sendBusDelayNotification,
    sendCustomAnnouncement,
    sendNotificationFromTemplate,
    getNotificationTemplates,
  };
};

export default useNotificationService;