/**
 * Enhanced Security Middleware
 * Addresses critical audit findings with comprehensive server-side validation
 */

import { User, UserRole } from "../types";
import {
  Permission,
  DataScope,
  ResourceType,
  Action,
  RBACManager,
} from "../lib/rbac";
import {
  ENHANCED_ACTION_PERMISSION_MATRIX,
  ENHANCED_DATA_ACCESS_PATTERNS,
  SECURITY_POLICIES,
  ENHANCED_PERMISSION_ERROR_MESSAGES,
} from "../lib/rbacCentralizedConfigEnhanced";

export interface SecurityValidationResult {
  allowed: boolean;
  error?: string;
  securityLevel?: "low" | "medium" | "high" | "critical";
  auditRequired?: boolean;
  riskScore?: number;
  details?: any;
}

export interface SecurityContext {
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp: Date;
  resourceId?: string;
  resourceOwnerId?: string;
  resourceTenantId?: string;
  resourceData?: any;
}

/**
 * Enhanced Security Middleware Class
 */
export class EnhancedSecurityMiddleware {
  private static auditLog: any[] = [];
  private static rateLimitStore = new Map<
    string,
    { count: number; resetTime: number; violations: number }
  >();
  private static securityViolations = new Map<
    string,
    { count: number; lastViolation: number }
  >();
  private static suspiciousActivities = new Map<string, any[]>();

  /**
   * Comprehensive resource access validation
   */
  static async validateResourceAccess(
    user: User,
    resource: ResourceType,
    action: Action,
    context: SecurityContext,
  ): Promise<SecurityValidationResult> {
    const startTime = performance.now();

    try {
      // 1. Basic user validation
      const userValidation = this.validateUser(user);
      if (!userValidation.allowed) {
        return userValidation;
      }

      // 2. Session validation
      const sessionValidation = this.validateSession(user, context);
      if (!sessionValidation.allowed) {
        return sessionValidation;
      }

      // 3. Security policy validation
      const policyValidation = this.validateSecurityPolicy(
        user,
        action,
        context,
      );
      if (!policyValidation.allowed) {
        return policyValidation;
      }

      // 4. Rate limiting validation
      const rateLimitValidation = this.validateRateLimit(
        user,
        resource,
        action,
        context,
      );
      if (!rateLimitValidation.allowed) {
        return rateLimitValidation;
      }

      // 5. Permission validation
      const permissionValidation = this.validatePermissions(
        user,
        resource,
        action,
        context,
      );
      if (!permissionValidation.allowed) {
        return permissionValidation;
      }

      // 6. Data scope validation
      const dataScopeValidation = this.validateDataScope(
        user,
        resource,
        context,
      );
      if (!dataScopeValidation.allowed) {
        return dataScopeValidation;
      }

      // 7. Risk assessment
      const riskScore = this.calculateRiskScore(
        user,
        resource,
        action,
        context,
      );

      // 8. Audit logging
      const actionConfig =
        ENHANCED_ACTION_PERMISSION_MATRIX[resource]?.[action];
      if (actionConfig?.auditRequired || riskScore > 70) {
        this.logSecurityEvent("resource_access_granted", {
          userId: user.id,
          userRole: user.role,
          resource,
          action,
          context,
          riskScore,
          duration: performance.now() - startTime,
        });
      }

      return {
        allowed: true,
        securityLevel: actionConfig?.securityLevel || "low",
        auditRequired: actionConfig?.auditRequired || false,
        riskScore,
      };
    } catch (error) {
      this.logSecurityEvent("validation_error", {
        userId: user.id,
        resource,
        action,
        error: error instanceof Error ? error.message : "Unknown error",
        context,
      });

      return {
        allowed: false,
        error: "Security validation failed",
        securityLevel: "critical",
      };
    }
  }

  /**
   * Validate user account status and basic security
   */
  private static validateUser(user: User): SecurityValidationResult {
    if (!user) {
      return {
        allowed: false,
        error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
        securityLevel: "critical",
      };
    }

    if (!user.is_active) {
      this.logSecurityEvent("inactive_user_access_attempt", {
        userId: user.id,
        userRole: user.role,
      });

      return {
        allowed: false,
        error: "Account has been deactivated",
        securityLevel: "high",
      };
    }

    // Check for account lockout
    if (user.metadata?.account_locked_until) {
      const lockoutTime = new Date(user.metadata.account_locked_until);
      if (lockoutTime > new Date()) {
        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.ACCOUNT_LOCKED,
          securityLevel: "high",
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Validate session security and integrity
   */
  private static validateSession(
    user: User,
    context: SecurityContext,
  ): SecurityValidationResult {
    const userRole = user.role as UserRole;
    const securityPolicy = SECURITY_POLICIES[userRole];

    if (!securityPolicy) {
      return { allowed: true };
    }

    // Check session timeout
    const lastActivity = user.metadata?.lastActivity;
    if (lastActivity) {
      const sessionAge = Date.now() - new Date(lastActivity).getTime();
      const maxAge = securityPolicy.sessionTimeout * 60 * 1000;

      if (sessionAge > maxAge) {
        this.logSecurityEvent("session_expired", {
          userId: user.id,
          sessionAge,
          maxAge,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          securityLevel: "medium",
        };
      }
    }

    // Check IP restrictions if configured
    if (securityPolicy.ipWhitelist && context.ipAddress) {
      if (!securityPolicy.ipWhitelist.includes(context.ipAddress)) {
        this.logSecurityEvent("ip_restriction_violation", {
          userId: user.id,
          ipAddress: context.ipAddress,
          allowedIPs: securityPolicy.ipWhitelist,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.IP_RESTRICTED,
          securityLevel: "critical",
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Validate security policies (time restrictions, MFA, etc.)
   */
  private static validateSecurityPolicy(
    user: User,
    action: Action,
    context: SecurityContext,
  ): SecurityValidationResult {
    const userRole = user.role as UserRole;
    const securityPolicy = SECURITY_POLICIES[userRole];

    if (!securityPolicy) {
      return { allowed: true };
    }

    // Check time restrictions
    if (securityPolicy.allowedTimeWindows) {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentDay = now.getDay();
      const currentTime = currentHour * 60 + currentMinute;

      const isAllowedTime = securityPolicy.allowedTimeWindows.some((window) => {
        if (!window.days.includes(currentDay)) {
          return false;
        }

        const [startHour, startMinute] = window.start.split(":").map(Number);
        const [endHour, endMinute] = window.end.split(":").map(Number);
        const startTime = startHour * 60 + startMinute;
        const endTime = endHour * 60 + endMinute;

        return currentTime >= startTime && currentTime <= endTime;
      });

      if (!isAllowedTime) {
        this.logSecurityEvent("time_restriction_violation", {
          userId: user.id,
          currentTime: now.toISOString(),
          allowedWindows: securityPolicy.allowedTimeWindows,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.TIME_RESTRICTED,
          securityLevel: "medium",
        };
      }
    }

    // Check MFA requirement for high-risk actions
    const actionConfig =
      ENHANCED_ACTION_PERMISSION_MATRIX[ResourceType.USER]?.[action];
    if (actionConfig?.requiresMFA && securityPolicy.requireMFA) {
      if (!user.metadata?.mfaCompleted) {
        this.logSecurityEvent("mfa_required_violation", {
          userId: user.id,
          action,
          mfaStatus: user.metadata?.mfaCompleted,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.MFA_REQUIRED,
          securityLevel: "critical",
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Validate rate limiting
   */
  private static validateRateLimit(
    user: User,
    resource: ResourceType,
    action: Action,
    context: SecurityContext,
  ): SecurityValidationResult {
    const actionConfig = ENHANCED_ACTION_PERMISSION_MATRIX[resource]?.[action];

    if (!actionConfig?.rateLimit) {
      return { allowed: true };
    }

    const key = `${user.id}:${resource}:${action}`;
    const now = Date.now();
    const windowMs = actionConfig.rateLimit.windowMinutes * 60 * 1000;

    const current = this.rateLimitStore.get(key);

    if (!current || now - current.resetTime > windowMs) {
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now,
        violations: current?.violations || 0,
      });
      return { allowed: true };
    }

    if (current.count >= actionConfig.rateLimit.maxRequests) {
      current.violations++;

      this.logSecurityEvent("rate_limit_violation", {
        userId: user.id,
        resource,
        action,
        currentCount: current.count,
        maxAllowed: actionConfig.rateLimit.maxRequests,
        violations: current.violations,
      });

      // Escalate security response for repeated violations
      if (current.violations > 5) {
        this.flagSuspiciousActivity(user.id, "repeated_rate_limit_violations", {
          resource,
          action,
          violations: current.violations,
        });
      }

      return {
        allowed: false,
        error: ENHANCED_PERMISSION_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
        securityLevel: "high",
        details: {
          retryAfter: new Date(current.resetTime + windowMs),
          violations: current.violations,
        },
      };
    }

    current.count++;
    return { allowed: true };
  }

  /**
   * Validate permissions using RBAC system
   */
  private static validatePermissions(
    user: User,
    resource: ResourceType,
    action: Action,
    context: SecurityContext,
  ): SecurityValidationResult {
    const actionConfig = ENHANCED_ACTION_PERMISSION_MATRIX[resource]?.[action];

    if (!actionConfig) {
      this.logSecurityEvent("unknown_action_attempted", {
        userId: user.id,
        resource,
        action,
      });

      return {
        allowed: false,
        error: "Action not configured in security matrix",
        securityLevel: "medium",
      };
    }

    const rbacManager = new RBACManager();
    const userRole = user.role as UserRole;

    // Check if user has any of the required permissions
    const hasPermission = actionConfig.permissions.some((permission) =>
      rbacManager.hasPermission(userRole, permission),
    );

    if (!hasPermission) {
      this.logSecurityEvent("permission_denied", {
        userId: user.id,
        userRole,
        resource,
        action,
        requiredPermissions: actionConfig.permissions,
      });

      return {
        allowed: false,
        error: ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
        securityLevel: actionConfig.securityLevel,
        auditRequired: actionConfig.auditRequired,
        details: {
          requiredPermissions: actionConfig.permissions,
          userRole,
        },
      };
    }

    return { allowed: true };
  }

  /**
   * Validate data scope access
   */
  private static validateDataScope(
    user: User,
    resource: ResourceType,
    context: SecurityContext,
  ): SecurityValidationResult {
    const userRole = user.role as UserRole;
    const dataAccessPattern = ENHANCED_DATA_ACCESS_PATTERNS[userRole];

    if (!dataAccessPattern) {
      return {
        allowed: false,
        error: "Data access pattern not configured for user role",
        securityLevel: "high",
      };
    }

    // Check tenant isolation
    if (
      dataAccessPattern.restrictions.tenantBound &&
      context.resourceTenantId
    ) {
      if (user.tenant_id !== context.resourceTenantId) {
        this.logSecurityEvent("tenant_isolation_violation", {
          userId: user.id,
          userTenantId: user.tenant_id,
          resourceTenantId: context.resourceTenantId,
          resource,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.TENANT_MISMATCH,
          securityLevel: "critical",
          auditRequired: true,
        };
      }
    }

    // Check personal data access
    if (
      dataAccessPattern.restrictions.personalOnly &&
      context.resourceOwnerId
    ) {
      if (user.id !== context.resourceOwnerId) {
        this.logSecurityEvent("personal_data_violation", {
          userId: user.id,
          resourceOwnerId: context.resourceOwnerId,
          resource,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.DATA_ACCESS_DENIED,
          securityLevel: "high",
          auditRequired: true,
        };
      }
    }

    // Check children-only access (for parents)
    if (dataAccessPattern.restrictions.childrenOnly && context.resourceData) {
      const isParentOfStudent = this.validateParentChildRelationship(
        user.id,
        context.resourceData,
      );

      if (!isParentOfStudent) {
        this.logSecurityEvent("children_data_violation", {
          userId: user.id,
          resourceData: context.resourceData,
          resource,
        });

        return {
          allowed: false,
          error: ENHANCED_PERMISSION_ERROR_MESSAGES.DATA_ACCESS_DENIED,
          securityLevel: "high",
          auditRequired: true,
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Calculate risk score for the current operation
   */
  private static calculateRiskScore(
    user: User,
    resource: ResourceType,
    action: Action,
    context: SecurityContext,
  ): number {
    let riskScore = 0;

    // Base risk by user role
    const userRole = user.role as UserRole;
    switch (userRole) {
      case UserRole.ADMIN:
        riskScore += 50;
        break;
      case UserRole.SCHOOL_MANAGER:
        riskScore += 30;
        break;
      case UserRole.SUPERVISOR:
        riskScore += 20;
        break;
      default:
        riskScore += 10;
    }

    // Risk by action type
    switch (action) {
      case Action.DELETE:
        riskScore += 30;
        break;
      case Action.CREATE:
      case Action.UPDATE:
        riskScore += 20;
        break;
      case Action.EXPORT:
        riskScore += 15;
        break;
      default:
        riskScore += 5;
    }

    // Risk by resource type
    switch (resource) {
      case ResourceType.USER:
      case ResourceType.SCHOOL:
        riskScore += 20;
        break;
      case ResourceType.STUDENT:
      case ResourceType.ATTENDANCE:
        riskScore += 15;
        break;
      default:
        riskScore += 5;
    }

    // Time-based risk factors
    const now = new Date();
    const hour = now.getHours();

    // Higher risk for off-hours access
    if (hour < 6 || hour > 22) {
      riskScore += 15;
    }

    // Weekend access risk
    const day = now.getDay();
    if (day === 0 || day === 6) {
      riskScore += 10;
    }

    // Check for suspicious patterns
    const suspiciousActivity = this.suspiciousActivities.get(user.id);
    if (suspiciousActivity && suspiciousActivity.length > 0) {
      riskScore += Math.min(30, suspiciousActivity.length * 5);
    }

    // Recent security violations
    const violations = this.securityViolations.get(user.id);
    if (violations && violations.count > 0) {
      const recentViolations = Date.now() - violations.lastViolation < 3600000; // 1 hour
      if (recentViolations) {
        riskScore += Math.min(25, violations.count * 5);
      }
    }

    return Math.min(100, riskScore);
  }

  /**
   * Log security events with comprehensive metadata
   */
  private static logSecurityEvent(event: string, details: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      severity: this.getEventSeverity(event),
      source: "EnhancedSecurityMiddleware",
    };

    this.auditLog.push(logEntry);

    // Keep only last 10000 entries to prevent memory issues
    if (this.auditLog.length > 10000) {
      this.auditLog.shift();
    }

    // Log to console (in production, send to centralized logging service)
    console.log(`🔒 Security Event [${logEntry.severity}]:`, logEntry);

    // Alert on critical events
    if (logEntry.severity === "CRITICAL") {
      this.handleCriticalSecurityEvent(logEntry);
    }
  }

  /**
   * Flag suspicious activity for monitoring
   */
  private static flagSuspiciousActivity(
    userId: string,
    activityType: string,
    details: any,
  ): void {
    const activities = this.suspiciousActivities.get(userId) || [];

    activities.push({
      timestamp: new Date().toISOString(),
      type: activityType,
      details,
    });

    // Keep only last 50 activities per user
    if (activities.length > 50) {
      activities.shift();
    }

    this.suspiciousActivities.set(userId, activities);

    this.logSecurityEvent("suspicious_activity_flagged", {
      userId,
      activityType,
      totalActivities: activities.length,
      details,
    });
  }

  /**
   * Validate parent-child relationship for data access
   */
  private static validateParentChildRelationship(
    parentId: string,
    resourceData: any,
  ): boolean {
    // Implementation would check against actual parent-child relationships
    // This is a simplified version
    if (resourceData.parent_id === parentId) {
      return true;
    }

    if (resourceData.student && resourceData.student.parent_id === parentId) {
      return true;
    }

    return false;
  }

  /**
   * Get event severity level
   */
  private static getEventSeverity(
    event: string,
  ): "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" {
    const criticalEvents = [
      "tenant_isolation_violation",
      "ip_restriction_violation",
      "mfa_required_violation",
      "inactive_user_access_attempt",
    ];

    const highEvents = [
      "permission_denied",
      "rate_limit_violation",
      "personal_data_violation",
      "children_data_violation",
    ];

    const mediumEvents = [
      "session_expired",
      "time_restriction_violation",
      "unknown_action_attempted",
    ];

    if (criticalEvents.includes(event)) {
      return "CRITICAL";
    } else if (highEvents.includes(event)) {
      return "HIGH";
    } else if (mediumEvents.includes(event)) {
      return "MEDIUM";
    } else {
      return "LOW";
    }
  }

  /**
   * Handle critical security events
   */
  private static handleCriticalSecurityEvent(logEntry: any): void {
    // In production, this would:
    // 1. Send immediate alerts to security team
    // 2. Potentially lock user account
    // 3. Trigger additional monitoring
    // 4. Log to SIEM system

    console.error("🚨 CRITICAL SECURITY EVENT:", logEntry);

    // Track security violations
    if (logEntry.details?.userId) {
      const userId = logEntry.details.userId;
      const violations = this.securityViolations.get(userId) || {
        count: 0,
        lastViolation: 0,
      };
      violations.count++;
      violations.lastViolation = Date.now();
      this.securityViolations.set(userId, violations);
    }
  }

  /**
   * Get security audit log (for admin review)
   */
  static getAuditLog(filters?: {
    userId?: string;
    event?: string;
    severity?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): any[] {
    let filteredLog = [...this.auditLog];

    if (filters) {
      if (filters.userId) {
        filteredLog = filteredLog.filter(
          (entry) => entry.details?.userId === filters.userId,
        );
      }

      if (filters.event) {
        filteredLog = filteredLog.filter(
          (entry) => entry.event === filters.event,
        );
      }

      if (filters.severity) {
        filteredLog = filteredLog.filter(
          (entry) => entry.severity === filters.severity,
        );
      }

      if (filters.startDate) {
        filteredLog = filteredLog.filter(
          (entry) => new Date(entry.timestamp) >= filters.startDate!,
        );
      }

      if (filters.endDate) {
        filteredLog = filteredLog.filter(
          (entry) => new Date(entry.timestamp) <= filters.endDate!,
        );
      }

      if (filters.limit) {
        filteredLog = filteredLog.slice(-filters.limit);
      }
    }

    return filteredLog.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
  }

  /**
   * Get security statistics
   */
  static getSecurityStats(): {
    totalEvents: number;
    eventsBySeverity: Record<string, number>;
    topViolatingUsers: Array<{ userId: string; violations: number }>;
    suspiciousActivities: number;
    rateLimitViolations: number;
  } {
    const eventsBySeverity = this.auditLog.reduce(
      (acc, entry) => {
        acc[entry.severity] = (acc[entry.severity] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const topViolatingUsers = Array.from(this.securityViolations.entries())
      .map(([userId, data]) => ({ userId, violations: data.count }))
      .sort((a, b) => b.violations - a.violations)
      .slice(0, 10);

    const suspiciousActivities = Array.from(
      this.suspiciousActivities.values(),
    ).reduce((total, activities) => total + activities.length, 0);

    const rateLimitViolations = this.auditLog.filter(
      (entry) => entry.event === "rate_limit_violation",
    ).length;

    return {
      totalEvents: this.auditLog.length,
      eventsBySeverity,
      topViolatingUsers,
      suspiciousActivities,
      rateLimitViolations,
    };
  }

  /**
   * Clear old audit data (for maintenance)
   */
  static clearOldAuditData(olderThanDays: number = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    this.auditLog = this.auditLog.filter(
      (entry) => new Date(entry.timestamp) > cutoffDate,
    );

    // Clear old rate limit data
    const now = Date.now();
    for (const [key, data] of this.rateLimitStore.entries()) {
      if (now - data.resetTime > 24 * 60 * 60 * 1000) {
        // 24 hours
        this.rateLimitStore.delete(key);
      }
    }

    // Clear old suspicious activities
    for (const [userId, activities] of this.suspiciousActivities.entries()) {
      const recentActivities = activities.filter(
        (activity) => new Date(activity.timestamp) > cutoffDate,
      );

      if (recentActivities.length === 0) {
        this.suspiciousActivities.delete(userId);
      } else {
        this.suspiciousActivities.set(userId, recentActivities);
      }
    }

    this.logSecurityEvent("audit_data_cleanup", {
      cutoffDate: cutoffDate.toISOString(),
      remainingEvents: this.auditLog.length,
    });
  }
}

export default EnhancedSecurityMiddleware;
