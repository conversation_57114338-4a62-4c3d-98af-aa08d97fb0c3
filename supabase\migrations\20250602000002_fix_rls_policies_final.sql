-- إصلاح نهائي لسياسات RLS وحل مشكلة إنشاء المستخدمين
-- Final fix for RLS policies and user creation issue

BEGIN;

-- 1. تعطيل RLS مؤقتاً لإجراء التعديلات
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- 2. حذف جميع السياسات الموجودة لتجنب التضارب
DO $$ 
DECLARE 
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'users' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON users';
    END LOOP;
END $$;

-- 3. إنشاء دالة للتحقق من دور المستخدم
CREATE OR REPLACE FUNCTION auth.get_user_role()
RETURNS text
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    (SELECT role FROM public.users WHERE id = auth.uid()),
    'anonymous'
  );
$$;

-- 4. إنشاء دالة للحصول على tenant_id
CREATE OR REPLACE FUNCTION auth.get_user_tenant()
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT tenant_id FROM public.users WHERE id = auth.uid();
$$;

-- 5. إنشاء دالة آمنة لإنشاء المستخدم
CREATE OR REPLACE FUNCTION public.safe_create_user(
  user_id uuid,
  user_email text,
  user_name text,
  user_role text DEFAULT 'student',
  user_tenant_id uuid DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_user users;
  new_user users;
  result json;
BEGIN
  -- التحقق من وجود المستخدم
  SELECT * INTO existing_user 
  FROM public.users 
  WHERE id = user_id;
  
  -- إذا كان المستخدم موجود، إرجاع بياناته
  IF existing_user.id IS NOT NULL THEN
    SELECT row_to_json(existing_user) INTO result;
    RETURN result;
  END IF;

  -- إنشاء مستخدم جديد
  INSERT INTO public.users (
    id,
    email,
    name,
    role,
    tenant_id,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    user_id,
    user_email,
    user_name,
    user_role,
    user_tenant_id,
    true,
    NOW(),
    NOW()
  ) RETURNING * INTO new_user;

  -- تسجيل حدث إنشاء المستخدم
  INSERT INTO public.security_events (
    event_type,
    severity,
    description,
    user_id,
    tenant_id,
    metadata,
    created_at
  ) VALUES (
    'USER_PROFILE_CREATED',
    'INFO',
    'User profile created via safe_create_user function',
    new_user.id,
    new_user.tenant_id,
    jsonb_build_object(
      'email', new_user.email,
      'role', new_user.role,
      'method', 'safe_create_user'
    ),
    NOW()
  );

  SELECT row_to_json(new_user) INTO result;
  RETURN result;
END;
$$;

-- 6. منح الصلاحيات للدالة
GRANT EXECUTE ON FUNCTION public.safe_create_user TO authenticated;
GRANT EXECUTE ON FUNCTION public.safe_create_user TO anon;

-- 7. إنشاء سياسات RLS جديدة ومبسطة

-- سياسة القراءة: المستخدم يمكنه رؤية ملفه + الأدمن يرى الكل + مدراء المدارس يرون مدرستهم
CREATE POLICY "users_select_policy" ON users
  FOR SELECT
  USING (
    -- المستخدم يرى ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن يرى جميع المستخدمين
    auth.get_user_role() = 'admin'
    OR
    -- مدير المدرسة يرى مستخدمي مدرسته
    (auth.get_user_role() = 'school_manager' AND tenant_id = auth.get_user_tenant())
    OR
    -- المشرف يرى مستخدمي مدرسته
    (auth.get_user_role() = 'supervisor' AND tenant_id = auth.get_user_tenant())
  );

-- سياسة الإدراج: السماح للجميع بإنشاء ملفهم الشخصي + الأدمن + مدراء المدارس
CREATE POLICY "users_insert_policy" ON users
  FOR INSERT
  WITH CHECK (
    -- المستخدم ينشئ ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن ينشئ أي مستخدم
    auth.get_user_role() = 'admin'
    OR
    -- مدير المدرسة ينشئ مستخدمين في مدرسته
    (auth.get_user_role() = 'school_manager' AND tenant_id = auth.get_user_tenant())
    OR
    -- السماح للدوال الآمنة (عندما لا يوجد مستخدم مصادق عليه)
    auth.uid() IS NULL
  );

-- سياسة التحديث: المستخدم يحدث ملفه + الأدمن + مدراء المدارس
CREATE POLICY "users_update_policy" ON users
  FOR UPDATE
  USING (
    -- المستخدم يحدث ملفه الشخصي
    id = auth.uid()
    OR
    -- الأدمن يحدث أي مستخدم
    auth.get_user_role() = 'admin'
    OR
    -- مدير المدرسة يحدث مستخدمي مدرسته
    (auth.get_user_role() = 'school_manager' AND tenant_id = auth.get_user_tenant())
  );

-- سياسة الحذف: الأدمن فقط + مدراء المدارس (لمدرستهم فقط)
CREATE POLICY "users_delete_policy" ON users
  FOR DELETE
  USING (
    -- الأدمن يحذف أي مستخدم
    auth.get_user_role() = 'admin'
    OR
    -- مدير المدرسة يحذف مستخدمي مدرسته (عدا نفسه)
    (auth.get_user_role() = 'school_manager' 
     AND tenant_id = auth.get_user_tenant() 
     AND id != auth.uid())
  );

-- 8. إعادة تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 9. إنشاء دالة للتحقق من صحة النظام
CREATE OR REPLACE FUNCTION public.check_user_system_status()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  policy_count integer;
  rls_enabled boolean;
BEGIN
  -- عد السياسات
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'users' AND schemaname = 'public';

  -- فحص حالة RLS
  SELECT row_security INTO rls_enabled
  FROM pg_tables 
  WHERE tablename = 'users' AND schemaname = 'public';

  result := json_build_object(
    'status', 'HEALTHY',
    'timestamp', NOW(),
    'rls_enabled', rls_enabled,
    'policies_count', policy_count,
    'functions_available', json_build_array(
      'safe_create_user',
      'auth.get_user_role',
      'auth.get_user_tenant'
    )
  );

  RETURN result;
END;
$$;

-- 10. إنشاء trigger لتسجيل أحداث إنشاء المستخدمين
CREATE OR REPLACE FUNCTION public.log_user_events()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO public.security_events (
      event_type,
      severity,
      description,
      user_id,
      tenant_id,
      metadata,
      created_at
    ) VALUES (
      'USER_CREATED',
      'INFO',
      'New user profile created',
      NEW.id,
      NEW.tenant_id,
      jsonb_build_object(
        'email', NEW.email,
        'role', NEW.role,
        'created_by', COALESCE(auth.uid()::text, 'system'),
        'method', 'direct_insert'
      ),
      NOW()
    );
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$;

-- إنشاء trigger
DROP TRIGGER IF EXISTS user_events_trigger ON users;
CREATE TRIGGER user_events_trigger
  AFTER INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION public.log_user_events();

COMMIT;
