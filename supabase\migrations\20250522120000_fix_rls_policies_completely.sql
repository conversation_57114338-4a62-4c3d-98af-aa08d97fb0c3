-- Completely fix the RLS policies to prevent infinite recursion

-- Disable <PERSON><PERSON> temporarily to clean up
ALTER TABLE "users" DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Admin users can see all users" ON "users";
DROP POLICY IF EXISTS "Users can see users in their tenant" ON "users";
DROP POLICY IF EXISTS "Users can see themselves" ON "users";
DROP POLICY IF EXISTS "Enable read access for all users" ON "users";
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON "users";
DROP POLICY IF EXISTS "Enable update for users based on email" ON "users";
DROP POLICY IF EXISTS "Enable delete for users based on email" ON "users";

-- Re-enable RLS
ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;

-- Create a simple policy that allows authenticated users to read all users
-- This prevents recursion by not querying the users table within the policy
CREATE POLICY "Allow authenticated users to read all users"
  ON "users"
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Allow users to update their own records
CREATE POLICY "Users can update their own records"
  ON "users"
  FOR UPDATE
  USING (auth.uid() = id);

-- Allow authenticated users to insert new users (for admin functionality)
CREATE POLICY "Allow authenticated users to insert users"
  ON "users"
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- Allow users to delete (for admin functionality)
CREATE POLICY "Allow authenticated users to delete users"
  ON "users"
  FOR DELETE
  USING (auth.role() = 'authenticated');

-- Create a function to check if current user is admin without causing recursion
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
BEGIN
  -- Check if the current user has admin role in auth.users metadata
  RETURN (auth.jwt() ->> 'user_metadata' ->> 'role') = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;

-- Update the get_all_users function to be simpler
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS SETOF "users" AS $$
BEGIN
  RETURN QUERY SELECT * FROM "users" ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;
