/**
 * Enhanced RBAC Hook
 * Centralized permission management with improved consistency and performance
 */

import { useMemo, useCallback } from "react";
import { useAuth } from "../contexts/AuthContext";
import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "../lib/rbac";
import {
  ROUTE_PERMISSIONS,
  COMPONENT_PERMISSIONS,
  DATA_ACCESS_PATTERNS,
  ACTION_PERMISSION_MATRIX,
  PERMISSION_ERROR_MESSAGES,
} from "../lib/rbacCentralizedConfig";
import { usePermissions } from "./usePermissions";

export interface RBACResult {
  allowed: boolean;
  error?: string;
  fallbackRoute?: string;
}

export interface EnhancedRBACHook {
  // Route-level permissions
  canAccessRoute: (route: string) => RBACResult;

  // Component-level permissions
  canUseComponent: (componentKey: string) => RBACResult;

  // Action-based permissions
  canPerformAction: (
    resource: ResourceType,
    action: Action,
    context?: any,
  ) => RBACResult;

  // Data filtering
  filterDataByRole: <T extends { tenant_id?: string; id?: string }>(
    data: T[],
    resourceType: ResourceType,
    ownerField?: keyof T,
  ) => T[];

  // Navigation helpers
  getAccessibleRoutes: () => string[];
  getNavigationItems: () => Array<{
    key: string;
    route: string;
    accessible: boolean;
    icon?: string;
  }>;

  // Permission checking utilities
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;

  // Role and scope utilities
  getUserDataScope: () => DataScope;
  canManageUser: (targetUser: any) => boolean;

  // Error handling
  getPermissionError: (errorType: string) => string;

  // Current user context
  currentUser: any;
  userRole: UserRole | null;
  isAdmin: boolean;
  isSchoolManager: boolean;
}

/**
 * Enhanced RBAC Hook Implementation
 */
export const useRBACEnhanced = (): EnhancedRBACHook => {
  const { user } = useAuth();
  const permissions = usePermissions();

  const userRole = user?.role as UserRole | null;
  const isAdmin = userRole === UserRole.ADMIN;
  const isSchoolManager = userRole === UserRole.SCHOOL_MANAGER;

  // Route access checking
  const canAccessRoute = useCallback(
    (route: string): RBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
          fallbackRoute: "/login",
        };
      }

      const routeConfig = ROUTE_PERMISSIONS[route];
      if (!routeConfig) {
        // Route not configured - allow access (backward compatibility)
        return { allowed: true };
      }

      // If no permissions required, allow access
      if (routeConfig.permissions.length === 0) {
        return { allowed: true };
      }

      // Check permissions
      const hasPermission = routeConfig.requireAll
        ? routeConfig.permissions.every((p) => permissions.hasPermission(p))
        : routeConfig.permissions.some((p) => permissions.hasPermission(p));

      if (!hasPermission) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.ROUTE_ACCESS_DENIED,
          fallbackRoute: routeConfig.fallbackRoute || "/dashboard",
        };
      }

      return { allowed: true };
    },
    [user, userRole, permissions],
  );

  // Component access checking
  const canUseComponent = useCallback(
    (componentKey: string): RBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
        };
      }

      const componentConfig = COMPONENT_PERMISSIONS[componentKey];
      if (!componentConfig) {
        // Component not configured - allow access (backward compatibility)
        return { allowed: true };
      }

      // Check role restrictions first
      if (componentConfig.roles && !componentConfig.roles.includes(userRole)) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
        };
      }

      // Check permissions
      const hasPermission = componentConfig.permissions.some((p) =>
        permissions.hasPermission(p),
      );

      if (!hasPermission) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED,
        };
      }

      return { allowed: true };
    },
    [user, userRole, permissions],
  );

  // Action-based permission checking
  const canPerformAction = useCallback(
    (resource: ResourceType, action: Action, context?: any): RBACResult => {
      if (!user || !userRole) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED,
        };
      }

      const actionPermissions = ACTION_PERMISSION_MATRIX[resource]?.[action];
      if (!actionPermissions) {
        // Action not configured - deny access for security
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED,
        };
      }

      // Check if user has any of the required permissions
      const hasPermission = actionPermissions.some((p) =>
        permissions.hasPermission(p),
      );

      if (!hasPermission) {
        return {
          allowed: false,
          error: PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
        };
      }

      // Additional context-based checks
      if (context) {
        const contextResult = permissions.checkResourceAccess(
          resource,
          action,
          context,
        );
        if (!contextResult.allowed) {
          return {
            allowed: false,
            error:
              contextResult.error ||
              PERMISSION_ERROR_MESSAGES.DATA_ACCESS_DENIED,
          };
        }
      }

      return { allowed: true };
    },
    [user, userRole, permissions],
  );

  // Data filtering based on role and permissions
  const filterDataByRole = useCallback(
    <T extends { tenant_id?: string; id?: string }>(
      data: T[],
      resourceType: ResourceType,
      ownerField?: keyof T,
    ): T[] => {
      if (!user || !userRole) {
        return [];
      }

      return permissions.filterDataByPermissions(
        data,
        resourceType,
        ownerField,
      );
    },
    [user, userRole, permissions],
  );

  // Get accessible routes for current user
  const getAccessibleRoutes = useCallback((): string[] => {
    if (!user || !userRole) {
      return [];
    }

    return Object.keys(ROUTE_PERMISSIONS).filter((route) => {
      const result = canAccessRoute(route);
      return result.allowed;
    });
  }, [user, userRole, canAccessRoute]);

  // Get navigation items with accessibility info
  const getNavigationItems = useCallback(() => {
    const items = [];

    for (const [key, config] of Object.entries(ROUTE_PERMISSIONS)) {
      const result = canAccessRoute(key);
      items.push({
        key,
        route: key,
        accessible: result.allowed,
        icon: (config as any).icon,
      });
    }

    return items;
  }, [canAccessRoute]);

  // Permission checking utilities
  const hasAnyPermission = useCallback(
    (permissionList: Permission[]): boolean => {
      return permissionList.some((p) => permissions.hasPermission(p));
    },
    [permissions],
  );

  const hasAllPermissions = useCallback(
    (permissionList: Permission[]): boolean => {
      return permissionList.every((p) => permissions.hasPermission(p));
    },
    [permissions],
  );

  // Get user's data scope
  const getUserDataScope = useCallback((): DataScope => {
    if (!userRole) {
      return DataScope.PERSONAL;
    }

    return DATA_ACCESS_PATTERNS[userRole]?.defaultScope || DataScope.PERSONAL;
  }, [userRole]);

  // Check if current user can manage another user
  const canManageUser = useCallback(
    (targetUser: any): boolean => {
      if (!user || !userRole || !targetUser) {
        return false;
      }

      return permissions.canEditUser(targetUser);
    },
    [user, userRole, permissions],
  );

  // Get permission error message
  const getPermissionError = useCallback((errorType: string): string => {
    return (
      PERMISSION_ERROR_MESSAGES[errorType] ||
      PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS
    );
  }, []);

  return useMemo(
    () => ({
      canAccessRoute,
      canUseComponent,
      canPerformAction,
      filterDataByRole,
      getAccessibleRoutes,
      getNavigationItems,
      hasAnyPermission,
      hasAllPermissions,
      getUserDataScope,
      canManageUser,
      getPermissionError,
      currentUser: user,
      userRole,
      isAdmin,
      isSchoolManager,
    }),
    [
      canAccessRoute,
      canUseComponent,
      canPerformAction,
      filterDataByRole,
      getAccessibleRoutes,
      getNavigationItems,
      hasAnyPermission,
      hasAllPermissions,
      getUserDataScope,
      canManageUser,
      getPermissionError,
      user,
      userRole,
      isAdmin,
      isSchoolManager,
    ],
  );
};

export default useRBACEnhanced;
