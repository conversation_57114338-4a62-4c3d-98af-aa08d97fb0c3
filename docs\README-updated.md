# 🚌 نظام إدارة الحافلات المدرسية | School Bus Management System

**إصدار:** 1.0.0  
**الحالة:** قيد التطوير - يحتاج تطبيق الهجرات  
**آخر تحديث:** 2025-01-25  

---

## 📋 نظرة عامة | Overview

نظام شامل لإدارة الحافلات المدرسية مصمم خصيصاً للمدارس في المنطقة العربية. يوفر النظام إدارة متكاملة للحافلات والطلاب والمسارات مع نظام أمان متقدم ودعم كامل للغة العربية.

### الميزات الرئيسية:
- 🏫 **إدارة متعددة المدارس** - نظام Multi-tenant آمن
- 👥 **نظام صلاحيات متقدم** - RBAC شامل مع 6 أدوار مختلفة
- 🚌 **تتبع الحافلات** - GPS tracking في الوقت الفعلي
- 📱 **تطبيق الأولياء** - متابعة الأطفال والإشعارات
- 📊 **تقارير شاملة** - تحليلات وإحصائيات مفصلة
- 🔐 **أمان عالي** - Row Level Security وتشفير البيانات
- 🌐 **دعم RTL** - واجهة عربية كاملة

---

## 🏗️ التقنيات المستخدمة | Tech Stack

### Frontend:
- **React 18.3.1** - مكتبة واجهة المستخدم
- **TypeScript** - لضمان الأمان النوعي
- **Vite 5.4.19** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل CSS
- **Radix UI** - مكونات واجهة متقدمة

### Backend & Database:
- **Supabase** - قاعدة بيانات وخدمات خلفية
- **PostgreSQL 15.8** - قاعدة البيانات الرئيسية
- **Row Level Security** - أمان على مستوى الصفوف
- **PostGIS** - دعم البيانات الجغرافية

### Additional Libraries:
- **Mapbox GL** - خرائط تفاعلية
- **Recharts** - رسوم بيانية
- **i18next** - دعم متعدد اللغات
- **React Router** - التنقل بين الصفحات

---

## 🚨 الحالة الحالية | Current Status

### ✅ ما يعمل:
- بنية الكود مكتملة ومنظمة
- نظام RBAC مصمم بالكامل
- واجهة المستخدم جاهزة
- ملفات الهجرة متوفرة

### ⚠️ ما يحتاج إصلاح:
- **قاعدة البيانات فارغة** - لم يتم تطبيق الهجرات
- **عدم وجود بيانات اختبار** - يحتاج إنشاء بيانات أولية
- **ملفات RBAC متعددة** - تحتاج توحيد وتبسيط

### 🎯 الأولوية الفورية:
1. تطبيق هجرات قاعدة البيانات
2. إنشاء مستخدم admin أولي
3. إنشاء بيانات اختبار أساسية

---

## 🚀 التثبيت والتشغيل | Installation & Setup

### المتطلبات:
- Node.js 18+ 
- npm أو yarn
- حساب Supabase

### خطوات التثبيت:

#### 1. استنساخ المشروع:
```bash
git clone [repository-url]
cd SchoolBus
```

#### 2. تثبيت المكتبات:
```bash
npm install
```

#### 3. إعداد متغيرات البيئة:
```bash
# إنشاء ملف .env
cp .env.example .env

# تحديث المتغيرات:
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 4. تطبيق هجرات قاعدة البيانات:
```bash
# ⚠️ هذه الخطوة مطلوبة حالياً
# يجب تطبيق ملفات الهجرة يدوياً في Supabase Dashboard
# أو استخدام Supabase CLI
```

#### 5. تشغيل المشروع:
```bash
npm run dev
```

---

## 👥 الأدوار والصلاحيات | User Roles

### 1. 🔧 Admin - مدير النظام
- صلاحيات كاملة لجميع المدارس
- إدارة النظام والإعدادات
- 52 صلاحية مختلفة

### 2. 🏫 School Manager - مدير المدرسة  
- إدارة مدرسة واحدة فقط
- إدارة المستخدمين والحافلات
- 38 صلاحية محدودة بالمدرسة

### 3. 👁️ Supervisor - مشرف
- مراقبة ومتابعة فقط
- عرض التقارير والإحصائيات
- 22 صلاحية للقراءة

### 4. 🚌 Driver - سائق
- إدارة حافلة واحدة
- تسجيل حضور الطلاب
- 16 صلاحية محدودة

### 5. 👨‍👩‍👧‍👦 Parent - ولي أمر
- متابعة الأطفال فقط
- استقبال الإشعارات
- 12 صلاحية للمتابعة

### 6. 🎓 Student - طالب
- عرض البيانات الشخصية
- متابعة الحافلة
- 8 صلاحيات أساسية

---

## 📁 بنية المشروع | Project Structure

```
SchoolBus/
├── src/
│   ├── components/          # مكونات واجهة المستخدم
│   │   ├── auth/           # مكونات المصادقة
│   │   ├── dashboard/      # لوحة التحكم
│   │   ├── buses/          # إدارة الحافلات
│   │   └── students/       # إدارة الطلاب
│   ├── contexts/           # إدارة الحالة
│   ├── hooks/              # React Hooks
│   ├── lib/                # مكتبات مساعدة
│   ├── pages/              # صفحات التطبيق
│   └── types/              # تعريفات TypeScript
├── supabase/
│   ├── migrations/         # ملفات هجرة قاعدة البيانات
│   └── functions/          # دوال Supabase
├── docs/                   # الوثائق
└── public/                 # ملفات عامة
```

---

## 🔐 الأمان | Security

### نظام الأمان المطبق:
- **Row Level Security (RLS)** - حماية على مستوى الصفوف
- **Multi-tenant Architecture** - عزل البيانات بين المدارس
- **RBAC System** - نظام صلاحيات متقدم
- **Audit Logging** - تسجيل جميع العمليات
- **Data Encryption** - تشفير البيانات الحساسة

### سياسات الأمان:
- كل مدرسة معزولة تماماً عن الأخرى
- المستخدمون يرون بياناتهم فقط
- تسجيل جميع العمليات الحساسة
- مراقبة محاولات الوصول غير المصرح

---

## 📊 قاعدة البيانات | Database

### الجداول الرئيسية:
- **tenants** - المدارس
- **users** - المستخدمين  
- **buses** - الحافلات
- **routes** - المسارات
- **students** - الطلاب
- **attendance** - الحضور
- **notifications** - الإشعارات

### الحالة الحالية:
⚠️ **قاعدة البيانات فارغة** - تحتاج تطبيق الهجرات

---

## 🧪 الاختبار | Testing

### أنواع الاختبارات:
- **Unit Tests** - اختبار الوحدات
- **Integration Tests** - اختبار التكامل  
- **Security Tests** - اختبار الأمان
- **Performance Tests** - اختبار الأداء

### تشغيل الاختبارات:
```bash
npm run test              # جميع الاختبارات
npm run test:watch        # مراقبة التغييرات
npm run test:permissions  # اختبار الصلاحيات
```

---

## 📚 الوثائق | Documentation

### الوثائق المتوفرة:
- `docs/comprehensive-inspection-report.md` - تقرير الفحص الشامل
- `docs/database-security-audit.md` - تقرير الأمان
- `docs/user-roles-permissions.md` - دليل الصلاحيات
- `docs/action-plan.md` - خطة العمل
- `docs/changelog.md` - سجل التغييرات

---

## 🚨 خطوات عاجلة مطلوبة | Urgent Steps Required

### خلال 24 ساعة:
1. **تطبيق هجرات قاعدة البيانات**
2. **إنشاء مستخدم admin أولي**
3. **إنشاء بيانات اختبار**

### خلال أسبوع:
1. **توحيد نظام RBAC**
2. **إنشاء اختبارات شاملة**
3. **إكمال الوثائق**

---

## 🤝 المساهمة | Contributing

### قواعد المساهمة:
1. اتباع معايير TypeScript
2. كتابة اختبارات للميزات الجديدة
3. توثيق التغييرات في changelog
4. مراجعة الأمان قبل الدمج

---

## 📞 الدعم | Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني:** <EMAIL>
- **المشروع:** SchoolBus Management System
- **الحالة:** قيد التطوير النشط

---

**تم إعداد هذا الدليل بواسطة:** Augment Agent  
**تاريخ الإعداد:** 2025-01-25  
**حالة المشروع:** جاهز للتطوير - يحتاج تطبيق الهجرات فوراً
