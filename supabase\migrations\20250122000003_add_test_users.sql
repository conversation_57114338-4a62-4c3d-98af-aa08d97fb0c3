-- Add test tenants for development and testing
-- This migration creates sample tenants only
-- Users should be created through the application UI to ensure proper auth.users entries

-- First, let's ensure we have some tenants to work with
INSERT INTO tenants (id, name, address, is_active) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'Greenwood Elementary School', '123 Oak Street, Springfield', true),
  ('550e8400-e29b-41d4-a716-446655440002', 'Riverside High School', '456 River Road, Riverside', true),
  ('550e8400-e29b-41d4-a716-446655440003', 'Sunset Middle School', '789 Sunset Blvd, Westside', true)
ON CONFLICT (id) DO NOTHING;

-- Note: Test users should be created through the application UI
-- This ensures proper creation in both auth.users and public.users tables
-- The following users can be created manually:
--
-- Super Admin:
-- - Name: Moataz Magdi
-- - Email: <EMAIL>
-- - Role: admin
-- - Tenant: None
--
-- School Managers:
-- - <PERSON> (<EMAIL>) - Greenwood Elementary
-- - <PERSON> (<EMAIL>) - Riverside High School
-- - <PERSON> (<EMAIL>) - Sunset Middle School
--
-- Transport Supervisors:
-- - <PERSON> (<EMAIL>) - Greenwood Elementary
-- - Lisa Thompson (<EMAIL>) - Riverside High School
--
-- Drivers:
-- - <PERSON> (<EMAIL>) - Greenwood Elementary
-- - Jennifer Davis (<EMAIL>) - Greenwood Elementary
-- - James Anderson (<EMAIL>) - Riverside High School
--
-- Parents:
-- - Maria Garcia (<EMAIL>) - Greenwood Elementary
-- - John Smith (<EMAIL>) - Greenwood Elementary
-- - Amanda Brown (<EMAIL>) - Riverside High School
-- - Carlos Hernandez (<EMAIL>) - Sunset Middle School
--
-- Students:
-- - Alex Garcia (<EMAIL>) - Greenwood Elementary
-- - Emma Smith (<EMAIL>) - Greenwood Elementary
-- - Tyler Brown (<EMAIL>) - Riverside High School
-- - Sofia Hernandez (<EMAIL>) - Sunset Middle School

-- Enable realtime for users table
alter publication supabase_realtime add table users;
