-- Fix infinite recursion in users table policies

-- First, drop the problematic policies
DROP POLICY IF EXISTS "Admin users can see all users" ON "users";
DROP POLICY IF EXISTS "Users can see users in their tenant" ON "users";

-- Create a policy for admin users that doesn't cause recursion
CREATE POLICY "Admin users can see all users"
  ON "users"
  FOR SELECT
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin')
  );

-- Create a policy for users to see others in their tenant
CREATE POLICY "Users can see users in their tenant"
  ON "users"
  FOR SELECT
  USING (
    auth.uid() IN (SELECT id FROM users WHERE tenant_id = users.tenant_id)
  );

-- Create a policy for users to see themselves
CREATE POLICY "Users can see themselves"
  ON "users"
  FOR SELECT
  USING (auth.uid() = id);

-- Create a function that doesn't cause recursion
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS SETOF "users" AS $$
BEGIN
  RETURN QUERY SELECT * FROM "users" ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
