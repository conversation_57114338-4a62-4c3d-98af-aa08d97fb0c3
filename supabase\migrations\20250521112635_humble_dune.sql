/*
  # Fix missing tables and policies

  1. New Tables
    - `tenants` (if not exists)
    - `users` (if not exists)
    - `buses` (if not exists)
    - `routes` (if not exists)
    - `route_stops` (if not exists)

  2. Security
    - Enable RLS on all tables
    - Add policies for proper access control
*/

-- Create tenants table
CREATE TABLE IF NOT EXISTS tenants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  domain text UNIQUE,
  logo_url text,
  address text,
  contact_number text,
  is_active boolean DEFAULT true,
  settings jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- Create tenant policies
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'tenants' 
    AND policyname = 'Tenants are manageable by admins only'
  ) THEN
    CREATE POLICY "Tenants are manageable by admins only"
      ON tenants
      FOR ALL
      TO authenticated
      USING (
        auth.uid() IN (
          SELECT id FROM users WHERE role = 'admin'
        )
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'tenants' 
    AND policyname = 'Tenants are viewable by authenticated users of the same tenant'
  ) THEN
    CREATE POLICY "Tenants are viewable by authenticated users of the same tenant"
      ON tenants
      FOR SELECT
      TO authenticated
      USING (
        auth.uid() IN (
          SELECT id FROM users WHERE tenant_id = tenants.id
        )
      );
  END IF;
END $$;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY REFERENCES auth.users(id),
  tenant_id uuid REFERENCES tenants(id),
  role user_role NOT NULL,
  name text NOT NULL,
  email text NOT NULL UNIQUE,
  avatar_url text,
  phone text,
  is_active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create user policies
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'users' 
    AND policyname = 'Users can view own record'
  ) THEN
    CREATE POLICY "Users can view own record"
      ON users
      FOR SELECT
      TO authenticated
      USING (id = auth.uid());
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'users' 
    AND policyname = 'Users can view users in same tenant'
  ) THEN
    CREATE POLICY "Users can view users in same tenant"
      ON users
      FOR SELECT
      TO authenticated
      USING (
        tenant_id IS NOT NULL AND
        tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'users' 
    AND policyname = 'Users can update own record'
  ) THEN
    CREATE POLICY "Users can update own record"
      ON users
      FOR UPDATE
      TO authenticated
      USING (id = auth.uid())
      WITH CHECK (id = auth.uid());
  END IF;
END $$;

-- Create buses table
CREATE TABLE IF NOT EXISTS buses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  plate_number text NOT NULL,
  capacity integer NOT NULL,
  driver_id uuid REFERENCES users(id),
  is_active boolean DEFAULT true,
  last_location geometry(Point,4326),
  last_updated timestamptz,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(tenant_id, plate_number)
);

-- Enable RLS
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;

-- Create bus policies
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'buses' 
    AND policyname = 'Buses are viewable by users of the same tenant'
  ) THEN
    CREATE POLICY "Buses are viewable by users of the same tenant"
      ON buses
      FOR SELECT
      TO authenticated
      USING (
        tenant_id IN (
          SELECT tenant_id FROM users WHERE id = auth.uid()
        )
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'buses' 
    AND policyname = 'Buses are manageable by school managers and supervisors'
  ) THEN
    CREATE POLICY "Buses are manageable by school managers and supervisors"
      ON buses
      FOR ALL
      TO authenticated
      USING (
        auth.uid() IN (
          SELECT id FROM users 
          WHERE tenant_id = buses.tenant_id 
          AND role IN ('school_manager', 'supervisor')
        )
      );
  END IF;
END $$;

-- Create routes table
CREATE TABLE IF NOT EXISTS routes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL REFERENCES tenants(id),
  name text NOT NULL,
  bus_id uuid REFERENCES buses(id),
  is_active boolean DEFAULT true,
  schedule jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;

-- Create route policies
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'routes' 
    AND policyname = 'Routes are viewable by users of the same tenant'
  ) THEN
    CREATE POLICY "Routes are viewable by users of the same tenant"
      ON routes
      FOR SELECT
      TO authenticated
      USING (
        tenant_id IN (
          SELECT tenant_id FROM users WHERE id = auth.uid()
        )
      );
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'routes' 
    AND policyname = 'Routes are manageable by school managers and supervisors'
  ) THEN
    CREATE POLICY "Routes are manageable by school managers and supervisors"
      ON routes
      FOR ALL
      TO authenticated
      USING (
        auth.uid() IN (
          SELECT id FROM users 
          WHERE tenant_id = routes.tenant_id 
          AND role IN ('school_manager', 'supervisor')
        )
      );
  END IF;
END $$;

-- Create route_stops table
CREATE TABLE IF NOT EXISTS route_stops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  route_id uuid NOT NULL REFERENCES routes(id),
  name text NOT NULL,
  location geometry(Point,4326) NOT NULL,
  "order" integer NOT NULL,
  arrival_time time,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE route_stops ENABLE ROW LEVEL SECURITY;

-- Create route_stops policies
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'route_stops' 
    AND policyname = 'Route stops are viewable by users of the same tenant'
  ) THEN
    CREATE POLICY "Route stops are viewable by users of the same tenant"
      ON route_stops
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM routes 
          WHERE routes.id = route_stops.route_id
          AND routes.tenant_id IN (
            SELECT tenant_id FROM users WHERE id = auth.uid()
          )
        )
      );
  END IF;
END $$;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_last_location ON buses USING GIST(last_location);
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_route_stops_location ON route_stops USING GIST(location);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_tenants_updated_at'
  ) THEN
    CREATE TRIGGER update_tenants_updated_at
        BEFORE UPDATE ON tenants
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_users_updated_at'
  ) THEN
    CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_buses_updated_at'
  ) THEN
    CREATE TRIGGER update_buses_updated_at
        BEFORE UPDATE ON buses
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_routes_updated_at'
  ) THEN
    CREATE TRIGGER update_routes_updated_at
        BEFORE UPDATE ON routes
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_route_stops_updated_at'
  ) THEN
    CREATE TRIGGER update_route_stops_updated_at
        BEFORE UPDATE ON route_stops
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;