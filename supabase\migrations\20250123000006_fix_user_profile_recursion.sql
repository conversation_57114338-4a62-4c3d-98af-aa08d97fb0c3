-- Fix infinite recursion in users table RLS policy
-- Create a function to safely fetch user profiles without triggering RLS recursion

CREATE OR REPLACE FUNCTION get_user_profile(user_id UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.name,
    u.phone,
    u.role,
    u.tenant_id,
    u.is_active,
    u.avatar_url,
    u.metadata,
    u.created_at,
    u.updated_at
  FROM users u
  WHERE u.id = user_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_profile(UUID) TO authenticated;

-- Create a function to get current user profile
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS TABLE (
  id UUID,
  email TEXT,
  name TEXT,
  phone TEXT,
  role user_role,
  tenant_id UUID,
  is_active BOOLEAN,
  avatar_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.name,
    u.phone,
    u.role,
    u.tenant_id,
    u.is_active,
    u.avatar_url,
    u.metadata,
    u.created_at,
    u.updated_at
  FROM users u
  WHERE u.id = auth.uid();
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_current_user_profile() TO authenticated;
