import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Plus, Loader2 } from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { StudentList } from "../../components/students/StudentList";
import { StudentModal } from "../../components/students/StudentModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

export const StudentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { loading, error, refreshData, tenant } = useDatabase();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<
    Tables<"students"> | undefined
  >();

  const handleOpenModal = (student?: Tables<"students">) => {
    setSelectedStudent(student);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedStudent(undefined);
    setIsModalOpen(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-error-600 dark:text-error-400">
          {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("students.manageStudents")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t("common.view")}, {t("common.add")}, {t("common.edit")}{" "}
                  {t("students.manageStudents").toLowerCase()}
                </p>
              </div>

              <div className="mt-4 md:mt-0">
                <Button
                  className="flex items-center gap-2"
                  onClick={() => handleOpenModal()}
                >
                  <Plus size={16} />
                  {t("students.addStudent")}
                </Button>
              </div>
            </div>

            <StudentList onRefresh={refreshData} />

            {isModalOpen && (
              <StudentModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onSubmit={async (data) => {
                  try {
                    if (selectedStudent) {
                      // Update existing student
                      const updateData = { ...data };
                      delete updateData.password;
                      delete updateData.email;

                      const { error } = await supabase
                        .from("students")
                        .update(updateData)
                        .eq("id", selectedStudent.id);

                      if (error) throw error;
                    } else {
                      // Create new student with auth account
                      if (!data.password || data.password.trim() === "") {
                        throw new Error(
                          "Password is required for new students",
                        );
                      }
                      if (!data.email || data.email.trim() === "") {
                        throw new Error("Email is required for new students");
                      }

                      // Prepare student user data
                      const studentUserData = {
                        email: data.email.trim(),
                        password: data.password.trim(),
                        name: data.name?.trim(),
                        role: "student",
                        tenant_id: tenant?.id,
                        phone: null,
                        is_active: true,
                      };

                      console.log("Creating student user with data:", {
                        ...studentUserData,
                        password: !!studentUserData.password,
                      });

                      const { data: userResult, error: userError } =
                        await supabase.functions.invoke(
                          "supabase-functions-create-user",
                          {
                            body: studentUserData,
                          },
                        );

                      console.log("Student creation response:", {
                        userResult,
                        userError,
                      });

                      if (userError) {
                        console.error("Student creation error:", userError);
                        throw new Error(
                          userError.message || "Failed to create student user",
                        );
                      }

                      if (!userResult?.success) {
                        console.error("Student creation failed:", userResult);
                        throw new Error(
                          userResult?.error || "Failed to create student user",
                        );
                      }

                      // Create student record
                      const studentData = { ...data };
                      delete studentData.password;
                      delete studentData.email;

                      const { error } = await supabase.from("students").insert([
                        {
                          id: userResult.user.id,
                          ...studentData,
                          tenant_id: tenant?.id,
                          parent_id: studentData.parent_id || null,
                          route_stop_id: studentData.route_stop_id || null,
                        },
                      ]);

                      if (error) throw error;
                    }

                    await refreshData();
                    handleCloseModal();
                  } catch (error) {
                    console.error("Error saving student:", error);
                    throw error;
                  }
                }}
                student={selectedStudent}
              />
            )}
          </div>
        </main>
      </div>
    </div>
  );
};
