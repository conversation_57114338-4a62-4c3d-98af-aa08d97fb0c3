-- Create notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('geofence', 'attendance', 'maintenance', 'announcements')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  variables TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification actions table
CREATE TABLE IF NOT EXISTS notification_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  action_data JSONB DEFAULT '{}',
  result TEXT,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification acknowledgments table
CREATE TABLE IF NOT EXISTS notification_acknowledgments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  acknowledged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE
);

-- Create notification groups table
CREATE TABLE IF NOT EXISTS notification_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_key TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_count INTEGER DEFAULT 1,
  last_notification_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification analytics table
CREATE TABLE IF NOT EXISTS notification_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL CHECK (event_type IN ('push_sent', 'notification_opened', 'notification_clicked', 'notification_dismissed')),
  user_ids UUID[] DEFAULT '{}',
  notification_id UUID,
  notification_data JSONB DEFAULT '{}',
  subscription_count INTEGER,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE
);

-- Create callbacks table
CREATE TABLE IF NOT EXISTS callbacks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  contact_id UUID,
  scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
  notes TEXT,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  assigned_to UUID REFERENCES users(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id) ON DELETE CASCADE,
  due_date TIMESTAMP WITH TIME ZONE,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'cancelled')),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
  recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subject TEXT,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'notification_response',
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- Create issue reports table
CREATE TABLE IF NOT EXISTS issue_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  issue_type TEXT NOT NULL,
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  resolved_by UUID REFERENCES users(id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create emergency contacts table
CREATE TABLE IF NOT EXISTS emergency_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  relationship TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  approved_by UUID REFERENCES users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_templates_tenant_id ON notification_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_actions_notification_id ON notification_actions(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_actions_user_id ON notification_actions(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_notification_id ON notification_acknowledgments(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_groups_tenant_id ON notification_groups(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_groups_group_key ON notification_groups(group_key);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_tenant_id ON notification_analytics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_type ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_timestamp ON notification_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_callbacks_user_id ON callbacks(user_id);
CREATE INDEX IF NOT EXISTS idx_callbacks_scheduled_time ON callbacks(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_by ON tasks(assigned_by);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_issue_reports_reporter_id ON issue_reports(reporter_id);
CREATE INDEX IF NOT EXISTS idx_issue_reports_status ON issue_reports(status);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_user_id ON emergency_contacts(user_id);

-- Enable realtime for new tables
ALTER PUBLICATION supabase_realtime ADD TABLE notification_templates;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_actions;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_acknowledgments;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_groups;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_analytics;
ALTER PUBLICATION supabase_realtime ADD TABLE callbacks;
ALTER PUBLICATION supabase_realtime ADD TABLE tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE issue_reports;
ALTER PUBLICATION supabase_realtime ADD TABLE emergency_contacts;

-- Create function to get notification engagement metrics
CREATE OR REPLACE FUNCTION get_notification_engagement_metrics(tenant_id UUID)
RETURNS TABLE (
  total_sent INTEGER,
  total_opened INTEGER,
  total_clicked INTEGER,
  total_dismissed INTEGER,
  open_rate DECIMAL,
  click_rate DECIMAL,
  dismiss_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END), 0)::INTEGER as total_sent,
    COALESCE(COUNT(CASE WHEN event_type = 'notification_opened' THEN 1 END), 0)::INTEGER as total_opened,
    COALESCE(COUNT(CASE WHEN event_type = 'notification_clicked' THEN 1 END), 0)::INTEGER as total_clicked,
    COALESCE(COUNT(CASE WHEN event_type = 'notification_dismissed' THEN 1 END), 0)::INTEGER as total_dismissed,
    CASE 
      WHEN COALESCE(SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END), 0) > 0 
      THEN (COUNT(CASE WHEN event_type = 'notification_opened' THEN 1 END)::DECIMAL / SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END)::DECIMAL) * 100
      ELSE 0
    END as open_rate,
    CASE 
      WHEN COALESCE(SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END), 0) > 0 
      THEN (COUNT(CASE WHEN event_type = 'notification_clicked' THEN 1 END)::DECIMAL / SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END)::DECIMAL) * 100
      ELSE 0
    END as click_rate,
    CASE 
      WHEN COALESCE(SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END), 0) > 0 
      THEN (COUNT(CASE WHEN event_type = 'notification_dismissed' THEN 1 END)::DECIMAL / SUM(CASE WHEN event_type = 'push_sent' THEN subscription_count ELSE 0 END)::DECIMAL) * 100
      ELSE 0
    END as dismiss_rate
  FROM notification_analytics 
  WHERE notification_analytics.tenant_id = get_notification_engagement_metrics.tenant_id;
END;
$$ LANGUAGE plpgsql;
