-- نظام الإشعارات الشامل مع سياسات RBAC المحدثة
-- Comprehensive Notification System with Updated RBAC Policies

-- إنشاء جداول النظام المحدثة
-- Create updated system tables

-- جدول قوالب الإشعارات المحدث
CREATE TABLE IF NOT EXISTS notification_templates (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id uuid NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name text NOT NULL,
  type text NOT NULL CHECK (type IN ('geofence', 'attendance', 'maintenance', 'announcements', 'emergency', 'delay', 'general')),
  title text NOT NULL,
  message text NOT NULL,
  variables text[] DEFAULT '{}',
  is_active boolean DEFAULT true,
  is_shared boolean DEFAULT false,
  shared_by uuid REFERENCES users(id),
  category text DEFAULT 'general',
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
  schedule_settings jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- جدول مشاركة القوالب بين المدارس
CREATE TABLE IF NOT EXISTS template_shares (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id uuid NOT NULL REFERENCES notification_templates(id) ON DELETE CASCADE,
  shared_with_tenant_id uuid NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  shared_by uuid NOT NULL REFERENCES users(id),
  shared_at timestamptz DEFAULT now(),
  message text,
  is_accepted boolean DEFAULT false,
  accepted_at timestamptz,
  accepted_by uuid REFERENCES users(id)
);

-- جدول تفضيلات الإشعارات للمستخدمين
CREATE TABLE IF NOT EXISTS notification_user_preferences (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id uuid NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  notification_types jsonb DEFAULT '{}',
  delivery_channels jsonb DEFAULT '{"push": true, "email": false, "sms": false}',
  schedule_settings jsonb DEFAULT '{}',
  sound_settings jsonb DEFAULT '{"enabled": true, "sound": "default"}',
  grouping_settings jsonb DEFAULT '{"enabled": true, "timeWindow": 300}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, tenant_id)
);

-- جدول جدولة الإشعارات
CREATE TABLE IF NOT EXISTS notification_schedules (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id uuid NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  template_id uuid NOT NULL REFERENCES notification_templates(id) ON DELETE CASCADE,
  created_by uuid NOT NULL REFERENCES users(id),
  title text NOT NULL,
  message text NOT NULL,
  target_users uuid[],
  target_roles text[],
  schedule_type text NOT NULL CHECK (schedule_type IN ('once', 'daily', 'weekly', 'monthly')),
  scheduled_at timestamptz NOT NULL,
  recurring_pattern jsonb DEFAULT '{}',
  is_active boolean DEFAULT true,
  last_sent_at timestamptz,
  next_send_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- جدول تحليلات الإشعارات المحدث
DROP TABLE IF EXISTS notification_analytics;
CREATE TABLE notification_analytics (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id uuid REFERENCES tenants(id) ON DELETE CASCADE,
  notification_id uuid REFERENCES notifications(id) ON DELETE CASCADE,
  event_type text NOT NULL CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'dismissed', 'failed')),
  user_id uuid REFERENCES users(id),
  event_data jsonb DEFAULT '{}',
  timestamp timestamptz DEFAULT now(),
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- جدول الإجراءات المجمعة للإشعارات
CREATE TABLE IF NOT EXISTS notification_bulk_actions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id uuid NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(id),
  action_type text NOT NULL CHECK (action_type IN ('mark_read', 'mark_unread', 'delete', 'archive')),
  notification_ids uuid[],
  filter_criteria jsonb DEFAULT '{}',
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  processed_count integer DEFAULT 0,
  total_count integer DEFAULT 0,
  error_message text,
  created_at timestamptz DEFAULT now(),
  completed_at timestamptz
);

-- تحديث جدول الإشعارات الموجود أولاً قبل إنشاء السياسات
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS category text DEFAULT 'general';
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS template_id uuid REFERENCES notification_templates(id);
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS scheduled_id uuid REFERENCES notification_schedules(id);
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS delivery_status text DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed'));
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS opened_at timestamptz;
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS clicked_at timestamptz;
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS dismissed_at timestamptz;

-- تحديث سياسات الإشعارات الموجودة (بعد إضافة الأعمدة)
DROP POLICY IF EXISTS "Admin can access all notifications" ON notifications;
CREATE POLICY "Enhanced notifications access"
  ON notifications
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    user_id = auth.uid() OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR
    (tenant_id IS NULL AND auth.uid() IN (SELECT id FROM users WHERE role = 'admin'))
  );

-- إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_templates_tenant_id ON notification_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_category ON notification_templates(category);
CREATE INDEX IF NOT EXISTS idx_template_shares_template_id ON template_shares(template_id);
CREATE INDEX IF NOT EXISTS idx_template_shares_tenant_id ON template_shares(shared_with_tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_schedules_tenant_id ON notification_schedules(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_schedules_next_send ON notification_schedules(next_send_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_notification_analytics_tenant_id ON notification_analytics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_type ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_timestamp ON notification_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_delivery_status ON notifications(delivery_status);

-- تحديث سياسات RLS للجداول الجديدة
-- Update RLS policies for new tables

-- سياسات قوالب الإشعارات
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "notification_templates_access" ON notification_templates;
CREATE POLICY "notification_templates_access"
  ON notification_templates
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR
    (is_shared = true AND tenant_id IN (
      SELECT shared_with_tenant_id FROM template_shares 
      WHERE template_id = notification_templates.id 
      AND is_accepted = true
    ))
  );

-- سياسات مشاركة القوالب
ALTER TABLE template_shares ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "template_shares_access" ON template_shares;
CREATE POLICY "template_shares_access"
  ON template_shares
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    shared_by = auth.uid() OR
    shared_with_tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

-- سياسات تفضيلات الإشعارات
ALTER TABLE notification_user_preferences ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "notification_preferences_access" ON notification_user_preferences;
CREATE POLICY "notification_preferences_access"
  ON notification_user_preferences
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    user_id = auth.uid() OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

-- سياسات جدولة الإشعارات
ALTER TABLE notification_schedules ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "notification_schedules_access" ON notification_schedules;
CREATE POLICY "notification_schedules_access"
  ON notification_schedules
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR
    created_by = auth.uid()
  );

-- سياسات تحليلات الإشعارات
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "notification_analytics_access" ON notification_analytics;
CREATE POLICY "notification_analytics_access"
  ON notification_analytics
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR
    user_id = auth.uid()
  );

-- سياسات الإجراءات المجمعة
ALTER TABLE notification_bulk_actions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "notification_bulk_actions_access" ON notification_bulk_actions;
CREATE POLICY "notification_bulk_actions_access"
  ON notification_bulk_actions
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()) OR
    user_id = auth.uid()
  );

-- تم نقل سياسات الإشعارات إلى الأعلى بعد إضافة الأعمدة

-- وظائف مساعدة للنظام
-- Helper functions for the system

-- وظيفة إنشاء إشعار من قالب
CREATE OR REPLACE FUNCTION create_notification_from_template(
  template_id_param uuid,
  user_ids_param uuid[],
  variables_param jsonb DEFAULT '{}'
)
RETURNS uuid[]
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  template_record notification_templates%ROWTYPE;
  user_id uuid;
  notification_id uuid;
  notification_ids uuid[] := '{}';
  processed_title text;
  processed_message text;
  variable_key text;
  variable_value text;
BEGIN
  -- الحصول على بيانات القالب
  SELECT * INTO template_record FROM notification_templates WHERE id = template_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Template not found';
  END IF;
  
  -- معالجة المتغيرات في العنوان والرسالة
  processed_title := template_record.title;
  processed_message := template_record.message;
  
  -- استبدال المتغيرات
  FOR variable_key IN SELECT jsonb_object_keys(variables_param)
  LOOP
    variable_value := variables_param ->> variable_key;
    processed_title := replace(processed_title, '{{' || variable_key || '}}', variable_value);
    processed_message := replace(processed_message, '{{' || variable_key || '}}', variable_value);
  END LOOP;
  
  -- إنشاء إشعار لكل مستخدم
  FOREACH user_id IN ARRAY user_ids_param
  LOOP
    INSERT INTO notifications (
      user_id,
      tenant_id,
      title,
      message,
      type,
      category,
      priority,
      template_id,
      metadata
    ) VALUES (
      user_id,
      template_record.tenant_id,
      processed_title,
      processed_message,
      template_record.type,
      template_record.category,
      template_record.priority,
      template_id_param,
      jsonb_build_object('variables', variables_param)
    ) RETURNING id INTO notification_id;
    
    notification_ids := array_append(notification_ids, notification_id);
  END LOOP;
  
  RETURN notification_ids;
END;
$$;

-- وظيفة معالجة الإجراءات المجمعة
CREATE OR REPLACE FUNCTION process_bulk_notification_action(
  action_id_param uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  action_record notification_bulk_actions%ROWTYPE;
  notification_id uuid;
  processed_count integer := 0;
BEGIN
  -- الحصول على بيانات الإجراء
  SELECT * INTO action_record FROM notification_bulk_actions WHERE id = action_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Bulk action not found';
  END IF;
  
  -- تحديث حالة الإجراء إلى "قيد المعالجة"
  UPDATE notification_bulk_actions 
  SET status = 'processing', updated_at = now()
  WHERE id = action_id_param;
  
  -- معالجة كل إشعار
  FOREACH notification_id IN ARRAY action_record.notification_ids
  LOOP
    CASE action_record.action_type
      WHEN 'mark_read' THEN
        UPDATE notifications SET read = true WHERE id = notification_id AND user_id = action_record.user_id;
      WHEN 'mark_unread' THEN
        UPDATE notifications SET read = false WHERE id = notification_id AND user_id = action_record.user_id;
      WHEN 'delete' THEN
        DELETE FROM notifications WHERE id = notification_id AND user_id = action_record.user_id;
      WHEN 'archive' THEN
        UPDATE notifications SET metadata = jsonb_set(COALESCE(metadata, '{}'), '{archived}', 'true') 
        WHERE id = notification_id AND user_id = action_record.user_id;
    END CASE;
    
    processed_count := processed_count + 1;
  END LOOP;
  
  -- تحديث حالة الإجراء إلى "مكتمل"
  UPDATE notification_bulk_actions 
  SET 
    status = 'completed',
    processed_count = processed_count,
    completed_at = now()
  WHERE id = action_id_param;
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    -- تحديث حالة الإجراء إلى "فشل"
    UPDATE notification_bulk_actions 
    SET 
      status = 'failed',
      error_message = SQLERRM,
      completed_at = now()
    WHERE id = action_id_param;
    
    RETURN false;
END;
$$;

-- وظيفة الحصول على إحصائيات الإشعارات
CREATE OR REPLACE FUNCTION get_notification_analytics(
  tenant_id_param uuid,
  start_date timestamptz DEFAULT now() - interval '30 days',
  end_date timestamptz DEFAULT now()
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  total_sent integer;
  total_delivered integer;
  total_opened integer;
  total_clicked integer;
  open_rate numeric;
  click_rate numeric;
BEGIN
  -- حساب الإحصائيات
  SELECT COUNT(*) INTO total_sent
  FROM notification_analytics
  WHERE tenant_id = tenant_id_param
    AND event_type = 'sent'
    AND timestamp BETWEEN start_date AND end_date;
  
  SELECT COUNT(*) INTO total_delivered
  FROM notification_analytics
  WHERE tenant_id = tenant_id_param
    AND event_type = 'delivered'
    AND timestamp BETWEEN start_date AND end_date;
  
  SELECT COUNT(*) INTO total_opened
  FROM notification_analytics
  WHERE tenant_id = tenant_id_param
    AND event_type = 'opened'
    AND timestamp BETWEEN start_date AND end_date;
  
  SELECT COUNT(*) INTO total_clicked
  FROM notification_analytics
  WHERE tenant_id = tenant_id_param
    AND event_type = 'clicked'
    AND timestamp BETWEEN start_date AND end_date;
  
  -- حساب المعدلات
  open_rate := CASE WHEN total_delivered > 0 THEN (total_opened::numeric / total_delivered::numeric) * 100 ELSE 0 END;
  click_rate := CASE WHEN total_opened > 0 THEN (total_clicked::numeric / total_opened::numeric) * 100 ELSE 0 END;
  
  -- بناء النتيجة
  result := jsonb_build_object(
    'total_sent', total_sent,
    'total_delivered', total_delivered,
    'total_opened', total_opened,
    'total_clicked', total_clicked,
    'open_rate', round(open_rate, 2),
    'click_rate', round(click_rate, 2),
    'delivery_rate', CASE WHEN total_sent > 0 THEN round((total_delivered::numeric / total_sent::numeric) * 100, 2) ELSE 0 END
  );
  
  RETURN result;
END;
$$;

-- وظيفة مشاركة قالب بين المدارس
CREATE OR REPLACE FUNCTION share_notification_template(
  template_id_param uuid,
  target_tenant_id_param uuid,
  message_param text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  share_id uuid;
  current_user_role text;
  template_tenant_id uuid;
BEGIN
  -- التحقق من صلاحيات المستخدم
  SELECT role INTO current_user_role FROM users WHERE id = auth.uid();
  
  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only admins can share templates between schools';
  END IF;
  
  -- التحقق من وجود القالب
  SELECT tenant_id INTO template_tenant_id FROM notification_templates WHERE id = template_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Template not found';
  END IF;
  
  -- إنشاء مشاركة جديدة
  INSERT INTO template_shares (
    template_id,
    shared_with_tenant_id,
    shared_by,
    message
  ) VALUES (
    template_id_param,
    target_tenant_id_param,
    auth.uid(),
    message_param
  ) RETURNING id INTO share_id;
  
  -- تحديث القالب ليكون قابل للمشاركة
  UPDATE notification_templates 
  SET is_shared = true, shared_by = auth.uid()
  WHERE id = template_id_param;
  
  RETURN share_id;
END;
$$;

-- تفعيل realtime للجداول الجديدة
alter publication supabase_realtime add table notification_templates;
alter publication supabase_realtime add table template_shares;
alter publication supabase_realtime add table notification_user_preferences;
alter publication supabase_realtime add table notification_schedules;
alter publication supabase_realtime add table notification_analytics;
alter publication supabase_realtime add table notification_bulk_actions;

-- إنشاء triggers للتحديث التلقائي
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة triggers للجداول
DROP TRIGGER IF EXISTS update_notification_templates_updated_at ON notification_templates;
CREATE TRIGGER update_notification_templates_updated_at
  BEFORE UPDATE ON notification_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_preferences_updated_at ON notification_user_preferences;
CREATE TRIGGER update_notification_preferences_updated_at
  BEFORE UPDATE ON notification_user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_schedules_updated_at ON notification_schedules;
CREATE TRIGGER update_notification_schedules_updated_at
  BEFORE UPDATE ON notification_schedules
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات تجريبية للقوالب
INSERT INTO notification_templates (tenant_id, name, type, title, message, variables, category, priority)
SELECT 
  t.id,
  'قالب وصول الحافلة',
  'geofence',
  'وصول الحافلة إلى {{stopName}}',
  'وصلت الحافلة رقم {{busNumber}} إلى محطة {{stopName}} في تمام الساعة {{time}}. الطالب {{studentName}} في الطريق.',
  ARRAY['busNumber', 'stopName', 'studentName', 'time'],
  'geofence',
  'normal'
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates nt 
  WHERE nt.tenant_id = t.id AND nt.name = 'قالب وصول الحافلة'
);

INSERT INTO notification_templates (tenant_id, name, type, title, message, variables, category, priority)
SELECT 
  t.id,
  'قالب تسجيل الحضور',
  'attendance',
  'تم تسجيل حضور {{studentName}}',
  'تم تسجيل {{attendanceType}} للطالب {{studentName}} في الحافلة رقم {{busNumber}} في تمام الساعة {{time}}.',
  ARRAY['studentName', 'attendanceType', 'busNumber', 'time'],
  'attendance',
  'normal'
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates nt 
  WHERE nt.tenant_id = t.id AND nt.name = 'قالب تسجيل الحضور'
);

INSERT INTO notification_templates (tenant_id, name, type, title, message, variables, category, priority)
SELECT 
  t.id,
  'قالب صيانة الحافلة',
  'maintenance',
  'تذكير صيانة الحافلة {{busNumber}}',
  'الحافلة رقم {{busNumber}} تحتاج إلى صيانة {{maintenanceType}} في تاريخ {{scheduledDate}}. التكلفة المتوقعة: {{cost}} ريال.',
  ARRAY['busNumber', 'maintenanceType', 'scheduledDate', 'cost'],
  'maintenance',
  'high'
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM notification_templates nt 
  WHERE nt.tenant_id = t.id AND nt.name = 'قالب صيانة الحافلة'
);

-- إدراج تفضيلات افتراضية للمستخدمين الموجودين
INSERT INTO notification_user_preferences (user_id, tenant_id, notification_types, delivery_channels, sound_settings)
SELECT 
  u.id,
  u.tenant_id,
  jsonb_build_object(
    'geofence', true,
    'attendance', true,
    'maintenance', CASE WHEN u.role IN ('admin', 'school_manager', 'supervisor') THEN true ELSE false END,
    'announcements', true,
    'emergency', true
  ),
  jsonb_build_object(
    'push', true,
    'email', CASE WHEN u.role IN ('admin', 'school_manager', 'parent') THEN true ELSE false END,
    'sms', false
  ),
  jsonb_build_object(
    'enabled', true,
    'sound', 'default',
    'volume', 0.8
  )
FROM users u
WHERE u.tenant_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM notification_user_preferences nup 
    WHERE nup.user_id = u.id AND nup.tenant_id = u.tenant_id
  );

-- تحديث الصلاحيات والسياسات النهائية
-- Final permissions and policies update

-- منح الصلاحيات للوظائف
GRANT EXECUTE ON FUNCTION create_notification_from_template TO authenticated;
GRANT EXECUTE ON FUNCTION process_bulk_notification_action TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION share_notification_template TO authenticated;

-- تأكيد تفعيل RLS على جميع الجداول
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_bulk_actions ENABLE ROW LEVEL SECURITY;

-- إضافة تعليقات للتوثيق
COMMENT ON TABLE notification_templates IS 'قوالب الإشعارات القابلة للإعادة الاستخدام';
COMMENT ON TABLE template_shares IS 'مشاركة قوالب الإشعارات بين المدارس';
COMMENT ON TABLE notification_user_preferences IS 'تفضيلات الإشعارات للمستخدمين';
COMMENT ON TABLE notification_schedules IS 'جدولة الإشعارات المستقبلية والمتكررة';
COMMENT ON TABLE notification_analytics IS 'تحليلات وإحصائيات الإشعارات';
COMMENT ON TABLE notification_bulk_actions IS 'الإجراءات المجمعة على الإشعارات';

COMMENT ON FUNCTION create_notification_from_template IS 'إنشاء إشعارات من قالب مع استبدال المتغيرات';
COMMENT ON FUNCTION process_bulk_notification_action IS 'معالجة الإجراءات المجمعة على الإشعارات';
COMMENT ON FUNCTION get_notification_analytics IS 'الحصول على إحصائيات وتحليلات الإشعارات';
COMMENT ON FUNCTION share_notification_template IS 'مشاركة قالب إشعار بين المدارس';
