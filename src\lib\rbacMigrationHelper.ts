/**
 * RBAC Migration Helper
 * Utilities to help migrate from hardcoded role checks to centralized RBAC
 */

import { UserRole } from "../types";
import { Permission, ResourceType, Action } from "./rbac";
import { ENHANCED_ROUTE_PERMISSIONS } from "./rbacCentralizedConfigEnhanced";

/**
 * Legacy role check patterns that should be replaced
 */
export const LEGACY_PATTERNS = {
  HARDCODED_ROLE_CHECKS: [
    'user?.role === "admin"',
    "user.role === UserRole.ADMIN",
    'userRole === "school_manager"',
    'role === "driver"',
  ],
  DIRECT_COMPARISONS: [
    "user?.role === UserRole.PARENT",
    "user?.role === UserRole.STUDENT",
    "user?.role === UserRole.SUPERVISOR",
  ],
  TERNARY_ROLE_CHECKS: [
    'user?.role === "admin" ? Component : null',
    "isAdmin ? AdminComponent : UserComponent",
  ],
};

/**
 * Migration mapping from legacy patterns to RBAC patterns
 */
export const MIGRATION_MAPPING = {
  // Route access patterns
  'user?.role === "admin" || user.tenant_id': {
    replacement: "canAccessRoute(route)",
    component: "EnhancedPermissionGuard",
    description: "Replace with route-based permission check",
  },

  // Component visibility patterns
  'user?.role === "admin" && <Component>': {
    replacement: "<PermissionGuard permission={Permission.SYSTEM_ADMIN}>",
    component: "PermissionGuard",
    description: "Replace with permission-based guard",
  },

  // Navigation patterns
  'user.role === UserRole.DRIVER ? "/dashboard/my-route"': {
    replacement: "getDefaultRouteForRole(user.role)",
    component: "RoleBasedRedirect",
    description: "Use centralized role-based routing",
  },
};

/**
 * Get default route for a user role
 */
export const getDefaultRouteForRole = (role: UserRole): string => {
  const defaultRoutes: Record<UserRole, string> = {
    [UserRole.ADMIN]: "/dashboard",
    [UserRole.SCHOOL_MANAGER]: "/dashboard",
    [UserRole.SUPERVISOR]: "/dashboard",
    [UserRole.DRIVER]: "/dashboard/my-route",
    [UserRole.PARENT]: "/dashboard/children",
    [UserRole.STUDENT]: "/dashboard/profile",
  };

  return defaultRoutes[role] || "/dashboard";
};

/**
 * Check if a route is accessible for a role
 */
export const isRouteAccessibleForRole = (
  route: string,
  role: UserRole,
): boolean => {
  const routeConfig = ENHANCED_ROUTE_PERMISSIONS[route];
  if (!routeConfig) return false;

  // If no permissions required, route is accessible
  if (routeConfig.permissions.length === 0) return true;

  // Check if role has any of the required permissions
  // This is a simplified check - in practice, use the full RBAC system
  const rolePermissions = getRolePermissions(role);
  return routeConfig.permissions.some((permission) =>
    rolePermissions.includes(permission),
  );
};

/**
 * Get permissions for a role (simplified version)
 */
const getRolePermissions = (role: UserRole): Permission[] => {
  // This should use the actual RBAC system
  // Simplified mapping for migration helper
  switch (role) {
    case UserRole.ADMIN:
      return Object.values(Permission);
    case UserRole.SCHOOL_MANAGER:
      return [
        Permission.SCHOOLS_VIEW_OWN,
        Permission.USERS_VIEW_TENANT,
        Permission.BUSES_VIEW_TENANT,
        Permission.STUDENTS_VIEW_TENANT,
        Permission.ROUTES_VIEW_TENANT,
        Permission.REPORTS_VIEW_TENANT,
      ];
    case UserRole.SUPERVISOR:
      return [
        Permission.USERS_VIEW_TENANT,
        Permission.BUSES_VIEW_TENANT,
        Permission.STUDENTS_VIEW_TENANT,
        Permission.STUDENTS_MANAGE_ATTENDANCE,
      ];
    case UserRole.DRIVER:
      return [
        Permission.BUSES_VIEW_ASSIGNED,
        Permission.ROUTES_VIEW_ASSIGNED,
        Permission.STUDENTS_MANAGE_ATTENDANCE,
        Permission.BUSES_TRACK,
      ];
    case UserRole.PARENT:
      return [
        Permission.STUDENTS_VIEW_CHILDREN,
        Permission.BUSES_TRACK,
        Permission.ROUTES_VIEW_ASSIGNED,
      ];
    case UserRole.STUDENT:
      return [Permission.USERS_VIEW_OWN, Permission.STUDENTS_VIEW_OWN];
    default:
      return [];
  }
};

/**
 * Migration checklist for developers
 */
export const MIGRATION_CHECKLIST = {
  PHASE_1: {
    title: "Remove Hardcoded Role Checks",
    tasks: [
      "Replace user?.role === 'admin' with Permission guards",
      "Update App.tsx route logic to use centralized config",
      "Convert Sidebar navigation to permission-based",
      "Update page components to use PermissionGuard",
    ],
  },
  PHASE_2: {
    title: "Implement Enhanced Guards",
    tasks: [
      "Replace PermissionGuard with EnhancedPermissionGuard where needed",
      "Add component keys to centralized configuration",
      "Implement audit logging for sensitive operations",
      "Add rate limiting to high-risk actions",
    ],
  },
  PHASE_3: {
    title: "Testing & Validation",
    tasks: [
      "Test all user roles can access appropriate features",
      "Verify tenant isolation is maintained",
      "Validate audit logs are generated correctly",
      "Performance test permission checking overhead",
    ],
  },
};

/**
 * Code scanning utility to find legacy patterns
 */
export const scanForLegacyPatterns = (
  codeContent: string,
): {
  pattern: string;
  line: number;
  suggestion: string;
}[] => {
  const findings: { pattern: string; line: number; suggestion: string }[] = [];
  const lines = codeContent.split("\n");

  lines.forEach((line, index) => {
    // Check for hardcoded role checks
    LEGACY_PATTERNS.HARDCODED_ROLE_CHECKS.forEach((pattern) => {
      if (line.includes(pattern)) {
        findings.push({
          pattern,
          line: index + 1,
          suggestion:
            "Replace with Permission-based check using PermissionGuard or hasPermission()",
        });
      }
    });

    // Check for direct role comparisons
    LEGACY_PATTERNS.DIRECT_COMPARISONS.forEach((pattern) => {
      if (line.includes(pattern)) {
        findings.push({
          pattern,
          line: index + 1,
          suggestion:
            "Use role-based permissions instead of direct role comparison",
        });
      }
    });
  });

  return findings;
};

export default {
  LEGACY_PATTERNS,
  MIGRATION_MAPPING,
  getDefaultRouteForRole,
  isRouteAccessibleForRole,
  MIGRATION_CHECKLIST,
  scanForLegacyPatterns,
};
