-- Add driver_performance table for Stage 5 (Reports)
CREATE TABLE IF NOT EXISTS driver_performance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  driver_id UUID NOT NULL REFERENCES users(id),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  date DATE NOT NULL,
  distance_traveled FLOAT,
  average_speed FLOAT,
  max_speed FLOAT,
  safety_score INT,
  on_time_percentage FLOAT,
  fuel_consumption FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add bus_utilization table for Stage 5 (Reports)
CREATE TABLE IF NOT EXISTS bus_utilization (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bus_id UUID NOT NULL REFERENCES buses(id),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  date DATE NOT NULL,
  total_trips INT,
  total_distance FLOAT,
  total_students INT,
  utilization_percentage FLOAT,
  fuel_consumption FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add route_delays table for Stage 5 (Reports)
CREATE TABLE IF NOT EXISTS route_delays (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  route_id UUID NOT NULL REFERENCES routes(id),
  bus_id UUID NOT NULL REFERENCES buses(id),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  date DATE NOT NULL,
  delay_minutes INT,
  reason TEXT,
  affected_stops TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add spare_parts table for Stage 6 (Maintenance)
CREATE TABLE IF NOT EXISTS spare_parts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  name TEXT NOT NULL,
  part_number TEXT,
  quantity INT NOT NULL DEFAULT 0,
  min_quantity INT DEFAULT 5,
  cost FLOAT,
  supplier TEXT,
  last_ordered_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add maintenance_parts junction table for Stage 6 (Maintenance)
CREATE TABLE IF NOT EXISTS maintenance_parts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  maintenance_id UUID NOT NULL REFERENCES bus_maintenance(id),
  part_id UUID NOT NULL REFERENCES spare_parts(id),
  quantity_used INT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add evaluations table for Stage 7 (Evaluation)
CREATE TABLE IF NOT EXISTS evaluations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  user_id UUID NOT NULL REFERENCES users(id),
  target_type TEXT NOT NULL, -- 'driver', 'route', 'service'
  target_id UUID NOT NULL,
  rating INT NOT NULL, -- 1-5 stars
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add complaints table for Stage 7 (Evaluation)
CREATE TABLE IF NOT EXISTS complaints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  user_id UUID NOT NULL REFERENCES users(id),
  type TEXT NOT NULL, -- 'driver', 'bus', 'route', 'service'
  target_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'in_progress', 'resolved', 'rejected'
  response TEXT,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable realtime for all new tables
alter publication supabase_realtime add table driver_performance;
alter publication supabase_realtime add table bus_utilization;
alter publication supabase_realtime add table route_delays;
alter publication supabase_realtime add table spare_parts;
alter publication supabase_realtime add table maintenance_parts;
alter publication supabase_realtime add table evaluations;
alter publication supabase_realtime add table complaints;
