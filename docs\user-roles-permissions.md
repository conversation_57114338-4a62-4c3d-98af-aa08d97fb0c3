# دليل أدوار المستخدمين والصلاحيات
## User Roles and Permissions Guide

**إصدار النظام:** 1.0  
**تاريخ التحديث:** 2025-01-25  
**نوع النظام:** Multi-tenant School Bus Management System  

---

## 📋 نظرة عامة | Overview

نظام إدارة الحافلات المدرسية يستخدم نظام **Role-Based Access Control (RBAC)** متقدم لضمان الأمان وتنظيم الصلاحيات. كل مستخدم له دور محدد يحدد ما يمكنه فعله في النظام.

### المبادئ الأساسية:
- **Multi-tenant:** كل مدرسة معزولة عن الأخرى
- **Principle of Least Privilege:** أقل صلاحيات ممكنة
- **Data Isolation:** عزل البيانات بين المدارس
- **Audit Trail:** تسجيل جميع العمليات

---

## 👥 الأدوار المعرفة | Defined Roles

### 1. 🔧 مدير النظام العام (Admin)
**الوصف:** المدير العام للنظام بأكمله، له صلاحيات كاملة على جميع المدارس والبيانات.

#### الصلاحيات الرئيسية:
- **إدارة النظام:**
  - إعدادات النظام العامة
  - إدارة النسخ الاحتياطية
  - مراقبة الأداء والأمان
  - إدارة الفواتير والاشتراكات

- **إدارة المدارس:**
  - إنشاء وحذف المدارس
  - تعديل إعدادات المدارس
  - عرض جميع بيانات المدارس
  - إدارة اشتراكات المدارس

- **إدارة المستخدمين:**
  - إنشاء وحذف جميع المستخدمين
  - تعيين الأدوار والصلاحيات
  - عرض جميع ملفات المستخدمين
  - إعادة تعيين كلمات المرور

- **البيانات والتقارير:**
  - الوصول لجميع البيانات
  - تقارير شاملة عبر المدارس
  - تحليلات النظام العامة
  - تصدير البيانات

#### نطاق البيانات:
- **GLOBAL:** الوصول لجميع البيانات في النظام
- **ALL_TENANTS:** جميع المدارس
- **SYSTEM_LEVEL:** بيانات النظام والإعدادات

---

### 2. 🏫 مدير المدرسة (School Manager)
**الوصف:** مدير مدرسة واحدة، له صلاحيات كاملة داخل مدرسته فقط.

#### الصلاحيات الرئيسية:
- **إدارة المدرسة:**
  - تعديل معلومات المدرسة
  - إدارة إعدادات المدرسة
  - رفع شعار المدرسة
  - إدارة معلومات الاتصال

- **إدارة المستخدمين:**
  - إنشاء مستخدمين في مدرسته
  - تعديل بيانات المستخدمين
  - تعيين أدوار (عدا admin)
  - إلغاء تفعيل المستخدمين

- **إدارة الحافلات:**
  - إضافة وتعديل الحافلات
  - تعيين السائقين
  - مراقبة مواقع الحافلات
  - إدارة صيانة الحافلات

- **إدارة الطلاب:**
  - إضافة وتعديل بيانات الطلاب
  - تعيين الطلاب للمسارات
  - إدارة ملفات الطلاب
  - متابعة حضور الطلاب

- **التقارير والتحليلات:**
  - تقارير مدرسته فقط
  - إحصائيات الحضور
  - تقارير الحافلات
  - تحليلات الأداء

#### نطاق البيانات:
- **TENANT:** محدود بمدرسته فقط
- **SCHOOL_LEVEL:** جميع بيانات المدرسة

#### القيود:
- لا يمكنه رؤية بيانات المدارس الأخرى
- لا يمكنه تعيين دور admin
- لا يمكنه الوصول لإعدادات النظام العامة

---

### 3. 👁️ المشرف (Supervisor)
**الوصف:** مشرف على العمليات، له صلاحيات مراقبة ومتابعة فقط.

#### الصلاحيات الرئيسية:
- **المراقبة والمتابعة:**
  - عرض جميع بيانات المدرسة
  - مراقبة مواقع الحافلات
  - متابعة حضور الطلاب
  - مراجعة التقارير

- **التقارير:**
  - عرض التقارير الجاهزة
  - تصدير التقارير
  - إحصائيات الأداء
  - تقارير الحضور

#### نطاق البيانات:
- **TENANT:** محدود بمدرسته
- **READ_ONLY:** قراءة فقط

#### القيود:
- لا يمكنه تعديل أي بيانات
- لا يمكنه إنشاء أو حذف المستخدمين
- لا يمكنه إدارة الحافلات أو الطلاب

---

### 4. 🚌 السائق (Driver)
**الوصف:** سائق حافلة، له صلاحيات محدودة بحافلته ومسارها.

#### الصلاحيات الرئيسية:
- **إدارة الحافلة:**
  - تحديث موقع الحافلة
  - تسجيل حالة الحافلة
  - تحديث معلومات الرحلة
  - تسجيل مشاكل الحافلة

- **إدارة الطلاب:**
  - تسجيل حضور الطلاب
  - تسجيل غياب الطلاب
  - عرض قائمة طلاب المسار
  - تسجيل ملاحظات الحضور

- **التواصل:**
  - إرسال إشعارات للأولياء
  - تسجيل الحوادث
  - التواصل مع إدارة المدرسة
  - تحديث حالة الرحلة

#### نطاق البيانات:
- **ASSIGNED:** محدود بحافلته ومسارها
- **BUS_LEVEL:** بيانات الحافلة والطلاب المخصصين

#### القيود:
- لا يمكنه رؤية حافلات أخرى
- لا يمكنه تعديل بيانات الطلاب الشخصية
- لا يمكنه الوصول للتقارير العامة

---

### 5. 👨‍👩‍👧‍👦 ولي الأمر (Parent)
**الوصف:** ولي أمر طالب، له صلاحيات محدودة بأطفاله فقط.

#### الصلاحيات الرئيسية:
- **متابعة الأطفال:**
  - عرض بيانات أطفاله
  - متابعة حضور الأطفال
  - عرض درجات وتقييمات الأطفال
  - متابعة سلوك الأطفال

- **متابعة النقل:**
  - مراقبة موقع حافلة الطفل
  - استقبال إشعارات الوصول
  - عرض مسار الحافلة
  - تسجيل غياب الطفل

- **التواصل:**
  - استقبال الإشعارات
  - التواصل مع السائق
  - التواصل مع إدارة المدرسة
  - تسجيل الشكاوى

#### نطاق البيانات:
- **CHILDREN:** محدود بأطفاله فقط
- **FAMILY_LEVEL:** بيانات العائلة

#### القيود:
- لا يمكنه رؤية بيانات أطفال آخرين
- لا يمكنه تعديل بيانات المدرسة
- لا يمكنه الوصول للتقارير العامة

---

### 6. 🎓 الطالب (Student)
**الوصف:** طالب في المدرسة، له صلاحيات محدودة جداً.

#### الصلاحيات الرئيسية:
- **البيانات الشخصية:**
  - عرض بياناته الشخصية
  - عرض جدوله الدراسي
  - عرض درجاته وتقييماته
  - تحديث بعض البيانات الشخصية

- **النقل:**
  - عرض معلومات حافلته
  - عرض مسار الحافلة
  - عرض مواعيد الحافلة
  - تسجيل الحضور الذاتي

#### نطاق البيانات:
- **PERSONAL:** بياناته الشخصية فقط
- **STUDENT_LEVEL:** معلومات الطالب

#### القيود:
- لا يمكنه رؤية بيانات طلاب آخرين
- لا يمكنه تعديل معظم البيانات
- لا يمكنه الوصول لأي تقارير

---

## 🔐 مصفوفة الصلاحيات | Permissions Matrix

### الموارد الرئيسية:
1. **SCHOOLS** - المدارس
2. **USERS** - المستخدمين
3. **BUSES** - الحافلات
4. **ROUTES** - المسارات
5. **STUDENTS** - الطلاب
6. **ATTENDANCE** - الحضور
7. **NOTIFICATIONS** - الإشعارات
8. **REPORTS** - التقارير

### العمليات الأساسية:
- **CREATE** - إنشاء
- **READ** - قراءة
- **UPDATE** - تحديث
- **DELETE** - حذف
- **ASSIGN** - تعيين
- **APPROVE** - موافقة
- **EXPORT** - تصدير
- **IMPORT** - استيراد

### جدول الصلاحيات:

| المورد | Admin | School Manager | Supervisor | Driver | Parent | Student |
|---------|-------|----------------|------------|--------|--------|---------|
| SCHOOLS | CRUD | RU (own) | R (own) | - | - | - |
| USERS | CRUD | CRU (tenant) | R (tenant) | R (assigned) | R (children) | R (self) |
| BUSES | CRUD | CRUD (tenant) | R (tenant) | RU (assigned) | R (children) | R (assigned) |
| ROUTES | CRUD | CRUD (tenant) | R (tenant) | R (assigned) | R (children) | R (assigned) |
| STUDENTS | CRUD | CRUD (tenant) | R (tenant) | RU (assigned) | R (children) | R (self) |
| ATTENDANCE | CRUD | CRU (tenant) | R (tenant) | CR (assigned) | R (children) | R (self) |
| NOTIFICATIONS | CRUD | CR (tenant) | R (tenant) | CR (assigned) | R (received) | R (received) |
| REPORTS | CRUD | R (tenant) | R (tenant) | R (assigned) | R (children) | - |

**الرموز:**
- C = Create (إنشاء)
- R = Read (قراءة)  
- U = Update (تحديث)
- D = Delete (حذف)
- (-) = غير مسموح

---

## 🛡️ قواعد الأمان | Security Rules

### 1. عزل البيانات (Data Isolation):
- كل مدرسة معزولة تماماً عن الأخرى
- المستخدمون لا يمكنهم رؤية بيانات مدارس أخرى
- الأدمن فقط له وصول عبر المدارس

### 2. مبدأ أقل الصلاحيات:
- كل مستخدم له أقل صلاحيات ممكنة لأداء مهامه
- لا توجد صلاحيات زائدة أو غير ضرورية
- مراجعة دورية للصلاحيات

### 3. التدقيق والمراقبة:
- تسجيل جميع العمليات الحساسة
- مراقبة محاولات الوصول غير المصرح
- تنبيهات أمنية فورية

### 4. التحقق المتعدد:
- التحقق من الهوية والدور
- التحقق من نطاق البيانات
- التحقق من الصلاحيات المطلوبة

---

## 📊 إحصائيات الصلاحيات | Permissions Statistics

### عدد الصلاحيات لكل دور:
- **Admin:** 52 صلاحية
- **School Manager:** 38 صلاحية  
- **Supervisor:** 22 صلاحية
- **Driver:** 16 صلاحية
- **Parent:** 12 صلاحية
- **Student:** 8 صلاحيات

### توزيع الصلاحيات:
- **صلاحيات النظام:** 8 (admin فقط)
- **صلاحيات المدرسة:** 15 (admin, school_manager)
- **صلاحيات المستخدمين:** 12 (admin, school_manager, supervisor)
- **صلاحيات الحافلات:** 10 (admin, school_manager, driver)
- **صلاحيات الطلاب:** 8 (admin, school_manager, parent)
- **صلاحيات التقارير:** 6 (admin, school_manager, supervisor)

---

**تم إعداد هذا الدليل بواسطة:** Augment Agent  
**تاريخ الإعداد:** 2025-01-25  
**حالة التطبيق:** مصمم ومجهز للتطبيق  
**المرحلة التالية:** تطبيق الهجرات وتفعيل النظام
