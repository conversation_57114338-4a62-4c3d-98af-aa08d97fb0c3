/**
 * Enhanced Centralized RBAC Configuration
 * Single source of truth for all permission mappings and access control rules
 * Addresses critical audit findings and provides scalable permission management
 */

import { UserRole } from "../types";
import { Permission, DataScope, ResourceType, Action } from "./rbac";

/**
 * Route Permission Configuration
 * Maps application routes to required permissions with enhanced metadata
 */
export interface RoutePermissionConfig {
  permissions: Permission[];
  requireAll?: boolean;
  fallbackRoute?: string;
  description: string;
  dataScope?: DataScope;
  auditLevel?: "low" | "medium" | "high" | "critical";
  rateLimit?: {
    maxRequests: number;
    windowMinutes: number;
  };
}

export const ENHANCED_ROUTE_PERMISSIONS: Record<string, RoutePermissionConfig> =
  {
    "/dashboard": {
      permissions: [],
      description: "Main dashboard - accessible to all authenticated users",
      auditLevel: "low",
    },
    "/dashboard/schools": {
      permissions: [Permission.SCHOOLS_VIEW_ALL],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "School management - admin only",
      dataScope: DataScope.GLOBAL,
      auditLevel: "high",
      rateLimit: { maxRequests: 100, windowMinutes: 1 },
    },
    "/dashboard/users": {
      permissions: [Permission.USERS_VIEW_ALL, Permission.USERS_VIEW_TENANT],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "User management - admin and school managers",
      dataScope: DataScope.TENANT,
      auditLevel: "critical",
      rateLimit: { maxRequests: 50, windowMinutes: 1 },
    },
    "/dashboard/buses": {
      permissions: [
        Permission.BUSES_VIEW_ALL,
        Permission.BUSES_VIEW_TENANT,
        Permission.BUSES_VIEW_ASSIGNED,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Bus management - varies by role",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "medium",
    },
    "/dashboard/routes": {
      permissions: [
        Permission.ROUTES_VIEW_ALL,
        Permission.ROUTES_VIEW_TENANT,
        Permission.ROUTES_VIEW_ASSIGNED,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Route management - varies by role",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "medium",
    },
    "/dashboard/students": {
      permissions: [
        Permission.STUDENTS_VIEW_ALL,
        Permission.STUDENTS_VIEW_TENANT,
        Permission.STUDENTS_VIEW_CHILDREN,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Student management - varies by role",
      dataScope: DataScope.CHILDREN,
      auditLevel: "high",
    },
    "/dashboard/children": {
      permissions: [Permission.STUDENTS_VIEW_CHILDREN],
      requireAll: true,
      fallbackRoute: "/dashboard",
      description: "Parent view of their children",
      dataScope: DataScope.CHILDREN,
      auditLevel: "high",
    },
    "/dashboard/attendance": {
      permissions: [
        Permission.STUDENTS_MANAGE_ATTENDANCE,
        Permission.STUDENTS_VIEW_ATTENDANCE,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Attendance management",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "critical",
    },
    "/dashboard/driver-attendance": {
      permissions: [Permission.STUDENTS_MANAGE_ATTENDANCE],
      requireAll: true,
      fallbackRoute: "/dashboard",
      description: "Driver attendance interface",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "critical",
    },
    "/dashboard/my-route": {
      permissions: [Permission.ROUTES_VIEW_ASSIGNED],
      requireAll: true,
      fallbackRoute: "/dashboard",
      description: "Driver route view",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "medium",
    },
    "/dashboard/tracking": {
      permissions: [Permission.BUSES_TRACK],
      requireAll: true,
      fallbackRoute: "/dashboard",
      description: "Bus tracking",
      dataScope: DataScope.ASSIGNED,
      auditLevel: "medium",
    },
    "/dashboard/reports": {
      permissions: [
        Permission.REPORTS_VIEW_SYSTEM,
        Permission.REPORTS_VIEW_TENANT,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Reports and analytics",
      dataScope: DataScope.TENANT,
      auditLevel: "critical",
      rateLimit: { maxRequests: 20, windowMinutes: 1 },
    },
    "/dashboard/notifications": {
      permissions: [
        Permission.NOTIFICATIONS_VIEW_ALL,
        Permission.NOTIFICATIONS_SEND_SYSTEM,
        Permission.NOTIFICATIONS_SEND_TENANT,
      ],
      requireAll: false,
      fallbackRoute: "/dashboard",
      description: "Notification management",
      dataScope: DataScope.TENANT,
      auditLevel: "high",
    },
    "/dashboard/evaluation": {
      permissions: [],
      description: "Evaluation system",
      auditLevel: "low",
    },
    "/dashboard/profile": {
      permissions: [],
      description: "User profile management",
      dataScope: DataScope.PERSONAL,
      auditLevel: "low",
    },
    "/dashboard/settings": {
      permissions: [],
      description: "User settings",
      dataScope: DataScope.PERSONAL,
      auditLevel: "low",
    },
  };

/**
 * Component Permission Configuration
 * Maps UI components to required permissions with enhanced security metadata
 */
export interface ComponentPermissionConfig {
  permissions: Permission[];
  roles?: UserRole[];
  dataScopes?: DataScope[];
  description: string;
  securityLevel: "low" | "medium" | "high" | "critical";
  auditRequired: boolean;
  rateLimit?: {
    maxRequests: number;
    windowMinutes: number;
  };
}

export const ENHANCED_COMPONENT_PERMISSIONS: Record<
  string,
  ComponentPermissionConfig
> = {
  "SchoolModal.create": {
    permissions: [Permission.SCHOOLS_CREATE],
    roles: [UserRole.ADMIN],
    description: "Create new school",
    securityLevel: "critical",
    auditRequired: true,
    rateLimit: { maxRequests: 5, windowMinutes: 1 },
  },
  "SchoolModal.edit": {
    permissions: [Permission.SCHOOLS_UPDATE],
    roles: [UserRole.ADMIN],
    description: "Edit school details",
    securityLevel: "high",
    auditRequired: true,
  },
  "UserModal.create": {
    permissions: [Permission.USERS_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new user",
    securityLevel: "critical",
    auditRequired: true,
    rateLimit: { maxRequests: 10, windowMinutes: 1 },
  },
  "UserModal.edit": {
    permissions: [
      Permission.USERS_UPDATE_ALL,
      Permission.USERS_UPDATE_TENANT,
      Permission.USERS_UPDATE_OWN,
    ],
    description: "Edit user details",
    securityLevel: "high",
    auditRequired: true,
  },
  "UserModal.delete": {
    permissions: [Permission.USERS_DELETE_ALL, Permission.USERS_DELETE_TENANT],
    description: "Delete user",
    securityLevel: "critical",
    auditRequired: true,
    rateLimit: { maxRequests: 3, windowMinutes: 1 },
  },
  "BusModal.create": {
    permissions: [Permission.BUSES_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new bus",
    securityLevel: "medium",
    auditRequired: true,
  },
  "StudentModal.create": {
    permissions: [Permission.STUDENTS_CREATE],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Create new student",
    securityLevel: "medium",
    auditRequired: true,
  },
  AttendanceForm: {
    permissions: [Permission.STUDENTS_MANAGE_ATTENDANCE],
    roles: [
      UserRole.DRIVER,
      UserRole.SUPERVISOR,
      UserRole.SCHOOL_MANAGER,
      UserRole.ADMIN,
    ],
    description: "Record attendance",
    securityLevel: "critical",
    auditRequired: true,
  },
  ReportExport: {
    permissions: [Permission.REPORTS_EXPORT],
    roles: [UserRole.ADMIN, UserRole.SCHOOL_MANAGER],
    description: "Export reports",
    securityLevel: "critical",
    auditRequired: true,
    rateLimit: { maxRequests: 5, windowMinutes: 5 },
  },
  NotificationTemplates: {
    permissions: [Permission.NOTIFICATIONS_MANAGE_TEMPLATES],
    roles: [UserRole.ADMIN],
    description: "Manage notification templates",
    securityLevel: "high",
    auditRequired: true,
  },
};

/**
 * Enhanced Data Access Patterns
 * Defines comprehensive data access rules with security controls
 */
export interface EnhancedDataAccessPattern {
  defaultScope: DataScope;
  allowedScopes: DataScope[];
  restrictions: {
    tenantBound: boolean;
    personalOnly: boolean;
    assignedOnly: boolean;
    childrenOnly: boolean;
    departmentBound?: boolean;
    routeBound?: boolean;
    busBound?: boolean;
  };
  securityLevel: "low" | "medium" | "high" | "critical";
  auditLevel: "basic" | "detailed" | "comprehensive";
  dataRetention?: {
    days: number;
    autoDelete: boolean;
  };
}

export const ENHANCED_DATA_ACCESS_PATTERNS: Record<
  UserRole,
  EnhancedDataAccessPattern
> = {
  [UserRole.ADMIN]: {
    defaultScope: DataScope.GLOBAL,
    allowedScopes: [
      DataScope.GLOBAL,
      DataScope.TENANT,
      DataScope.PERSONAL,
      DataScope.ASSIGNED,
      DataScope.CHILDREN,
    ],
    restrictions: {
      tenantBound: false,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
    },
    securityLevel: "critical",
    auditLevel: "comprehensive",
  },
  [UserRole.SCHOOL_MANAGER]: {
    defaultScope: DataScope.TENANT,
    allowedScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
      departmentBound: false,
    },
    securityLevel: "high",
    auditLevel: "detailed",
  },
  [UserRole.SUPERVISOR]: {
    defaultScope: DataScope.TENANT,
    allowedScopes: [DataScope.TENANT, DataScope.PERSONAL, DataScope.ASSIGNED],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: false,
      departmentBound: true,
    },
    securityLevel: "medium",
    auditLevel: "detailed",
  },
  [UserRole.DRIVER]: {
    defaultScope: DataScope.ASSIGNED,
    allowedScopes: [DataScope.ASSIGNED, DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: true,
      childrenOnly: false,
      routeBound: true,
      busBound: true,
    },
    securityLevel: "medium",
    auditLevel: "detailed",
    dataRetention: { days: 90, autoDelete: true },
  },
  [UserRole.PARENT]: {
    defaultScope: DataScope.CHILDREN,
    allowedScopes: [DataScope.CHILDREN, DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: false,
      assignedOnly: false,
      childrenOnly: true,
    },
    securityLevel: "high",
    auditLevel: "detailed",
    dataRetention: { days: 365, autoDelete: false },
  },
  [UserRole.STUDENT]: {
    defaultScope: DataScope.PERSONAL,
    allowedScopes: [DataScope.PERSONAL],
    restrictions: {
      tenantBound: true,
      personalOnly: true,
      assignedOnly: false,
      childrenOnly: false,
    },
    securityLevel: "low",
    auditLevel: "basic",
    dataRetention: { days: 180, autoDelete: true },
  },
};

/**
 * Enhanced Action Permission Matrix
 * Comprehensive mapping of actions to permissions with security metadata
 */
export interface ActionPermissionConfig {
  permissions: Permission[];
  securityLevel: "low" | "medium" | "high" | "critical";
  auditRequired: boolean;
  rateLimit?: {
    maxRequests: number;
    windowMinutes: number;
  };
  requiresMFA?: boolean;
  ipRestrictions?: boolean;
}

export const ENHANCED_ACTION_PERMISSION_MATRIX: Record<
  ResourceType,
  Record<Action, ActionPermissionConfig>
> = {
  [ResourceType.SCHOOL]: {
    [Action.READ]: {
      permissions: [Permission.SCHOOLS_VIEW_ALL, Permission.SCHOOLS_VIEW_OWN],
      securityLevel: "medium",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [Permission.SCHOOLS_CREATE],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 5, windowMinutes: 60 },
      requiresMFA: true,
    },
    [Action.UPDATE]: {
      permissions: [Permission.SCHOOLS_UPDATE],
      securityLevel: "high",
      auditRequired: true,
      requiresMFA: true,
    },
    [Action.DELETE]: {
      permissions: [Permission.SCHOOLS_DELETE],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 2, windowMinutes: 60 },
      requiresMFA: true,
      ipRestrictions: true,
    },
  },
  [ResourceType.USER]: {
    [Action.READ]: {
      permissions: [
        Permission.USERS_VIEW_ALL,
        Permission.USERS_VIEW_TENANT,
        Permission.USERS_VIEW_OWN,
      ],
      securityLevel: "medium",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [Permission.USERS_CREATE],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 10, windowMinutes: 60 },
    },
    [Action.UPDATE]: {
      permissions: [
        Permission.USERS_UPDATE_ALL,
        Permission.USERS_UPDATE_TENANT,
        Permission.USERS_UPDATE_OWN,
      ],
      securityLevel: "high",
      auditRequired: true,
    },
    [Action.DELETE]: {
      permissions: [
        Permission.USERS_DELETE_ALL,
        Permission.USERS_DELETE_TENANT,
      ],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 5, windowMinutes: 60 },
      requiresMFA: true,
    },
  },
  [ResourceType.BUS]: {
    [Action.READ]: {
      permissions: [
        Permission.BUSES_VIEW_ALL,
        Permission.BUSES_VIEW_TENANT,
        Permission.BUSES_VIEW_ASSIGNED,
      ],
      securityLevel: "low",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [Permission.BUSES_CREATE],
      securityLevel: "medium",
      auditRequired: true,
    },
    [Action.UPDATE]: {
      permissions: [Permission.BUSES_UPDATE],
      securityLevel: "medium",
      auditRequired: true,
    },
    [Action.DELETE]: {
      permissions: [Permission.BUSES_DELETE],
      securityLevel: "high",
      auditRequired: true,
      rateLimit: { maxRequests: 5, windowMinutes: 60 },
    },
    [Action.TRACK]: {
      permissions: [Permission.BUSES_TRACK],
      securityLevel: "medium",
      auditRequired: false,
    },
  },
  [ResourceType.STUDENT]: {
    [Action.READ]: {
      permissions: [
        Permission.STUDENTS_VIEW_ALL,
        Permission.STUDENTS_VIEW_TENANT,
        Permission.STUDENTS_VIEW_CHILDREN,
      ],
      securityLevel: "high",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [Permission.STUDENTS_CREATE],
      securityLevel: "high",
      auditRequired: true,
    },
    [Action.UPDATE]: {
      permissions: [Permission.STUDENTS_UPDATE],
      securityLevel: "high",
      auditRequired: true,
    },
    [Action.DELETE]: {
      permissions: [Permission.STUDENTS_DELETE],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 3, windowMinutes: 60 },
      requiresMFA: true,
    },
  },
  [ResourceType.ATTENDANCE]: {
    [Action.READ]: {
      permissions: [Permission.STUDENTS_VIEW_ATTENDANCE],
      securityLevel: "high",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [Permission.STUDENTS_MANAGE_ATTENDANCE],
      securityLevel: "critical",
      auditRequired: true,
    },
    [Action.UPDATE]: {
      permissions: [Permission.STUDENTS_MANAGE_ATTENDANCE],
      securityLevel: "critical",
      auditRequired: true,
    },
  },
  [ResourceType.REPORT]: {
    [Action.READ]: {
      permissions: [
        Permission.REPORTS_VIEW_SYSTEM,
        Permission.REPORTS_VIEW_TENANT,
      ],
      securityLevel: "high",
      auditRequired: true,
    },
    [Action.EXPORT]: {
      permissions: [Permission.REPORTS_EXPORT],
      securityLevel: "critical",
      auditRequired: true,
      rateLimit: { maxRequests: 5, windowMinutes: 60 },
    },
    [Action.SCHEDULE]: {
      permissions: [Permission.REPORTS_SCHEDULE],
      securityLevel: "medium",
      auditRequired: true,
    },
  },
  [ResourceType.NOTIFICATION]: {
    [Action.READ]: {
      permissions: [Permission.NOTIFICATIONS_VIEW_ALL],
      securityLevel: "low",
      auditRequired: false,
    },
    [Action.CREATE]: {
      permissions: [
        Permission.NOTIFICATIONS_SEND_SYSTEM,
        Permission.NOTIFICATIONS_SEND_TENANT,
      ],
      securityLevel: "high",
      auditRequired: true,
      rateLimit: { maxRequests: 20, windowMinutes: 60 },
    },
  },
};

/**
 * Security Policy Configuration
 */
export interface SecurityPolicyConfig {
  sessionTimeout: number; // minutes
  maxFailedAttempts: number;
  lockoutDuration: number; // minutes
  requireMFA: boolean;
  ipWhitelist?: string[];
  allowedTimeWindows?: {
    start: string; // HH:MM
    end: string; // HH:MM
    days: number[]; // 0-6 (Sunday-Saturday)
  }[];
}

export const SECURITY_POLICIES: Record<UserRole, SecurityPolicyConfig> = {
  [UserRole.ADMIN]: {
    sessionTimeout: 480, // 8 hours
    maxFailedAttempts: 3,
    lockoutDuration: 30,
    requireMFA: true,
  },
  [UserRole.SCHOOL_MANAGER]: {
    sessionTimeout: 360, // 6 hours
    maxFailedAttempts: 5,
    lockoutDuration: 15,
    requireMFA: true,
  },
  [UserRole.SUPERVISOR]: {
    sessionTimeout: 240, // 4 hours
    maxFailedAttempts: 5,
    lockoutDuration: 10,
    requireMFA: false,
  },
  [UserRole.DRIVER]: {
    sessionTimeout: 480, // 8 hours (long shifts)
    maxFailedAttempts: 5,
    lockoutDuration: 10,
    requireMFA: false,
    allowedTimeWindows: [
      {
        start: "05:00",
        end: "20:00",
        days: [1, 2, 3, 4, 5], // Monday to Friday
      },
    ],
  },
  [UserRole.PARENT]: {
    sessionTimeout: 120, // 2 hours
    maxFailedAttempts: 5,
    lockoutDuration: 5,
    requireMFA: false,
  },
  [UserRole.STUDENT]: {
    sessionTimeout: 60, // 1 hour
    maxFailedAttempts: 3,
    lockoutDuration: 5,
    requireMFA: false,
    allowedTimeWindows: [
      {
        start: "06:00",
        end: "18:00",
        days: [1, 2, 3, 4, 5], // Monday to Friday
      },
    ],
  },
};

/**
 * Error Messages Configuration
 */
export const ENHANCED_PERMISSION_ERROR_MESSAGES: Record<string, string> = {
  ROUTE_ACCESS_DENIED:
    "You do not have permission to access this page. Please contact your administrator if you believe this is an error.",
  COMPONENT_ACCESS_DENIED:
    "You do not have permission to perform this action. Your role does not include the required permissions.",
  DATA_ACCESS_DENIED:
    "You do not have permission to view this data. Access is restricted based on your role and data scope.",
  FEATURE_DISABLED:
    "This feature is not available for your role or has been disabled for your organization.",
  TENANT_MISMATCH:
    "You can only access data from your organization. Cross-tenant access is not permitted.",
  INSUFFICIENT_PERMISSIONS:
    "Your current role does not have sufficient permissions to perform this action.",
  SESSION_EXPIRED: "Your session has expired. Please log in again to continue.",
  RATE_LIMIT_EXCEEDED: "Too many requests. Please wait before trying again.",
  MFA_REQUIRED: "Multi-factor authentication is required for this action.",
  IP_RESTRICTED: "Access from your current location is not permitted.",
  TIME_RESTRICTED: "Access is only allowed during specified hours.",
  ACCOUNT_LOCKED:
    "Your account has been temporarily locked due to multiple failed attempts.",
};

export default {
  ENHANCED_ROUTE_PERMISSIONS,
  ENHANCED_COMPONENT_PERMISSIONS,
  ENHANCED_DATA_ACCESS_PATTERNS,
  ENHANCED_ACTION_PERMISSION_MATRIX,
  SECURITY_POLICIES,
  ENHANCED_PERMISSION_ERROR_MESSAGES,
};
